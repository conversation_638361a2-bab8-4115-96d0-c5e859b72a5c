.build-image:kaniko:
  stage: build
  image:
    name: cr.d.xiaomi.net/containercloud/kaniko-executor-xiaomi:release-latest
    entrypoint: [ "" ]
  script:
    - mkdir -p /kaniko/.docker/
    - echo "{\"auths\":{\"${CI_REGISTRY}\":{\"username\":\"${CI_REGISTRY_USER}\",\"password\":\"${CI_REGISTRY_PASSWORD}\"}}}" > /kaniko/.docker/config.json
    - echo "generate docker image $IMAGE"
    - |
      /kaniko/executor \
      --context ${CI_PROJECT_DIR} \
      --dockerfile $CI_PROJECT_DIR/Dockerfile \
      --build-arg ENV=${PROFILE_NAME} \
      --build-arg APP=${SERVICE_NAME} \
      --destination ${IMAGE}
  after_script:
    - echo "APP=${SERVICE_NAME} ENV=${PROFILE_NAME}"
    - echo "build completed"