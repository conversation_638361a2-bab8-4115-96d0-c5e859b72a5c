build:test:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: test
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^test-.*$/

build:pre:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: pre
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^pre-.*$/
    - /^hotfix-.*$/

build:prod:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: prod
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^pro-.*$/