# 统一Topic实现说明

## 概述

本项目实现了从Talos到RocketMQ的统一Topic过渡方案，支持平滑迁移和灰度发布。

## 核心组件

### 1. UnifiedTopicManager
- **位置**: `src/main/java/com/mioffice/ums/engine/manager/UnifiedTopicManager.java`
- **功能**: 管理统一Topic的配置和系统ID白名单
- **主要方法**:
  - `isUnifiedTopicEnabled()`: 检查统一Topic功能是否启用
  - `shouldUseUnifiedTopic(String systemId)`: 判断系统ID是否应使用统一Topic
  - `getUnifiedTopicName()`: 获取统一Topic名称
  - `getUnifiedTopicSystemIds()`: 获取白名单系统ID集合

### 2. UniversalMessageConsumer
- **位置**: `src/main/java/com/mioffice/ums/engine/rocketmq/UniversalMessageConsumer.java`
- **功能**: 通用消息消费者，处理来自所有RocketMQ Topic的消息
- **特性**:
  - 支持并发消息处理
  - 集成限流机制
  - 支持多渠道消息发送（MiWork、Email、SMS）
  - 完善的错误处理和重试机制

### 3. ProducerAndConsumerManager (重构)
- **位置**: `src/main/java/com/mioffice/ums/engine/rocketmq/ProducerAndConsumerManager.java`
- **改进**:
  - 使用通用生产者发送所有消息
  - 统一消费者处理逻辑
  - 支持统一Topic和专属队列
  - 简化了生产者和消费者的创建逻辑

### 4. 消息路由逻辑
- **位置**: `src/main/java/com/mioffice/ums/engine/service/grpc/server/MessageRpcService.java`
- **路由策略**:
  1. 优先检查是否使用统一Topic（基于系统ID白名单）
  2. 检查系统级专属队列
  3. 检查机器人级专属队列
  4. 默认使用Talos公共队列

### 5. 重试任务
- **RetryUnifiedTopicFailMessageTask**: 重试统一Topic中的失败消息
- **RetryUnifiedTopicSendingMessageTask**: 重试统一Topic中的发送中消息
- **集成到MessageServiceImpl**: 通过任务类执行重试逻辑

## 配置说明

### Nacos配置示例
```yaml
ums:
  engine:
    unified-topic:
      enabled: true  # 启用统一Topic功能
      topic-name: "ums-unified-topic"  # 统一Topic名称
      system-ids: "sys001,sys002,sys003"  # 白名单系统ID，逗号分隔

rocketmq:
  name-server: "localhost:9876"
  unified-topic:
    producer-group: "ums-unified-producer-group"
    consumer-group: "ums-unified-consumer-group"
```

### 测试配置
- **文件**: `src/main/resources/application-unified-topic-test.yml`
- **用途**: 本地测试和开发环境配置

## 部署和迁移策略

### 阶段1: 准备阶段
1. 部署包含统一Topic功能的新版本
2. 配置中保持 `unified-topic.enabled: false`
3. 验证系统正常运行

### 阶段2: 灰度测试
1. 启用统一Topic功能: `unified-topic.enabled: true`
2. 配置少量测试系统ID到白名单
3. 监控消息发送和消费情况
4. 验证重试机制正常工作

### 阶段3: 逐步迁移
1. 逐步增加白名单中的系统ID
2. 监控系统性能和消息处理情况
3. 根据需要调整配置参数

### 阶段4: 完全迁移
1. 将所有系统ID加入白名单
2. 验证所有消息都通过RocketMQ处理
3. 准备下线Talos相关代码

## 监控和日志

### 关键日志
- 统一Topic消息发送成功/失败日志
- 消息路由决策日志
- 重试任务执行日志
- 消费者处理消息日志

### 监控指标
- 统一Topic消息发送量
- 消息处理成功率
- 重试任务执行频率
- 各渠道消息发送延迟

## 回滚策略

如果发现问题，可以快速回滚：
1. 设置 `unified-topic.enabled: false`
2. 清空或减少白名单系统ID
3. 系统会自动回退到Talos公共队列

## 注意事项

1. **配置刷新**: 使用 `@NacosValue(autoRefreshed = true)` 支持配置热更新
2. **线程安全**: 使用 `ConcurrentHashMap` 和适当的同步机制
3. **异常处理**: 完善的异常处理和降级机制
4. **性能考虑**: 使用线程池处理重试任务，避免阻塞主线程
5. **数据一致性**: 确保消息状态更新的原子性

## 扩展性

该设计支持未来的扩展需求：
- 支持多个统一Topic
- 支持更复杂的路由规则
- 支持消息优先级
- 支持更多的消息渠道 