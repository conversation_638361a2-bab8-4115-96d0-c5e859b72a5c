# 时间窗口限流器 (WindowRateLimiter)

## 概述

时间窗口限流器是一个基于Redis的限流组件，用于替代原有的平滑限流器。它实现了基于固定时间窗口的限流策略，适用于RocketMQ等高并发消息消费场景。

## 特性

- **双重时间窗口控制**：同时支持秒级和分钟级限流
- **appId维度隔离**：每个appId独立计算限流
- **高性能**：基于Redis Lua脚本，原子性操作
- **灵活配置**：支持自定义限流参数
- **静默模式**：支持不抛异常的限流检查

## 默认配置

- **1秒窗口**：最多50个请求
- **1分钟窗口**：最多1000个请求

## 核心组件

### 1. WindowRateLimiter
核心限流器类，提供限流检查功能。

### 2. WindowRateLimiterManager
限流器管理器，负责创建和缓存限流器实例。

### 3. WindowRateLimitScriptConfig
Lua脚本配置类，加载限流脚本。

## 使用方法

### 1. 基本使用

```java
@Autowired
private WindowRateLimiterManager windowRateLimiterManager;

public void processMessage(String appId, String messageContent) {
    WindowRateLimiter limiter = windowRateLimiterManager.getLimiter(appId);
    
    try {
        // 检查限流
        limiter.tryAcquire(appId);
        
        // 处理消息
        doProcessMessage(messageContent);
        
    } catch (LimitException e) {
        // 被限流，丢弃消息或延迟处理
        log.warn("Message dropped due to rate limiting: {}", e.getMessage());
    }
}
```

### 2. 自定义配置

```java
// 自定义限流参数：1秒30个，1分钟500个
WindowRateLimiter limiter = windowRateLimiterManager.getLimiter(appId, 30, 500);
```

### 3. 静默模式

```java
// 不抛出异常的方式
boolean allowed = limiter.tryAcquireQuietly(appId);
if (allowed) {
    // 处理消息
    processMessage(messageContent);
} else {
    // 被限流，记录日志
    log.warn("Message dropped due to rate limiting");
}
```

### 4. 在RocketMQ消费者中使用

```java
@Component
public class MessageConsumer {
    
    @Autowired
    private WindowRateLimiterManager windowRateLimiterManager;
    
    public void consumeMessage(String appId, String topic, Object messageUserInfo) {
        WindowRateLimiter limiter = windowRateLimiterManager.getLimiter(appId);
        
        try {
            limiter.tryAcquire(appId);
            
            // 处理消息
            processRocketMQMessage(topic, messageUserInfo);
            
        } catch (LimitException e) {
            log.warn("RocketMQ message dropped - appId: {}, topic: {}, reason: {}", 
                    appId, topic, e.getMessage());
        }
    }
}
```

## 与原有平滑限流器的区别

| 特性 | 平滑限流器 (SmoothRateLimiter) | 时间窗口限流器 (WindowRateLimiter) |
|------|-------------------------------|----------------------------------|
| 限流策略 | 平滑限流，控制请求间隔 | 固定时间窗口限流 |
| 时间窗口 | 无固定窗口概念 | 1秒 + 1分钟双重窗口 |
| 请求分布 | 均匀分布 | 可能出现突发请求 |
| 适用场景 | 需要平滑请求的场景 | 需要快速响应的高并发场景 |
| 性能 | 需要等待时间 | 立即返回结果 |

## 配置参数

### 脚本参数

- `KEYS[1]`: appId
- `ARGV[1]`: 当前时间戳(毫秒)
- `ARGV[2]`: 1秒窗口限制 (默认50)
- `ARGV[3]`: 1分钟窗口限制 (默认1000)

### 返回值

成功时返回：`{1, "success", second_count, minute_count}`
失败时返回：`{0, "error_reason", current_count, limit_value}`

## Redis键设计

- 秒级键：`{appId}:second:{timestamp_second}`
- 分钟级键：`{appId}:minute:{timestamp_minute}`

## 注意事项

1. **时钟同步**：确保应用服务器时钟同步，避免时间偏移导致的限流异常
2. **Redis性能**：高并发场景下注意Redis性能，建议使用Redis集群
3. **键过期**：脚本会自动设置键的过期时间，避免内存泄漏
4. **监控告警**：建议对限流事件进行监控和告警

## 测试

运行测试类验证功能：

```bash
mvn test -Dtest=WindowRateLimiterTest
```

## 迁移指南

从平滑限流器迁移到时间窗口限流器：

1. 替换依赖注入：
   ```java
   // 原有方式
   @Autowired
   private LimiterManager limiterManager;
   
   // 新方式
   @Autowired
   private WindowRateLimiterManager windowRateLimiterManager;
   ```

2. 替换限流检查：
   ```java
   // 原有方式
   limiterManager.getLimiter(appId).acquire(timeout);
   
   // 新方式
   windowRateLimiterManager.getLimiter(appId).tryAcquire(appId);
   ```

3. 调整异常处理：
   ```java
   // 原有方式会阻塞等待
   // 新方式立即抛出异常，需要处理LimitException
   ``` 