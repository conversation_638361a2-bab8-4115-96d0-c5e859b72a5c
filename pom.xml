<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.mioffice</groupId>
  <artifactId>ums-engine</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>ums-engine</name>
  <description>消息引擎</description>

  <properties>
    <java.version>1.8</java.version>
    <starter.version>2.2.3-SNAPSHOT</starter.version>
    <sentry-logback.version>5.3.0</sentry-logback.version>
    <nacos-starter-version>0.2.13-xiaomi</nacos-starter-version>
    <nacos-actuator-version>0.2.13-xiaomi</nacos-actuator-version>
    <!--sonar.exclusions为不扫描文件配置-->
    <sonar.exclusions>**/**DO.java, **/**Do.java, **/**PO.java, **/**Res.java, **/**Resp.java, **/**Req.java, **/**Dto.java,**/**DTO.java,
      **/**Assembler.java, **/**Converter.java
    </sonar.exclusions>
    <sonar.coverage.exclusions>**/**DO.java, **/**Res.java, **/**Resp.java, **/**Req.java, **/**Dto.java,**/**DTO.java,
      **/**Assembler.java, **/**Converter.java, **/**VO.java, **/**Enum.java</sonar.coverage.exclusions>
    <!--sonar.cpd.exclusions为重复代码检查排除配置，CI模板默认排除**/**DO.java,**/**BO.java,**/**DTO.java,**/**VO.java,
    **/**PO.java,**/**Model.java,**/**Entity.java,**/**Bean.java-->
    <sonar.cpd.exclusions>**/**Enum.java</sonar.cpd.exclusions>
  </properties>


  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.1.8.RELEASE</version>
    <relativePath/> <!-- lookup parent from repository -->
  </parent>

  <repositories>
    <repository>
      <id>mi-new</id>
      <url>https://pkgs.d.xiaomi.net:443/artifactory/public</url>
    </repository>
  </repositories>

  <dependencies>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.10.0</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.10.0</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.10</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-collections4</artifactId>
      <version>4.4</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.10</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <!-- 可选, 非必须 -->
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-core</artifactId>
      <version>1.6.1</version>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.6</version>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.larksuite</groupId>
      <artifactId>appframework-sdk</artifactId>
      <version>1.1.23-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>3.3.2</version>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>5.1.46</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-text</artifactId>
      <version>1.8</version>
    </dependency>

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
      <version>5.8.21</version>
    </dependency>

    <dependency>
      <groupId>com.xiaomi</groupId>
      <artifactId>keycenter-agent-client</artifactId>
      <version>3.5.5</version>
    </dependency>

    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson-spring-boot-starter</artifactId>
      <version>3.23.3</version>
    </dependency>

    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>29.0-jre</version>
    </dependency>

    <dependency>
      <groupId>com.101tec</groupId>
      <artifactId>zkclient</artifactId>
      <version>0.11</version>
      <exclusions>
        <exclusion>
          <artifactId>zookeeper</artifactId>
          <groupId>org.apache.zookeeper</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>android-json</artifactId>
          <groupId>com.vaadin.external.google</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- JUnit 5 依赖 -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- Mockito 依赖 -->
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.info.grpc</groupId>
      <artifactId>mi-grpc-server-spring-boot-starter</artifactId>
      <version>${starter.version}</version>
      <!--如果需要引入 redis 和 datasource , 则必须在 application.yml 中进行参数配置 -->
      <!--如果不需要引入 redis 和 datasource , 必须将其 exclude -->
      <exclusions>
        <exclusion>
          <groupId>com.xiaomi.info.grpc</groupId>
          <artifactId>mi-grpc-redis-spring-boot-starter</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.xiaomi.info.grpc</groupId>
          <artifactId>mi-grpc-datasource-spring-boot-starter</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>libthrift</artifactId>
          <groupId>org.apache.thrift</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.info.grpc</groupId>
      <artifactId>mi-grpc-client-spring-boot-starter</artifactId>
      <version>${starter.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.info.grpc.proto</groupId>
      <artifactId>app-mioffice-ums-engine-v1</artifactId>
      <version>0.0.79-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.info.grpc.proto</groupId>
      <artifactId>app-mioffice-ums-open-v1</artifactId>
      <version>0.0.48-saas-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.cloud</groupId>
      <artifactId>miplan-springboot-starter</artifactId>
      <version>2.1.5.release</version>
      <exclusions>
        <exclusion>
          <artifactId>zookeeper</artifactId>
          <groupId>org.apache.zookeeper</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.zookeeper</groupId>
      <artifactId>zookeeper</artifactId>
      <version>3.4.14</version>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.infra.galaxy</groupId>
      <artifactId>galaxy-talos-wrapper-********</artifactId>
      <version>1.0.0</version>
      <exclusions>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring</artifactId>
          <groupId>org.springframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hbase-client</artifactId>
          <groupId>com.xiaomi.infra</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hadoop-hdfs</artifactId>
          <groupId>org.apache.hadoop</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hadoop-client</artifactId>
          <groupId>org.apache.hadoop</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hadoop-common</artifactId>
          <groupId>org.apache.hadoop</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hadoop-auth</artifactId>
          <groupId>org.apache.hadoop</groupId>
        </exclusion>
        <exclusion>
          <artifactId>java-uuid-generator</artifactId>
          <groupId>com.fasterxml.uuid</groupId>
        </exclusion>
        <exclusion>
          <artifactId>galaxy-lcs-log-collect</artifactId>
          <groupId>com.xiaomi.infra.galaxy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>galaxy-lcs-log-core</artifactId>
          <groupId>com.xiaomi.infra.galaxy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>galaxy-lcs-monitor-client</artifactId>
          <groupId>com.xiaomi.infra.galaxy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>galaxy-lcs-thrift</artifactId>
          <groupId>com.xiaomi.infra.galaxy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>c3p0</artifactId>
          <groupId>c3p0</groupId>
        </exclusion>
        <exclusion>
          <artifactId>zookeeper</artifactId>
          <groupId>org.apache.zookeeper</groupId>
        </exclusion>
        <!--<exclusion>
            <artifactId>zkclient</artifactId>
            <groupId>com.101tec</groupId>
        </exclusion>-->
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>1.3.1.Final</version>
    </dependency>

    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <version>1.3.1.Final</version>
    </dependency>

    <!-- sms -->
    <dependency>
      <groupId>com.xiaomi</groupId>
      <artifactId>xiaomi-common-thrift</artifactId>
      <version>2.8.8</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-io</artifactId>
          <groupId>commons-io</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>com.xiaomi</groupId>
      <artifactId>xiaomi-common-zookeeper</artifactId>
      <version>3.0.3</version>
      <exclusions>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>zookeeper</artifactId>
          <groupId>org.apache.zookeeper</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.xiaomi</groupId>
      <artifactId>sms-thrift</artifactId>
      <version>0.3.1</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/org.freemarker/freemarker -->
    <dependency>
      <groupId>org.freemarker</groupId>
      <artifactId>freemarker</artifactId>
      <version>2.3.28</version>
    </dependency>

    <dependency>
      <groupId>com.github.kevinsawicki</groupId>
      <artifactId>http-request</artifactId>
      <version>6.0</version>
    </dependency>

    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-registry-prometheus</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.info.infra</groupId>
      <artifactId>log-be-spring-boot-starter</artifactId>
      <version>1.0.3.RELEASE</version>
    </dependency>

    <dependency>
      <groupId>io.sentry</groupId>
      <artifactId>sentry-logback</artifactId>
      <version>${sentry-logback.version}</version>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.mit</groupId>
      <artifactId>mit-starter</artifactId>
      <version>1.0.28</version>
    </dependency>

    <!--   nacos     -->
    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>nacos-config-spring-boot-starter</artifactId>
      <version>${nacos-starter-version}</version>
    </dependency>

    <dependency>
      <groupId>com.alibaba.boot</groupId>
      <artifactId>nacos-config-spring-boot-actuator</artifactId>
      <version>${nacos-actuator-version}</version>
    </dependency>

    <dependency>
      <groupId>com.xiaomi.infra</groupId>
      <artifactId>rocketmq-client-java</artifactId>
      <version>1.0.9-RELEASE</version>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>dev</id>
      <properties>
        <env>dev</env>
      </properties>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
    </profile>
    <profile>
      <id>test</id>
      <properties>
        <env>test</env>
      </properties>
    </profile>
    <profile>
      <id>pre</id>
      <properties>
        <env>pre</env>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <env>prod</env>
      </properties>
    </profile>
  </profiles>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <directory>src/main/resources-env/${env}</directory>
        <filtering>true</filtering>
      </resource>
    </resources>

    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
