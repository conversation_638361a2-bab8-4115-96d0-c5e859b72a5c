CREATE TABLE `message_user_saas_info`
(
    `id`                  bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_info_id`        bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '消息id',
    `app_id`              varchar(32) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT '机器人id',
    `msg_tmp_id`          bigint(20) NOT NULL DEFAULT '0' COMMENT '消息模板id',
    `open_id`             varchar(64) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT 'openId',
    `email`               varchar(64) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT 'email',
    `username`            varchar(64) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT 'username',
    `phone`               varchar(256) CHARACTER SET utf8mb4     NOT NULL DEFAULT '' COMMENT 'phone',
    `mi_dept_level2`      varchar(32) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT '二级部门ID',
    `mi_dept_level2_desc` varchar(64) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT '二级部门描述',
    `mi_dept_level3`      varchar(32) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT '三级部门ID',
    `mi_dept_level3_desc` varchar(64) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT '三级部门描述',
    `channel`             tinyint(3) NOT NULL DEFAULT '0' COMMENT '消息类型（1-飞书，2-邮件，3-短信，4-miPush）',
    `message_status`      tinyint(3) NOT NULL DEFAULT '0' COMMENT '消息状态(1-发送中，2-发送成功，3-发送失败)',
    `message_id`          varchar(128) CHARACTER SET utf8mb4     NOT NULL DEFAULT '' COMMENT '消息结果id',
    `error_log`           mediumtext CHARACTER SET utf8mb4 COMMENT '发送失败log',
    `retry_count`         int(10) NOT NULL DEFAULT '0' COMMENT '失败重试',
    `title_cn`            mediumtext COLLATE utf8mb4_unicode_ci COMMENT '消息标题(中文)',
    `title_en`            mediumtext COLLATE utf8mb4_unicode_ci COMMENT '消息标题(英文)',
    `placeholder_content` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '变量数据',
    `extra_id`            varchar(128) CHARACTER SET utf8mb4     NOT NULL DEFAULT '' COMMENT '业务层扩展extra_id',
    `extra_content`       mediumtext COLLATE utf8mb4_unicode_ci COMMENT '业务方自定义数据',
    `create_time`         bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time`         bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `content_flag`        tinyint(3) NOT NULL DEFAULT '0' COMMENT '内容标记（\n0-无，1-飞书原始内容，后续扩展）',
    `final_content`       mediumtext COLLATE utf8mb4_unicode_ci COMMENT 'lark消息体内容(按需存储)',
    `error_type`          tinyint(3) NOT NULL DEFAULT '0' COMMENT '错误类型',
    `cc_email`            mediumtext CHARACTER SET utf8mb4 COMMENT '邮件抄送人，分号分离',
    `attach_email_url`    mediumtext CHARACTER SET utf8mb4 COMMENT '邮件附件FDS地址 格式 ["", ""]',
    `chat_id`             varchar(64) CHARACTER SET utf8mb4      NOT NULL DEFAULT '' COMMENT '群chatId',
    `mi_dept_level4`      varchar(32) CHARACTER SET utf8mb4      NOT NULL DEFAULT '',
    `mi_dept_level4_desc` varchar(64) CHARACTER SET utf8mb4      NOT NULL DEFAULT '',
    `read_status`         tinyint(4) NOT NULL DEFAULT '0' COMMENT '已读(0否1是)',
    `sys_id`              varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '系统编号',
    `is_send_saas`        tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否发送saas,0不发送,1发送',
    PRIMARY KEY (`id`),
    KEY                   `idx_user_info_id` (`user_info_id`),
    KEY                   `idx_app_id` (`app_id`),
    KEY                   `idx_email` (`email`),
    KEY                   `idx_phone` (`phone`),
    KEY                   `idx_username` (`username`),
    KEY                   `idx_extra_id` (`extra_id`),
    KEY                   `idx_update_time` (`update_time`),
    KEY                   `idx_message_status_update_time_retry_count` (`message_status`,`update_time`,`retry_count`),
    KEY                   `idx_extra_status` (`extra_id`,`message_status`),
    KEY                   `idx_channel_message_status_message_read_status` (`channel`,`message_status`,`read_status`),
    KEY                   `idx` (`msg_tmp_id`),
    KEY                   `idx_create_time_read_status` (`create_time`,`read_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='saas消息用户表';

ALTER TABLE `message_user_info`
    ADD COLUMN `is_send_saas` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否发送saas,0不发送,1发送' AFTER `sys_id`;

ALTER TABLE `message_template_info`
    ADD COLUMN `saas_images` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'saas图片' AFTER `resolve_ready`;