create table `email_robot_info`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `type`        tinyint(3)          not null default 0 comment '类型 1 内部邮箱，2 外部邮箱',

    `sender`      varchar(32)         not null default '' comment '邮箱sender',
    `token`       varchar(256)        not null default '' comment '邮箱token',
    `sender_name` varchar(32)         not null default '' comment '机器人sender',
    `mail_host`   varchar(256)        not null default '' comment '邮件地址',

    `stop_flag`   tinyint(3)          not null default 0 comment '停用状态（1-开启, 2-停用）',

    `create_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`),
    unique index `uk_sender` (`sender`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='邮箱机器人表';

create table `sms_robot_info`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `content_type_key` varchar(32)         not null default '' comment '短信模板号',
    `sign`             varchar(32)         not null default '' comment '短信签名',
    `stop_flag`        tinyint(3)          not null default 0 comment '停用状态（1-开启, 2-停用）',
    `create_time`      bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`      bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`)

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='短信机器人表';


create table `dev_user_info`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `email`           varchar(64)         not null default '' comment 'email',
    `username`        varchar(64)         not null default '' comment 'username',

    `update_username` varchar(64)         not null default '' comment '更新者username',
    `create_username` varchar(64)         not null default '' comment '创建者username',

    `create_time`     bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`     bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='测试环境发送范围表';


alter table message_user_info
    add error_type tinyint(3) default 0 not null comment '错误类型';


alter table message_template_info
    add system_id varchar(32) default '' not null comment '系统id';




