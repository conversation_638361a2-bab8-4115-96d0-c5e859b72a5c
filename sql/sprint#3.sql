alter table message_template_info
    add msg_format_type tinyint(3) not null default 1 comment '1 SDK格式消息， 2 json格式消息';


alter table message_template_info
    add card_actions_cn varchar(512) not null default '' comment '卡片按钮cn';

alter table message_template_info
    add card_actions_en varchar(512) not null default '' comment '卡片按钮en';

alter table dev_user_info
    add phone varchar(32) default '' not null comment '手机号';


-- 2020年10月19日18:51:35
-- 三期新需求
alter table message_user_info
    add cc_email text comment '邮件抄送人，分号分离';

alter table message_user_info
    add attach_email_url text comment '邮件附件FDS地址 格式 ["", ""]';


alter table message_user_info
    modify message_id varchar(128) default '' not null comment '消息结果id';



alter table message_template_info
    modify content_cn longtext null comment '消息内容(中文)';

alter table message_template_info
    modify content_en longtext null comment '消息内容(英文)';


alter table message_user_info
    add index idx_update_time (`update_time`);


-- 2020年11月26日16:44:46
alter table message_user_info
    add chat_id varchar(64) not null default '' comment '群chatId';

alter table message_user_info
    add mi_dept_level4 varchar(32) not null default '' comment '';

alter table message_user_info
    add mi_dept_level4_desc varchar(64) not null default '' comment '';

-- 2020-12-10

alter table message_user_info
    add index idx_extra_status (`extra_id`, `message_status`);

alter table sms_robot_info
    add unique uk_content_type_key(`content_type_key`)