create table `robot_info`
(
    `id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `app_id`             varchar(32)         not null default '' comment '机器人id',
    `app_secret`         varchar(256)        not null default '' comment '机器人secret',
    `app_short_name`     varchar(32)         not null default '' comment '机器人appShortName',
    `encrypt_key`        varchar(32)         not null default '' comment '机器人encryptKey',
    `verification_token` varchar(32)         not null default '' comment '机器人verificationToken',

    `stop_flag`          tinyint(3)          not null default 0 comment '停用状态（1-开启, 2-停用）',

    `create_time`        bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`        bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`),
    unique index `uk_app_id` (`app_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='机器人表';

create table `message_template_info`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `title_cn`     text comment '消息标题(中文)',
    `title_en`     text comment '消息标题(英文)',
    `content_cn`   text comment '消息内容(中文)',
    `content_en`   text comment '消息内容(英文)',

    `channel`      tinyint(3)          not null default 0 comment '消息类型（1-飞书，2-邮件，3-短信，4-miPush）',

    `app_id`       varchar(32)         not null default '' comment '机器人id',

    `card_actions` text comment '按钮  [{"name": "", "landingUrl":"", "val": {"key": "val"} }]',

    `images`       text comment '图片id:imageKey {"123iuy21u312890831": "img_18290830123"}',

    `create_time`  bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`  bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',

    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='消息模板表';

create table `message_user_info`
(
    `id`                  bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',

    `app_id`              varchar(32)         not null default '' comment '机器人id',
    `msg_tmp_id`          bigint(20)          NOT NULL default 0 COMMENT '消息模板id',

    `open_id`             varchar(64)         not null default '' comment 'openId',
    `email`               varchar(64)         not null default '' comment 'email',
    `username`            varchar(64)         not null default '' comment 'username',
    `phone`               varchar(32)         not null default '' comment 'phone',

    `mi_dept_level2`      VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '二级部门ID',
    `mi_dept_level2_desc` VARCHAR(64)         NOT NULL DEFAULT '' COMMENT '二级部门描述',
    `mi_dept_level3`      VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '三级部门ID',
    `mi_dept_level3_desc` VARCHAR(64)         NOT NULL DEFAULT '' COMMENT '三级部门描述',

    `channel`             tinyint(3)          not null default 0 comment '消息渠道（1-飞书，2-邮件，3-短信，4-miPush）',
    `message_status`      tinyint(3)          not null default 0 comment '消息状态(1-发送中，2-发送成功，3-发送失败，4-消息中断)',
    `message_id`          varchar(64)         not null default '' comment '飞书消息id',
    `error_log`           text comment '发送失败log',

    `retry_count`         int(10)             not null default 0 comment '失败重试',

    `title_cn`            text comment '消息标题(中文)',
    `title_en`            text comment '消息标题(英文)',
    `placeholder_content` text comment '变量数据',

    `extra_id`            varchar(32)         not null default '' comment '业务层扩展extra_id',
    `extra_content`       text comment '业务方自定义数据',

    `content_flag`        tinyint(3)          not null default 0 comment '内容标记（0-无，1-飞书原始内容，后续扩展）',
    `final_content`       text comment '最终转换后的内容体（按需存储）',

    `create_time`         bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time`         bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',

    PRIMARY KEY (`id`),
    index idx_app_id (`app_id`),
    index idx_email (`email`),
    index idx_phone (`phone`),
    index idx_username (`username`),
    index idx_extra_id (`extra_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='消息用户表';


create table `message_filter_info`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `extra_id`    varchar(32)         not null default '' comment '业务层扩展extra_id',
    `create_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '创建时间',
    `update_time` bigint(20)          NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`),
    unique uk_extra_id (`extra_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8 COMMENT ='消息中断';

alter table `message_user_info` add index idx_message_status_update_time_retry_count (`message_status`, `update_time`, `retry_count`);