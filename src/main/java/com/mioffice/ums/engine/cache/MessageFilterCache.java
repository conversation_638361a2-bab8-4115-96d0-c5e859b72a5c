package com.mioffice.ums.engine.cache;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.engine.entity.info.MessageFilterInfo;
import com.mioffice.ums.engine.mapper.MessageFilterInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 消息过滤cache
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.12
 */
@Component
public class MessageFilterCache {

    /**
     * 消息 extraId 黑名单
     */
    private static final ConcurrentHashMap.KeySetView<String, Boolean> extraIdCache = ConcurrentHashMap.newKeySet();

    private final MessageFilterInfoMapper messageFilterInfoMapper;

    @PostConstruct
    public void init() {
        this.load();
    }

    public void load() {
        List<MessageFilterInfo> messageFilterInfoList = messageFilterInfoMapper.selectList(
                Wrappers.lambdaQuery()
        );

        if (messageFilterInfoList.isEmpty()) {
            return;
        }

        List<String> extraIdList = messageFilterInfoList.stream().map(MessageFilterInfo::getExtraId).collect(Collectors.toList());
        extraIdCache.addAll(extraIdList);
    }

    public MessageFilterCache(MessageFilterInfoMapper messageFilterInfoMapper) {
        this.messageFilterInfoMapper = messageFilterInfoMapper;
    }

    public void add(String extraId) {
        extraIdCache.add(extraId);
    }

    public void remove(String extraId) {
        extraIdCache.remove(extraId);
    }

    public boolean contain(String extraId) {
        return extraIdCache.contains(extraId);
    }


}
