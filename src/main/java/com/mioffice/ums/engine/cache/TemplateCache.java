package com.mioffice.ums.engine.cache;

import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import static com.mioffice.ums.engine.enums.constants.RedisConstants.TEMPLATE_ID_FIELD;
import com.mioffice.ums.engine.mapper.MessageTemplateInfoMapper;
import com.mioffice.ums.engine.utils.JsonUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.10
 */
@Component
public class TemplateCache {

    private final MessageTemplateInfoMapper messageTemplateInfoMapper;

    private final RedissonClient redissonClient;

    public TemplateCache(MessageTemplateInfoMapper messageTemplateInfoMapper, RedissonClient redissonClient) {
        this.messageTemplateInfoMapper = messageTemplateInfoMapper;
        this.redissonClient = redissonClient;
    }

    public MessageTemplateInfo get(Long id) {
        Object obj = redissonClient.getBucket(String.format("%s:%d", TEMPLATE_ID_FIELD, id)).get();
        if (Objects.nonNull(obj)) {
            return JsonUtils.parse(obj.toString(), new TypeToken<MessageTemplateInfo>() {
            }.getType());
        } else {
            MessageTemplateInfo messageTemplateInfo = messageTemplateInfoMapper.selectById(id);
            redissonClient.getBucket(String.format("%s:%d", TEMPLATE_ID_FIELD, id))
                    .set(JsonUtils.toJson(messageTemplateInfo), Duration.ofHours(1));
            return messageTemplateInfo;
        }
    }
}
