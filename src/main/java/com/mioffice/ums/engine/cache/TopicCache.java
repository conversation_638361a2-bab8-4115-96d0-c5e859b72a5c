package com.mioffice.ums.engine.cache;

import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.engine.entity.bo.AppTopicDetailBO;
import static com.mioffice.ums.engine.enums.constants.RedisConstants.TOPIC_FIELD;
import com.mioffice.ums.engine.service.grpc.client.AppListGrpcClient;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.mioffice.ums.engine.utils.MapperUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * author:junfudong
 * created:2024/03/26 11:32
 */
@Component
public class TopicCache {

    @Autowired
    private AppListGrpcClient appListGrpcClient;

    @Autowired
    private RedissonClient redissonClient;

    public List<AppTopicDetailBO> getByRobotId(String robotId) {
        Object obj = redissonClient.getBucket(String.format("%s:robot:%s", TOPIC_FIELD, robotId)).get();
        if (Objects.nonNull(obj)) {
            return JsonUtils.parse(obj.toString(), new TypeToken<List<AppTopicDetailBO>>() {
            }.getType());
        } else {
            List<AppTopicDetailBO> appTopicDetailBOList =
                    MapperUtil.INSTANCE.map2AppTopicDetailBOList(
                            appListGrpcClient.getRobotTopic(robotId).getRobotAppTopicInfo().getAppTopicInfoList());
            if (CollectionUtils.isNotEmpty(appTopicDetailBOList)) {
                redissonClient.getBucket(String.format("%s:robot:%s", TOPIC_FIELD, robotId))
                        .set(JsonUtils.toJson(appTopicDetailBOList), Duration.ofMinutes(10));
            }
            return appTopicDetailBOList;
        }
    }

    public AppTopicDetailBO getBySysId(String sysId) {
        Object obj = redissonClient.getBucket(String.format("%s:sys:%s", TOPIC_FIELD, sysId)).get();
        if (Objects.nonNull(obj)) {
            return JsonUtils.parse(obj.toString(), new TypeToken<AppTopicDetailBO>() {
            }.getType());
        } else {
            AppTopicDetailBO appTopicDetail =
                    MapperUtil.INSTANCE.map2AppTopicDetailBO(appListGrpcClient.getAppTopic(sysId).getAppTopicInfo());
            if (Objects.nonNull(appTopicDetail) && StringUtils.isNotBlank(appTopicDetail.getTopic()) &&
                    appTopicDetail.getStatus() == 2) {
                redissonClient.getBucket(String.format("%s:sys:%s", TOPIC_FIELD, sysId))
                        .set(JsonUtils.toJson(appTopicDetail), Duration.ofMinutes(10));
            }
            return appTopicDetail;
        }
    }
}
