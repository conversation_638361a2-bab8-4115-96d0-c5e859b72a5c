package com.mioffice.ums.engine.cache;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一Topic系统ID白名单缓存
 * 用于判断哪些系统ID应该走统一RocketMQ Topic而不是Talos
 * 
 * <AUTHOR>
 * @date 2024.12.20
 */
@Slf4j
@Component
public class UnifiedTopicSysIdCache {

    /**
     * 系统ID白名单，在此名单中的系统ID将走统一RocketMQ Topic
     */
    private final Set<String> sysIdWhitelist = ConcurrentHashMap.newKeySet();

    @NacosValue(value = "${ums.engine.unified-topic.sys-id-whitelist:}", autoRefreshed = true)
    private String sysIdWhitelistConfig;

    @PostConstruct
    public void init() {
        refreshWhitelist();
        log.info("UnifiedTopicSysIdCache initialized with whitelist: {}", sysIdWhitelist);
    }

    /**
     * 刷新白名单配置
     */
    public void refreshWhitelist() {
        sysIdWhitelist.clear();
        if (StringUtils.isNotBlank(sysIdWhitelistConfig)) {
            sysIdWhitelist.addAll(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(sysIdWhitelistConfig));
        }
        log.info("Refreshed unified topic sys id whitelist: {}", sysIdWhitelist);
    }

    /**
     * 判断系统ID是否在统一Topic白名单中
     * 
     * @param sysId 系统ID
     * @return true表示在白名单中，应该走统一RocketMQ Topic
     */
    public boolean isInUnifiedTopicWhitelist(String sysId) {
        if (StringUtils.isBlank(sysId)) {
            return false;
        }
        return sysIdWhitelist.contains(sysId);
    }

    /**
     * 添加系统ID到白名单
     * 
     * @param sysId 系统ID
     */
    public void addToWhitelist(String sysId) {
        if (StringUtils.isNotBlank(sysId)) {
            sysIdWhitelist.add(sysId);
            log.info("Added sys id {} to unified topic whitelist", sysId);
        }
    }

    /**
     * 从白名单中移除系统ID
     * 
     * @param sysId 系统ID
     */
    public void removeFromWhitelist(String sysId) {
        if (StringUtils.isNotBlank(sysId)) {
            sysIdWhitelist.remove(sysId);
            log.info("Removed sys id {} from unified topic whitelist", sysId);
        }
    }

    /**
     * 获取当前白名单
     * 
     * @return 白名单集合的副本
     */
    public Set<String> getWhitelist() {
        return Set.copyOf(sysIdWhitelist);
    }
}
