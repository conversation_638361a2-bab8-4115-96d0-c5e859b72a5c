package com.mioffice.ums.engine.config;

import com.mioffice.ums.engine.rocketmq.ProducerAndConsumerManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * @ClassName AppStartUp
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/12 18:01
 **/

@Slf4j
@Component
public class AppStartUp implements ApplicationRunner {

    @Autowired
    private ProducerAndConsumerManager producerAndConsumerManager;

    @Override
    public void run(ApplicationArguments args) {
        producerAndConsumerManager.init();
    }
}
