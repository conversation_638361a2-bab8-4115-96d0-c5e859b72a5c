package com.mioffice.ums.engine.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * @ClassName AsyncConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/8/21 16:48
 **/
@Configuration
@EnableAsync
public class AsyncConfig {
//    @Bean("asyncUploadImageExecutor")
//    public Executor asyncUploadImageExecutor() {
//        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
//        threadPoolTaskExecutor.setCorePoolSize(10);
//        threadPoolTaskExecutor.setMaxPoolSize(20);
//        threadPoolTaskExecutor.setQueueCapacity(60);
//        threadPoolTaskExecutor.setThreadNamePrefix("uploadImageExecutor-");
//        threadPoolTaskExecutor.initialize();
//        return threadPoolTaskExecutor;
//    }

    @Bean("async-retract-message")
    public Executor asyncRetractMessageExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(5);
        threadPoolTaskExecutor.setMaxPoolSize(10);
        threadPoolTaskExecutor.setQueueCapacity(10 * 1024);
        threadPoolTaskExecutor.setThreadNamePrefix("async-retract-message-");
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }

    @Bean("message-to-eventbus")
    public Executor sendMessage2EventBusExecutor() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(50);
        threadPoolTaskExecutor.setMaxPoolSize(100);
        threadPoolTaskExecutor.setQueueCapacity(100 * 1024);
        threadPoolTaskExecutor.setThreadNamePrefix("message-to-eventbus-");
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }
}
