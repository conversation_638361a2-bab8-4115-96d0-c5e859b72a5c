package com.mioffice.ums.engine.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "ums.lark-saas-map")
public class LarkSaasMapConfig {
    private Map<String, String> linkMap;
    private Map<String, String> appIdMap;
    private Map<String, String> saasIdSecretMap;
    private Map<String, String> imgKeyMap;
}