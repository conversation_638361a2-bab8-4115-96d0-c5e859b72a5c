package com.mioffice.ums.engine.config;

import com.mioffice.ums.engine.exceptions.LuaScriptLoadException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/8/27
 */
@Slf4j
@Configuration
public class LuaScriptConfig {

    public final String script;

    public LuaScriptConfig() {
        try (InputStream stream = LuaScriptConfig.class.getClassLoader().getResourceAsStream("script/smooth-rate-limit.lua")) {
            Assert.notNull(stream, "file not exists");
            List<String> lines = IOUtils.readLines(stream);
            script = String.join(System.lineSeparator(), lines);
            log.info("scriptLoad success {}", script);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new LuaScriptLoadException(e);
        }
    }

    public String getScriptSha() {
        return this.script;
    }

}
