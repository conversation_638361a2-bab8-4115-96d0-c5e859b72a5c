package com.mioffice.ums.engine.config;

import com.google.gson.Gson;
import com.mioffice.ums.engine.manager.EmailRobotManager;
import com.mioffice.ums.engine.manager.EnhanceMiWorkRobotManager;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.LarkSaasRobotManager;
import com.mioffice.ums.engine.manager.LarkSaasV1RobotManager;
import com.mioffice.ums.engine.manager.MessageV1MiWorkRobotManager;
import com.mioffice.ums.engine.manager.SmsRobotManager;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import com.mioffice.ums.engine.message.LarkSaasMessageHelper;
import com.mioffice.ums.engine.template.LarkSaasTemplateParse;
import com.mioffice.ums.engine.template.TemplateParse;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.04
 */
@Configuration
public class ManagerConfig {

    @Value("${email.guard.out-url}")
    private String outGuardUrl;

    @Value("${email.guard.out-multiple-file-url}")
    private String outGuardMultipleFileUrl;

    @Autowired
    private LarkSaasMapConfig larkSaasMapConfig;

    @Bean
    @Primary
    public EnhanceMiWorkRobotManager enhanceMiWorkRobotManager(RobotInfoMapper robotInfoMapper, TemplateParse templateParse) {
        EnhanceMiWorkRobotManager enhanceMiWorkRobotManager = new EnhanceMiWorkRobotManager();

        enhanceMiWorkRobotManager.setRobotInfoMapper(robotInfoMapper);
        enhanceMiWorkRobotManager.setTemplateParse(templateParse);
        enhanceMiWorkRobotManager.setLarkSaasMapConfig(larkSaasMapConfig);
        enhanceMiWorkRobotManager.init();
        return enhanceMiWorkRobotManager;
    }

    @Bean
    public MessageV1MiWorkRobotManager messageV1MiWorkRobotManager(RobotInfoMapper robotInfoMapper, TemplateParse templateParse) {
        MessageV1MiWorkRobotManager messageV1MiWorkRobotManager = new MessageV1MiWorkRobotManager();

        messageV1MiWorkRobotManager.setRobotInfoMapper(robotInfoMapper);
        messageV1MiWorkRobotManager.setTemplateParse(templateParse);
        messageV1MiWorkRobotManager.setLarkSaasMapConfig(larkSaasMapConfig);
        messageV1MiWorkRobotManager.init();
        return messageV1MiWorkRobotManager;
    }

    @Bean
    public LarkSaasRobotManager larkSaasRobotManager(RobotInfoMapper robotInfoMapper, LarkSaasTemplateParse templateParse) {
        LarkSaasRobotManager larkSaasRobotManager = new LarkSaasRobotManager();

        larkSaasRobotManager.setRobotInfoMapper(robotInfoMapper);
        larkSaasRobotManager.setTemplateParse(templateParse);
        larkSaasRobotManager.setLarkSaasMapConfig(larkSaasMapConfig);
        larkSaasRobotManager.setLarkSaasMessageHelper(new LarkSaasMessageHelper(templateParse));
        larkSaasRobotManager.init();
        return larkSaasRobotManager;
    }

    @Bean
    public LarkSaasV1RobotManager larkSaasV1RobotManager(RobotInfoMapper robotInfoMapper, LarkSaasTemplateParse templateParse) {
        LarkSaasV1RobotManager larkSaasV1RobotManager = new LarkSaasV1RobotManager();

        larkSaasV1RobotManager.setRobotInfoMapper(robotInfoMapper);
        larkSaasV1RobotManager.setTemplateParse(templateParse);
        larkSaasV1RobotManager.setLarkSaasMapConfig(larkSaasMapConfig);
        larkSaasV1RobotManager.setLarkSaasMessageHelper(new LarkSaasMessageHelper(templateParse));
        larkSaasV1RobotManager.init();
        return larkSaasV1RobotManager;
    }

    @Bean
    public EmailRobotManager emailRobotManager(EmailRobotInfoMapper emailRobotInfoMapper, TemplateParse templateParse) {
        EmailRobotManager emailRobotManager = new EmailRobotManager();
        emailRobotManager.setEmailRobotInfoMapper(emailRobotInfoMapper);
        emailRobotManager.setTemplateParse(templateParse);
        emailRobotManager.setOutGuardUrl(outGuardUrl);
        emailRobotManager.setOutGuardMultipleFileUrl(outGuardMultipleFileUrl);
        emailRobotManager.init();
        return emailRobotManager;
    }

    @Bean
    @DependsOn("smsConfig")
    public SmsRobotManager smsRobotManager(SmsRobotInfoMapper smsRobotInfoMapper, TemplateParse templateParse, Gson gson) {
        SmsRobotManager smsRobotManager = new SmsRobotManager(gson);
        smsRobotManager.setSmsRobotInfoMapper(smsRobotInfoMapper);
        smsRobotManager.setTemplateParse(templateParse);
        smsRobotManager.init();
        return smsRobotManager;
    }

    @Bean
    public IdDeduplicateManager idDeduplicateManager(RedissonClient redissonClient) {
        IdDeduplicateManager idDeduplicateManager = new IdDeduplicateManager();
        idDeduplicateManager.setRedissonClient(redissonClient);
        idDeduplicateManager.init();
        return idDeduplicateManager;
    }
}
