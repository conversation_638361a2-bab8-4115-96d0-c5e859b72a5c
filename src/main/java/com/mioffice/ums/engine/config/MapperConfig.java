package com.mioffice.ums.engine.config;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.core.incrementer.IKeyGenerator;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.core.toolkit.Sequence;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.mioffice.ums.engine.datasource.DatasourceInterceptor;
import com.mioffice.ums.engine.datasource.RWDatasourceContext;
import com.mioffice.ums.engine.datasource.RWDynamicDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.04
 */
@Slf4j
@Configuration
@MapperScan("com.mioffice.ums.engine.mapper")
@EnableConfigurationProperties(MybatisPlusProperties.class)
public class MapperConfig implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        // 设置请求的页面大于最大页后操作， true调回到首页，false 继续请求  默认false
        paginationInterceptor.setOverflow(false);
        return paginationInterceptor;
    }

    @Bean
    public IdentifierGenerator idGenerator() {
        return new CustomIdGenerator();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public static class CustomIdGenerator implements IdentifierGenerator {

        public static final String ENV_POP_ID_KEY = "POD_IP";
        public static final String ENV_POP_ID_NAME = "POD_NAME";
        public static final Long DEFAULT_WORKER_ID = 0L;
        public static final Long DEFAULT_DATA_CENTER_ID = 0L;

        private final Sequence sequence;

        public CustomIdGenerator() {
            String podIp = System.getenv(ENV_POP_ID_KEY);
            if (StringUtils.isNoneBlank(podIp)) {
                // workId 不能超过31
                int workId = (podIp.hashCode() > 0 ? podIp.hashCode() : podIp.hashCode() * -1) % 31;
                log.info("雪花id  workId = [{}]", workId);
                this.sequence = new Sequence(workId, DEFAULT_DATA_CENTER_ID);
            } else {
                this.sequence = new Sequence(DEFAULT_WORKER_ID, DEFAULT_DATA_CENTER_ID);
            }

        }

        @Override
        public Long nextId(Object entity) {
            return sequence.nextId();
        }
    }


    @Bean
    public DatasourceInterceptor datasourcePlugin() {
        return new DatasourceInterceptor();
    }

    @Bean
    @ConfigurationProperties("spring.datasource.master")
    public DataSource masterDatasource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    @ConfigurationProperties("spring.datasource.slave")
    public DataSource slaveDatasource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    public RWDynamicDataSource rwDynamicDataSource(@Qualifier("masterDatasource") DataSource masterDatasource, @Qualifier("slaveDatasource") DataSource slaveDatasource) {

        RWDynamicDataSource rwDynamicDataSource = new RWDynamicDataSource();

        // 默认数据源
        rwDynamicDataSource.setDefaultTargetDataSource(masterDatasource);
        Map<Object, Object> targetDatasource = new HashMap<>(2);
        targetDatasource.put(RWDatasourceContext.MASTER, masterDatasource);
        targetDatasource.put(RWDatasourceContext.SLAVE, slaveDatasource);
        rwDynamicDataSource.setTargetDataSources(targetDatasource);

        return rwDynamicDataSource;
    }

    @Bean
    public SqlSessionFactory sqlSessionFactoryBean(RWDynamicDataSource rwDynamicDataSource, Interceptor[] interceptors, MybatisPlusProperties mybatisPlusProperties) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();

        sqlSessionFactoryBean.setDataSource(rwDynamicDataSource);

        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath:/mapper/*Mapper.xml"));

        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        configuration.setJdbcTypeForNull(JdbcType.NULL);

        GlobalConfig globalConfig = mybatisPlusProperties.getGlobalConfig();

        this.getBeanThen(MetaObjectHandler.class, globalConfig::setMetaObjectHandler);
        this.getBeanThen(IKeyGenerator.class, i -> globalConfig.getDbConfig().setKeyGenerator(i));
        this.getBeanThen(ISqlInjector.class, globalConfig::setSqlInjector);
        this.getBeanThen(IdentifierGenerator.class, globalConfig::setIdentifierGenerator);
        sqlSessionFactoryBean.setGlobalConfig(globalConfig);

        sqlSessionFactoryBean.setConfiguration(configuration);
        sqlSessionFactoryBean.setPlugins(interceptors);
        return sqlSessionFactoryBean.getObject();
    }

    private <T> void getBeanThen(Class<T> clazz, Consumer<T> consumer) {
        if (this.applicationContext.getBeanNamesForType(clazz, false, false).length > 0) {
            consumer.accept(this.applicationContext.getBean(clazz));
        }
    }

    @Bean
    @Primary
    public DataSourceTransactionManager testTransactionManager(@Qualifier("masterDatasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
