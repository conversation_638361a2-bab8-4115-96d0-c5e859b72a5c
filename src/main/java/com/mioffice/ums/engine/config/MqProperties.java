package com.mioffice.ums.engine.config;

import lombok.Data;
import org.springframework.stereotype.Component;

import com.alibaba.nacos.api.config.annotation.NacosConfigurationProperties;
import com.alibaba.nacos.api.config.ConfigType;

import java.util.HashSet;
import java.util.Set;

/**
 * MQ相关配置属性
 * 统一管理消息队列的所有配置项
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Component
@NacosConfigurationProperties(prefix = "ums.engine", dataId = "common_config", groupId = "infra.ums", type = ConfigType.YAML, autoRefreshed = true)
public class MqProperties {

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 统一Topic配置
     */
    private UnifiedTopic unifiedTopic = new UnifiedTopic();

    /**
     * 限流配置
     */
    private RateLimit rateLimit = new RateLimit();

    /**
     * 飞书API升级配置
     */
    private LarkApiUpgrade larkApiUpgrade = new LarkApiUpgrade();

    /**
     * 专属队列配置
     */
    private ExclusiveQueue exclusiveQueue = new ExclusiveQueue();

    @Data
    public static class Retry {
        /**
         * 重试次数限制
         */
        private Integer limit = 50;

        /**
         * 发送中消息重试间隔（分钟）
         */
        private Integer sendingAfter = 5;

        /**
         * 失败消息重试间隔（分钟）
         */
        private Integer failedAfter = 1;
    }

    @Data
    public static class UnifiedTopic {
        /**
         * 是否启用统一Topic功能
         */
        private boolean enabled = false;

        /**
         * 统一Topic名称
         */
        private String name = "ums-unified-topic";

        /**
         * 使用统一Topic的机器人ID列表
         */
        private Set<String> robotIds = new HashSet<>();

        /**
         * Topic延迟最小秒数
         */
        private int delayMinSeconds = 1;

        /**
         * Topic延迟最大秒数
         */
        private int delayMaxSeconds = 10;
    }

    @Data
    public static class RateLimit {
        /**
         * 邮件限流配置
         */
        private Email email = new Email();

        /**
         * 短信限流配置
         */
        private Sms sms = new Sms();

        /**
         * 飞书限流配置
         */
        private Miwork miwork = new Miwork();

        @Data
        public static class Email {
            /**
             * 邮件限流时间窗口（毫秒）
             */
            private long rateMs = 1000L;

            /**
             * 邮件限流速率（每个时间窗口内的最大请求数）
             */
            private long rate = 10L;
        }

        @Data
        public static class Sms {
            /**
             * 短信限流时间窗口（毫秒）
             */
            private long rateMs = 1000L;

            /**
             * 短信限流速率（每个时间窗口内的最大请求数）
             */
            private long rate = 10L;
        }

        @Data
        public static class Miwork {
            /**
             * 飞书限流时间窗口（毫秒）
             */
            private long rateMs = 1000L;

            /**
             * 飞书限流速率（每个时间窗口内的最大请求数）
             */
            private long rate = 50L;

            /**
             * 飞书秒级限流（每秒最大请求数）
             */
            private int secondLimit = 50;

            /**
             * 飞书分钟级限流（每分钟最大请求数）
             */
            private int minuteLimit = 1000;
        }
    }

    @Data
    public static class LarkApiUpgrade {
        /**
         * 是否启用白名单
         */
        private boolean enableWhiteList = false;

        /**
         * 白名单列表
         */
        private Set<String> whiteList = new HashSet<>();
    }

    // Topic配置类已移除，延迟配置已移动到UnifiedTopic中

    @Data
    public static class ExclusiveQueue {
        /**
         * 不走专属队列的机器人列表
         */
        private Set<String> excludeBots = new HashSet<>();
    }
} 