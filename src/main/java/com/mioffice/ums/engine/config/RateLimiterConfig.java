package com.mioffice.ums.engine.config;

import com.mioffice.ums.engine.utils.MyZkSerializer;
import org.I0Itec.zkclient.ZkClient;
import org.I0Itec.zkclient.ZkConnection;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/8/11 20:19
 */
@Configuration
public class RateLimiterConfig {

    @Bean
    @ConditionalOnMissingBean(ZkClient.class)
    public ZkClient zkClient(@Value("${zk.server}") String server,
                             @Value("${zk.session-timeout-ms}") Integer sessionTimeoutMs,
                             @Value("${zk.connection-timeout-ms}") Integer connectionTimeoutMs) {
        if (StringUtils.isBlank(server)) {
            return null;
        }

        ZkConnection zkConnection = new ZkConnection(server, sessionTimeoutMs);
        ZkClient zkClient = new ZkClient(zkConnection, connectionTimeoutMs);
        zkClient.setZkSerializer(new MyZkSerializer());
        return zkClient;
    }
}
