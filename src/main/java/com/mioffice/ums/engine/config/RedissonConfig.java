package com.mioffice.ums.engine.config;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.07.21
 */
//@Configuration
public class RedissonConfig {

//    @Value("${spring.redis.host}")
//    private String host;
//
//    @Value("${spring.redis.port}")
//    private String port;
//
//    @Value("${spring.redis.password}")
//    private String password;
//
//    @Bean
//    public RedissonClient getRedisson() {
//        Config config = new Config();
//        config.useSingleServer().setAddress("redis://" + host + ":" + port);
//        config.useSingleServer().setPassword(password);
//        return Redisson.create(config);
//    }
}
