package com.mioffice.ums.engine.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @ClassName RocketMqProperties
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/3/26 14:48
 **/

@Data
@ConfigurationProperties(prefix = "rocketmq")
@Component
public class RocketMqProperties {
    private String nameServer;
    private String ak;
    private String sk;

    /**
     * 统一Topic配置，用于替代Talos
     */
    private String unifiedTopic;

    /**
     * 统一Topic的生产者组
     */
    private String unifiedProducerGroup;

    /**
     * 统一Topic的消费者组
     */
    private String unifiedConsumerGroup;
}
