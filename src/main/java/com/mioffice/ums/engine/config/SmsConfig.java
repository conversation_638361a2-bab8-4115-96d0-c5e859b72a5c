package com.mioffice.ums.engine.config;

import com.xiaomi.miliao.thrift.ClientFactory;
import com.xiaomi.miliao.zookeeper.EnvironmentType;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.sms.SmsUserconnectService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2020/9/1
 */
@EnableConfigurationProperties(SmsProperties.class)
@Configuration
public class SmsConfig implements InitializingBean {
    @Autowired
    SmsProperties smsProperties;

    @Override
    public void afterPropertiesSet() throws Exception {
        String env = smsProperties.getEnv();
        EnvironmentType type = null;
        switch (env.toUpperCase()) {
            case "TEST":
                // 与对接人经沟通 线上和测试使用同样的环境
                type = EnvironmentType.C3;
                break;
            case "C3":
                // C3 环境, 如线上需要部署到c4则另外起一个配置
                type = EnvironmentType.C3;
                break;
            default:
                // 不会真实发出短信，但是有msg-id返回值
                type = EnvironmentType.STAGING;
                break;
        }
        ZKFacade.getZKSettings().setEnviromentType(type);
    }

//    @Bean
//    public SmsUserconnectService.Iface smsClient() {
//        return ClientFactory.getClient(SmsUserconnectService.Iface.class);
//    }

}
