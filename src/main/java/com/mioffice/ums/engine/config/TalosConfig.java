package com.mioffice.ums.engine.config;

import com.mioffice.ums.engine.limiter.LimiterManager;
import com.mioffice.ums.engine.talos.DefaultTalosHelper;
import com.mioffice.ums.engine.talos.MessageProcessor;
import com.mioffice.ums.engine.talos.TalosConstant;
import com.mioffice.ums.engine.talos.TalosHelper;
import com.mioffice.ums.engine.talos.callback.*;
import com.xiaomi.infra.galaxy.talos.client.SimpleTopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.client.TalosClientConfigKeys;
import libthrift091.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Properties;

/**
 * Talos 配置类
 *
 * <AUTHOR>
 * @date 2020/8/11 13:35
 */
@Configuration
public class TalosConfig {


    @Bean
    public TalosHelper talosHelper(@Value("${galaxy.talos.client.access-key}") String accessKey,
                                   @Value("${galaxy.talos.client.acess-secret}") String accessSecret,
                                   @Value("${galaxy.talos.service.endpoint}") String endpoint,
                                   MiworkMessageCallback miworkMessageCallback,
                                   SmsMessageCallback smsMessageCallback,
                                   EmailMessageCallback emailMessageCallback,
                                   MipushMessageCallback mipushMessageCallback,
                                   SettingMessageCallback settingMessageCallback,
                                   List<MessageProcessor<?>> processors,
                                   LimiterManager limiterManager) throws TException {
        Properties properties = new Properties();
        properties.setProperty(TalosClientConfigKeys.GALAXY_TALOS_ACCESS_KEY, accessKey);
        properties.setProperty(TalosClientConfigKeys.GALAXY_TALOS_ACCESS_SECRET, accessSecret);
        properties.setProperty(TalosClientConfigKeys.GALAXY_TALOS_SERVICE_ENDPOINT, endpoint);
        properties.setProperty(TalosClientConfigKeys.GALAXY_TALOS_CLIENT_FALCON_MONITOR_SWITCH, "false");

        TalosHelper talosHelper = new DefaultTalosHelper(properties);
        talosHelper.createProducer(TalosConstant.TOPIC_NAME_MI_WORK, new SimpleTopicAbnormalCallback(),
                miworkMessageCallback);
        talosHelper.createProducer(TalosConstant.TOPIC_NAME_SMS, new SimpleTopicAbnormalCallback(),
                smsMessageCallback);
        talosHelper.createProducer(TalosConstant.TOPIC_NAME_EMAIL, new SimpleTopicAbnormalCallback(),
                emailMessageCallback);
        talosHelper.createProducer(TalosConstant.TOPIC_NAME_MI_PUSH, new SimpleTopicAbnormalCallback(),
                mipushMessageCallback);
        talosHelper.createProducer(TalosConstant.TOPIC_NAME_SETTING, new SimpleTopicAbnormalCallback(),
                settingMessageCallback);

        // consumers configuration
        processors.forEach(processor -> talosHelper.createConsume(processor, limiterManager));

        return talosHelper;
    }

}
