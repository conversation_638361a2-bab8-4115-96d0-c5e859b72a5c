package com.mioffice.ums.engine.config;

import com.mioffice.ums.engine.exceptions.LuaScriptLoadException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

import java.io.InputStream;
import java.util.List;

/**
 * 时间窗口限流脚本配置
 * <AUTHOR>
 * @since 2020/8/27
 */
@Slf4j
@Configuration
public class WindowRateLimitScriptConfig {

    public final String script;

    public WindowRateLimitScriptConfig() {
        try (InputStream stream = WindowRateLimitScriptConfig.class.getClassLoader().getResourceAsStream("script/window-rate-limit.lua")) {
            Assert.notNull(stream, "file not exists");
            List<String> lines = IOUtils.readLines(stream);
            script = String.join(System.lineSeparator(), lines);
            log.info("windowRateLimitScript load success {}", script);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new LuaScriptLoadException(e);
        }
    }

    public String getScript() {
        return this.script;
    }
} 