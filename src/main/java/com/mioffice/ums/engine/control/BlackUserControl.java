package com.mioffice.ums.engine.control;

import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 黑名单控制(后面做成通用的处理)
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.12
 */
@Slf4j
@Component
public class BlackUserControl {

    /**
     * 飞书渠道的用户 黑名单
     */
    private static final Set<String> LARK_USERNAME_LIST = new HashSet<>();

    static {
        /**
         * 暂时关闭受总的 lark 消息
         */
//        LARK_USERNAME_LIST.add("s");
    }

    public void control(List<MessageUserInfo> messageUserInfoList) {

        messageUserInfoList.forEach(user -> {
            if (user.getChannel().equals(MessageChannelEnum.MI_WORK.getType()) && LARK_USERNAME_LIST.contains(user.getUsername())) {
                user.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
            }
        });
    }

}
