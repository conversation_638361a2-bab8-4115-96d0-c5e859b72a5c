package com.mioffice.ums.engine.control;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.engine.entity.info.DevUserInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.mapper.DevUserInfoMapper;
import com.mioffice.ums.engine.utils.AesUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.03
 */
@Profile({"local"})
@Component
public class DevUserSendControl {

    private final DevUserInfoMapper devUserInfoMapper;

    public DevUserSendControl(DevUserInfoMapper devUserInfoMapper) {
        this.devUserInfoMapper = devUserInfoMapper;
    }

    public void filter(List<MessageUserInfo> userInfoList) {
        if (userInfoList.isEmpty()) {
            return;
        }

        List<String> usernameList = userInfoList.stream().map(MessageUserInfo::getUsername).collect(Collectors.toList());

        List<DevUserInfo> devUserInfos = devUserInfoMapper.selectList(Wrappers.<DevUserInfo>lambdaQuery().in(DevUserInfo::getUsername, usernameList));
        Set<String> devUsernameList = devUserInfos.stream().map(DevUserInfo::getUsername).collect(Collectors.toSet());

        for (MessageUserInfo messageUserInfo : userInfoList) {

            if (messageUserInfo.getChannel().equals(MessageChannelEnum.MI_WORK.getType())) {
                continue;
            }

            if (!devUsernameList.contains(messageUserInfo.getUsername())) {
                messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                messageUserInfo.setErrorLog("不在可发送范围名单内");
            }
        }
    }

    public void filterByChannel(List<MessageUserInfo> userInfoList) {

        List<MessageUserInfo> smsUserList = new ArrayList<>();
        List<MessageUserInfo> emailUserList = new ArrayList<>();
        userInfoList.forEach(userInfo -> {
            if (MessageChannelEnum.SMS.getType() == userInfo.getChannel()) {
                smsUserList.add(userInfo);
            } else if (MessageChannelEnum.EMAIL.getType() == userInfo.getChannel()) {
                emailUserList.add(userInfo);
            }
        });

        // 短信
        if (!smsUserList.isEmpty()) {

            List<DevUserInfo> devUserInfos = devUserInfoMapper.selectList(
                    Wrappers.<DevUserInfo>lambdaQuery()
                            .in(DevUserInfo::getPhone, smsUserList.stream().map(MessageUserInfo::getPhone).map(this::decryptPhone).collect(Collectors.toList()))
            );

            List<String> phoneList = devUserInfos.stream().map(DevUserInfo::getPhone).map(AesUtils::encryptNoErr).collect(Collectors.toList());
            for (MessageUserInfo messageUserInfo : smsUserList) {
                if (!phoneList.contains(messageUserInfo.getPhone())) {
                    messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                    messageUserInfo.setErrorLog("不在可发送范围名单内");
                }
            }
        }

        // 邮件
        if (!emailUserList.isEmpty()) {
            List<DevUserInfo> devUserInfos = devUserInfoMapper.selectList(
                    Wrappers.<DevUserInfo>lambdaQuery()
                            .in(DevUserInfo::getEmail, emailUserList.stream().map(MessageUserInfo::getEmail).collect(Collectors.toList()))
            );
            List<String> emailList = devUserInfos.stream().map(DevUserInfo::getEmail).collect(Collectors.toList());
            for (MessageUserInfo messageUserInfo : emailUserList) {
                if (!emailList.contains(messageUserInfo.getEmail())) {
                    messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                    messageUserInfo.setErrorLog("不在可发送范围名单内");
                }
            }
        }


    }

    public String decryptPhone(String encrypt) {
        try {
            return AesUtils.decrypt(encrypt);
        } catch (Exception e) {
            return "";
        }
    }
}
