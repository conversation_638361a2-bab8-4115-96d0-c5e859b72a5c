package com.mioffice.ums.engine.controller;

import com.mioffice.ums.engine.utils.KeyCenterUtil;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * @since 2020/11/25 上午11:29
 * version: 1.0.0
 */
@RestController
public class HealthCheckController {

    @GetMapping("/health")
    public int health() {

        int resultCode = -1;
        try {
            String word = "58f56ffc-ee01-ce48-1dd8-071501f34826";
            String encrypted = KeyCenterUtil.encrypt(word);
            String decrypted = KeyCenterUtil.decrypt(encrypted);
            if (decrypted.equals(word)) {
                resultCode = 0;
            }
        } catch (DataProtectionException e) {
            resultCode = 1;
        }
        return resultCode;

    }
}
