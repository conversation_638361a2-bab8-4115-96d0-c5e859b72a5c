package com.mioffice.ums.engine.controller;

import com.mioffice.ums.engine.entity.bo.DecryptStringBO;
import com.mioffice.ums.engine.utils.KeyCenterUtil;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/5 8:15 下午
 * version: 1.0.0
 */
@RestController
@Slf4j
@RequestMapping("/test")
public class HelloController {

    @GetMapping("/hello")
    public void hello(
            @RequestParam("word") String word

            ) {
        try {
            String encrypted = KeyCenterUtil.encrypt(word);
            log.info("encrypted[{}]", encrypted);
            String decrypted = KeyCenterUtil.decrypt(encrypted);
            log.info("decrypted[{}]", decrypted);
        } catch (DataProtectionException e) {
            log.error("加解密异常", e);
        }

    }

    @PostMapping("/decrypt")
    public void decrypt(
            @RequestBody DecryptStringBO decryptStringBO
            ) {
        try {
            String decrypted = KeyCenterUtil.decrypt(decryptStringBO.getDecryptSt());
            log.info("decrypted[{}]", decrypted);
        } catch (DataProtectionException e) {
            log.error("加解密异常=[{}]", decryptStringBO.getDecryptSt(), e);
        }

    }

}
