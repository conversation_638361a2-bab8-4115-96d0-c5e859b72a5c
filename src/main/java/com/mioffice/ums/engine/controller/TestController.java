package com.mioffice.ums.engine.controller;

import com.mioffice.ums.engine.manager.EmailRobotManager;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import com.mioffice.ums.engine.manager.SmsRobotManager;
import com.mioffice.ums.engine.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.14
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    private final MessageService messageService;

    private final MiWorkRobotManager miWorkRobotManager;

    private final EmailRobotManager emailRobotManager;

    private final SmsRobotManager smsRobotManager;

    public TestController(MessageService messageService,
                          MiWorkRobotManager miWorkRobotManager,
                          EmailRobotManager emailRobotManager,
                          SmsRobotManager smsRobotManager) {
        this.messageService = messageService;
        this.miWorkRobotManager = miWorkRobotManager;
        this.emailRobotManager = emailRobotManager;
        this.smsRobotManager = smsRobotManager;
    }

    @GetMapping("/testSendRetryMessage")
    public Map<String, Object> testSendRetryMessage() {
        messageService.retrySendFailMsg();

        Map<String, Object> map = new HashMap<>(1);
        map.put("code", 200);

        return map;
    }


    @GetMapping("/testSendingMessage")
    public Map<String, Object> testSendingMessage() {
        messageService.retrySendingMsg(null);

        Map<String, Object> map = new HashMap<>(1);
        map.put("code", 200);

        return map;
    }

    @GetMapping("/printRobotCache")
    public Set printRobotCache(@RequestParam(required = false,defaultValue = "1") Integer type){
        switch (type){
            case 1:
                return miWorkRobotManager.effectiveRobot();
            case 2:
                return emailRobotManager.effectiveRobot();
            case 3:
                return smsRobotManager.effectiveRobot();
            default:
                return miWorkRobotManager.effectiveRobot();
        }
    }

}
