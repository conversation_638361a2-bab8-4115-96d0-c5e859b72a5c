package com.mioffice.ums.engine.entity.bo;

import com.larksuite.appframework.sdk.client.message.card.module.Img;
import com.larksuite.appframework.sdk.client.message.card.objects.Text;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public class CardImgBO extends Img {
    private String mode;

    public CardImgBO(String imgKey, Text alt, Text title, String mode) {
        super(imgKey, alt, title);
        this.mode = mode;
    }

    @Override
    public Object toObjectForJson() {

        Map<String, Object> obj = (Map<String, Object>) super.toObjectForJson();
        if (StringUtils.isNotBlank(mode)) {
            obj.put("mode", mode);
        }

        return obj;
    }
}
