package com.mioffice.ums.engine.entity.bo;

import lombok.Data;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/16 8:02 下午
 * version: 1.0.0
 */
@Data
public class MessageGroupDeptRecordBO {
    private String miDeptLevel2;
    // 一级部门
    private String miDeptLevel2Desc;
    // 二级部门
    private String miDeptLevel3;
    private String miDeptLevel3Desc;
    // 三级部门
    private String miDeptLevel4;
    private String miDeptLevel4Desc;
    // 消息推送总数
    private Long allCount;
    // 已推送成功消息数量
    private Long pushCount;
    // 发送中消息数量
    private Long todoCount;
    // 发送失败消息数量
    private Long failCount;
    // 发送中断消息数量
    private Long interruptCount;
    // 已撤回的消息数量
    private Long retractCount;
}
