package com.mioffice.ums.engine.entity.bo;

import com.mioffice.ums.engine.exceptions.LimitException;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/10/16
 */
@Data
public class RateConfigBO {
    private Integer rate;
    private Long rateMs;

    public static class Builder {
        RateConfigBO instance = new RateConfigBO();

        public Builder rate(Integer rate) {
            instance.setRate(rate);
            return this;
        }

        public Builder rateMs(Long rateMs) {
            instance.setRateMs(rateMs);
            return this;
        }

        public RateConfigBO build() {
            if (instance.getRateMs() / instance.getRate() < 1) {
                // 大于 1个每毫秒
                throw new LimitException("faster than 1s 1000");
            }
            return instance;
        }
    }

    public static Builder builder() {
        return new Builder();
    }
}
