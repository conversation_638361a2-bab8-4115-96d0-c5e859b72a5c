package com.mioffice.ums.engine.entity.bo;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2020/9/1
 */
@Getter
@Setter
@ToString
public class SmsResponseBO extends MsgResponse {
    /**
     * {"msgid":"1509527311085-4652904681433523400","desc":"success","errorCode":0}
     */
    @SerializedName(value = "msgId", alternate = {"msgid", "messageId"})
    private String msgId;

    private String desc;

    private Integer errorCode;
}
