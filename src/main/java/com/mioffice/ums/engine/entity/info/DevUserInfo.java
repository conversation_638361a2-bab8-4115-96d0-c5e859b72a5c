package com.mioffice.ums.engine.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.29 
 */

/**
 * 测试环境发送范围表
 */
@Data
@TableName(value = "dev_user_info")
public class DevUserInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * email
     */
    @TableField(value = "email")
    private String email;

    /**
     * username
     */
    @TableField(value = "username")
    private String username;

    /**
     * 更新者username
     */
    @TableField(value = "update_username")
    private String updateUsername;

    /**
     * 创建者username
     */
    @TableField(value = "create_username")
    private String createUsername;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    /**
     * 手机号
     */
    @TableField(value = "phone")
    private String phone;
}