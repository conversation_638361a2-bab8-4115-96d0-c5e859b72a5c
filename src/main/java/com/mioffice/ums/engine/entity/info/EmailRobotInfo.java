package com.mioffice.ums.engine.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.09 
 */

/**
 * 邮箱机器人表
 */
@Data
@TableName(value = "email_robot_info")
public class EmailRobotInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型 1 内部邮箱，2 外部邮箱
     */
    @TableField(value = "type")
    private Byte type;

    /**
     * 邮箱sender
     */
    @TableField(value = "sender")
    private String sender;

    /**
     * 邮箱token
     */
    @TableField(value = "token")
    private String token;

    /**
     * 机器人sender
     */
    @TableField(value = "sender_name")
    private String senderName;

    /**
     * 邮件地址
     */
    @TableField(value = "mail_host")
    private String mailHost;

    /**
     * 停用状态（1-开启, 2-停用）
     */
    @TableField(value = "stop_flag")
    private Byte stopFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;
}