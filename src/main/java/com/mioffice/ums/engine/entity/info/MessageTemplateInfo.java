package com.mioffice.ums.engine.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mioffice.ums.engine.enums.I18nEnum;
import com.mioffice.ums.engine.enums.ResolveReadyEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.22
 */

/**
 * 消息模板表
 */
@Data
@TableName(value = "message_template_info")
public class MessageTemplateInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息标题(中文)
     */
    @TableField(value = "title_cn")
    private String titleCn;

    /**
     * 消息标题(英文)
     */
    @TableField(value = "title_en")
    private String titleEn;

    /**
     * 消息内容(中文)
     */
    @TableField(value = "content_cn")
    private String contentCn;

    /**
     * 消息内容(英文)
     */
    @TableField(value = "content_en")
    private String contentEn;

    /**
     * 消息类型（1-飞书，2-邮件，3-短信，4-miPush）
     */
    @TableField(value = "channel")
    private Byte channel;

    /**
     * 机器人id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 按钮  [{"name": "", "landingUrl":"", "val": {"key": "val"} }]
     */
    @TableField(value = "card_actions")
    private String cardActions;

    /**
     * 图片id:imageKey {"123iuy21u312890831": "img_18290830123"}
     */
    @TableField(value = "images")
    private String images;

    /**
     * saas图片id
     */
    @TableField(value = "saas_images")
    private String saasImages;

    /**
     * 图片显示模式
     */
    @TableField(value = "image_mode")
    private String imageMode;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    /**
     * 系统id
     */
    @TableField(value = "system_id")
    private String systemId;

    /**
     * 1 SDK格式消息， 2 json格式消息
     */
    @TableField(value = "msg_format_type")
    private Byte msgFormatType;

    /**
     * 卡片按钮cn
     */
    @TableField(value = "card_actions_cn")
    private String cardActionsCn;

    /**
     * 卡片按钮en
     */
    @TableField(value = "card_actions_en")
    private String cardActionsEn;

    /**
     * 卡片主题
     */
    @TableField(value = "theme")
    private String theme;

    /**
     * 是否预处理完毕
     */
    @TableField(value = "resolve_ready")
    private Byte resolveReady;

    public static com.mioffice.ums.engine.entity.info.MessageTemplateInfo newCreateAndUpdateTimeInstant() {
        com.mioffice.ums.engine.entity.info.MessageTemplateInfo messageTemplateInfo = new com.mioffice.ums.engine.entity.info.MessageTemplateInfo();
        long time = System.currentTimeMillis();
        messageTemplateInfo.setCreateTime(time);
        messageTemplateInfo.setUpdateTime(time);
        messageTemplateInfo.setResolveReady(ResolveReadyEnum.YES.getCode());
        return messageTemplateInfo;
    }

    public static com.mioffice.ums.engine.entity.info.MessageTemplateInfo newUpdateTimeInstant() {
        com.mioffice.ums.engine.entity.info.MessageTemplateInfo messageTemplateInfo = new com.mioffice.ums.engine.entity.info.MessageTemplateInfo();
        messageTemplateInfo.setUpdateTime(System.currentTimeMillis());
        return messageTemplateInfo;
    }

    public String getI18nContent(I18nEnum i18nEnum) {

        if (i18nEnum == I18nEnum.zh_cn) {
            return this.contentCn;
        } else {
            return this.contentEn;
        }

    }

    public String getI18nTitle(I18nEnum i18nEnum) {
        if (i18nEnum == I18nEnum.zh_cn) {
            return this.titleCn;
        } else {
            return this.titleEn;
        }
    }

    public boolean hasCardActions() {
        return StringUtils.isNotBlank(this.cardActions) || StringUtils.isNotBlank(this.cardActionsCn) || StringUtils.isNotBlank(this.cardActionsEn);
    }


    public String getCardActions(I18nEnum i18nEnum) {
        if (i18nEnum == I18nEnum.zh_cn) {
            return this.cardActionsCn;
        } else {
            return this.cardActionsEn;
        }
    }
}
