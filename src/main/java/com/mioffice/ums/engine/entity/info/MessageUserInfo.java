package com.mioffice.ums.engine.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.02
 */

/**
 * 消息用户表
 */
@Data
@TableName(value = "message_user_info")
public class MessageUserInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机器人id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 消息模板id
     */
    @TableField(value = "msg_tmp_id")
    private Long msgTmpId;

    /**
     * openId
     */
    @TableField(value = "open_id")
    private String openId;

    /**
     * email
     */
    @TableField(value = "email")
    private String email;

    /**
     * username
     */
    @TableField(value = "username")
    private String username;

    /**
     * phone
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 二级部门ID
     */
    @TableField(value = "mi_dept_level2")
    private String miDeptLevel2;

    /**
     * 二级部门描述
     */
    @TableField(value = "mi_dept_level2_desc")
    private String miDeptLevel2Desc;

    /**
     * 三级部门ID
     */
    @TableField(value = "mi_dept_level3")
    private String miDeptLevel3;

    /**
     * 三级部门描述
     */
    @TableField(value = "mi_dept_level3_desc")
    private String miDeptLevel3Desc;

    /**
     * 消息类型（1-飞书，2-邮件，3-短信，4-miPush）
     */
    @TableField(value = "channel")
    private Byte channel;

    /**
     * 消息状态(1-发送中，2-发送成功，3-发送失败)
     */
    @TableField(value = "message_status")
    private Byte messageStatus;

    /**
     * 消息结果id
     */
    @TableField(value = "message_id")
    private String messageId;

    /**
     * 发送失败log
     */
    @TableField(value = "error_log")
    private String errorLog;

    /**
     * 失败重试
     */
    @TableField(value = "retry_count")
    private Integer retryCount;

    /**
     * 消息标题(中文)
     */
    @TableField(value = "title_cn")
    private String titleCn;

    /**
     * 消息标题(英文)
     */
    @TableField(value = "title_en")
    private String titleEn;

    /**
     * 变量数据
     */
    @TableField(value = "placeholder_content")
    private String placeholderContent;

    /**
     * 业务层扩展extra_id
     */
    @TableField(value = "extra_id")
    private String extraId;

    /**
     * 业务方自定义数据
     */
    @TableField(value = "extra_content")
    private String extraContent;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    /**
     * 内容标记（
     * 0-无，1-飞书原始内容，后续扩展）
     */
    @TableField(value = "content_flag")
    private Byte contentFlag;

    /**
     * lark的消息体内容（按需存储）
     */
    @TableField(value = "final_content")
    private String finalContent;

    /**
     * 错误类型
     */
    @TableField(value = "error_type")
    private Byte errorType;

    /**
     * 邮件抄送人，分号分离
     */
    @TableField(value = "cc_email")
    private String ccEmail;

    /**
     * 邮件附件FDS地址 格式 ["", ""]
     */
    @TableField(value = "attach_email_url")
    private String attachEmailUrl;

    /**
     * 群chatId
     */
    @TableField(value = "chat_id")
    private String chatId;

    @TableField(value = "mi_dept_level4")
    private String miDeptLevel4;

    @TableField(value = "mi_dept_level4_desc")
    private String miDeptLevel4Desc;

    @TableField(value = "read_status")
    private Byte readStatus;

    @TableField(value = "sys_id")
    private String sysId;

    @TableField(value = "is_send_saas")
    private Byte isSendSaas;

    public static com.mioffice.ums.engine.entity.info.MessageUserInfo newCreateAndUpdateTimeInstant() {
        com.mioffice.ums.engine.entity.info.MessageUserInfo messageUserInfo =
                new com.mioffice.ums.engine.entity.info.MessageUserInfo();
        long time = System.currentTimeMillis();
        messageUserInfo.setCreateTime(time);
        messageUserInfo.setUpdateTime(time);
        return messageUserInfo;
    }

    public static com.mioffice.ums.engine.entity.info.MessageUserInfo newUpdateTimeInstant() {
        com.mioffice.ums.engine.entity.info.MessageUserInfo messageUserInfo =
                new com.mioffice.ums.engine.entity.info.MessageUserInfo();
        messageUserInfo.setUpdateTime(System.currentTimeMillis());
        return messageUserInfo;
    }
}
