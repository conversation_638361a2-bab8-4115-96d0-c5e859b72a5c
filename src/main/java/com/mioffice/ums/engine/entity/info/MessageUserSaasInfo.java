package com.mioffice.ums.engine.entity.info;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息用户表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "message_user_saas_info")
public class MessageUserSaasInfo extends MessageUserInfo {
    /**
     * 用户消息id
     */
    @TableField(value = "user_info_id")
    private Long userInfoId;

    @TableField(value = "is_v1_channel")
    private Byte isV1Channel;
}
