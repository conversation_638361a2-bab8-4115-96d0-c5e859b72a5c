package com.mioffice.ums.engine.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.08.22 
 */

/**
 * 机器人表
 */
@Data
@TableName(value = "robot_info")
public class RobotInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 机器人id
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 机器人secret
     */
    @TableField(value = "app_secret")
    private String appSecret;

    /**
     * 机器人appShortName
     */
    @TableField(value = "app_short_name")
    private String appShortName;

    /**
     * 机器人encryptKey
     */
    @TableField(value = "encrypt_key")
    private String encryptKey;

    /**
     * 机器人verificationToken
     */
    @TableField(value = "verification_token")
    private String verificationToken;

    /**
     * 停用状态（1-开启, 2-停用）
     */
    @TableField(value = "stop_flag")
    private Byte stopFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;

    public static com.mioffice.ums.engine.entity.info.RobotInfo newUpdateTimeInstant() {
        long time = System.currentTimeMillis();
        com.mioffice.ums.engine.entity.info.RobotInfo robotInfo = new com.mioffice.ums.engine.entity.info.RobotInfo();
        robotInfo.setUpdateTime(time);
        return robotInfo;
    }

    public static com.mioffice.ums.engine.entity.info.RobotInfo newCreateAndUpdateTimeInstant() {
        long time = System.currentTimeMillis();
        com.mioffice.ums.engine.entity.info.RobotInfo robotInfo = new com.mioffice.ums.engine.entity.info.RobotInfo();
        robotInfo.setUpdateTime(time);
        robotInfo.setCreateTime(time);
        return robotInfo;
    }
}