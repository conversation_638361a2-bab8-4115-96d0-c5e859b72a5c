package com.mioffice.ums.engine.entity.info;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.02 
 */
/**
    * 短信机器人表
    */
@Data
@TableName(value = "sms_robot_info")
public class SmsRobotInfo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 短信模板号
     */
    @TableField(value = "content_type_key")
    private String contentTypeKey;

    /**
     * 短信签名
     */
    @TableField(value = "sign")
    private String sign;

    /**
     * 停用状态（1-开启, 2-停用）
     */
    @TableField(value = "stop_flag")
    private Byte stopFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Long updateTime;
}