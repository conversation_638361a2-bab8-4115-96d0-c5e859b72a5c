package com.mioffice.ums.engine.enums;

import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.01
 */
@Getter
public enum EmailRobotTypeEnum {
    /**
     * 类型
     */
    INNER((byte) 1, "内部机器人"), OUT((byte) 2, "外部机器人");

    private final byte type;
    private final String desc;

    EmailRobotTypeEnum(byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
