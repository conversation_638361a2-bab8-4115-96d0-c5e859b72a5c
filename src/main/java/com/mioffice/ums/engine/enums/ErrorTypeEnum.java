package com.mioffice.ums.engine.enums;

import lombok.Getter;

/**
 * <p>
 * 错误类型
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.04
 */
@Getter
public enum ErrorTypeEnum {

    /**
     * 后面继续添加
     */

    NO_KNOW((byte) 0, "0", "未知错误码"),

    LARK_USER_NOT_EXIST((byte) 1, "11105", "飞书系统不存在该用户"),

    LARK_USER_INVISIBLE((byte) 2, "11225", "用户范围不可见"),

    LARK_CARD_ERR((byte) 3, "11246", "卡片内容格式错误"),
    LARK_USER_NOT_FOUND((byte) 4, "10004", "找不到该用户"),
    BOT_NOT_IN_CHAT((byte) 5, "10030", "机器人未加入群"),
    CHAT_DISSOLVED((byte) 6, "232009", "群已取消"),
    ;

    private final byte type;
    private final String code;
    private final String desc;

    ErrorTypeEnum(byte type, String code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public static ErrorTypeEnum getByCode(String code) {
        for (ErrorTypeEnum value : ErrorTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }

        return NO_KNOW;
    }
}
