package com.mioffice.ums.engine.enums;

import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.21
 */
@Getter
public enum I18nEnum {

    /**
     * 国际化
     */
    zh_cn("zh_cn", "中文"),
    en_us("en_us", "英文"),
    ja_jp("ja_jp", "日语");

    private final String i18n;
    private final String desc;

    I18nEnum(String i18n, String desc) {
        this.i18n = i18n;
        this.desc = desc;
    }

}
