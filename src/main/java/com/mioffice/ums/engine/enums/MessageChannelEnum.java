package com.mioffice.ums.engine.enums;

import lombok.Getter;

/**
 * <p>
 * 消息类型枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.12
 */
@Getter
public enum MessageChannelEnum {

    /**
     * 消息类型
     */
    NO((byte) 0, "错误消息类型"),
    MI_WORK((byte) 1, "飞书"),
    EMAIL((byte) 2, "邮件"),
    SMS((byte) 3, "短信"),
    MI_PUSH((byte) 4, "miPush"),
    MI_WORK_SAAS((byte) 99, "飞书SAAS"),
    ;

    private final byte type;
    private final String desc;

    MessageChannelEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MessageChannelEnum get(byte type) {
        MessageChannelEnum[] values = MessageChannelEnum.values();
        for (MessageChannelEnum value : values) {
            if (value.getType() == type) {
                return value;
            }
        }

        return NO;
    }
}
