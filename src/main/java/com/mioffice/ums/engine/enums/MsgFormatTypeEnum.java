package com.mioffice.ums.engine.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.17
 */
@AllArgsConstructor
@Getter
public enum MsgFormatTypeEnum {

    /**
     * 消息格式
     */
    NO((byte) 0, "不存在格式"),
    SDK((byte) 1, "sdk消息体格式"), JSON((byte) 2, "json消息体格式");

    private final Byte type;
    private final String desc;

    public static MsgFormatTypeEnum get(Byte type) {
        for (MsgFormatTypeEnum msgFormatTypeEnum : MsgFormatTypeEnum.values()) {
            if (msgFormatTypeEnum.getType().equals(type)) {
                return msgFormatTypeEnum;
            }
        }

        return NO;
    }
}
