package com.mioffice.ums.engine.enums.constants;

/**
 * <AUTHOR>
 * @since 2020/8/27
 */
public class RedisConstants {

    /**
     * hash
     * key: ums:engine:rate:limit
     * field: MessageProcessor#getRateLimitKey
     * v: timestamp ms
     * <p>
     * field: MessageProcessor#getRateLimitKey + rate
     * v: int
     */
    public static final String RATE_LIMIT_KEY = "ums:engine:rate:limit";

    public static final String RATE_LIMIT_FIELD = "rate";

    public static final String RATE_MS_FIELD = "rate_ms";

    public static final String TEMPLATE_ID_FIELD = "template_id";

    public static final String LARK_MSG_READ_SCAN_START_ID = "larkMsgReadScanStartId";

    public static final String LARK_MSG_UNREAD_APPID_LIST = "larkMsgUnReadAppIdList";

    public static final String TOPIC_FIELD = "topic";

    public static final String TOPIC_PRODUCER_GROUP = "ums-engine-producer";

    public static final String TOPIC_CONSUMER_GROUP = "ums-engine-consumer";

    private RedisConstants() {
    }
}
