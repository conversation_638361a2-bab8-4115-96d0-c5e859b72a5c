package com.mioffice.ums.engine.enums.constants;

/**
 * <p>
 * 更新信令动作
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.13
 */
public class SingleActionConstants {

    /**
     * 更新机器人动作，加载到内存
     */
    public static final String SYNC_ROBOT_ACTION = "sync-robot-action";

    /**
     * 更新中断消息extraId，加载到内存
     */
    public static final String SYNC_MSG_FILTER_ACTION = "sync-msg-filter-action";

    /**
     * 限流调整config
     */
    public static final String SYNC_RATE_LIMIT_CONFIG_ACTION = "sync-msg-rate-limit-config-action";


    private SingleActionConstants() {
    }
}
