package com.mioffice.ums.engine.event;

import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import lombok.Getter;

import java.util.List;


/**
 * <AUTHOR>
 */
@Getter
public class SendMessageEvent {

    private MessageChannelEnum messageChannelEnum;
    private List<MessageUserInfo> userInfoList;

    public SendMessageEvent(MessageChannelEnum messageChannelEnum, List<MessageUserInfo> userInfoList) {
        this.userInfoList = userInfoList;
        this.messageChannelEnum = messageChannelEnum;
    }

}