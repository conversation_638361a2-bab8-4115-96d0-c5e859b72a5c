package com.mioffice.ums.engine.event;

import com.mioffice.ums.engine.cache.MessageFilterCache;
import com.mioffice.ums.engine.enums.constants.SingleActionConstants;
import com.mioffice.ums.engine.manager.EmailRobotManager;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import com.mioffice.ums.engine.manager.SmsRobotManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.13
 */
@Slf4j
@Component
public class SettingEventDispatcher {

    private final MiWorkRobotManager miWorkRobotManager;

    private final EmailRobotManager emailRobotManager;

    private final SmsRobotManager smsRobotManager;

    private final MessageFilterCache messageFilterCache;

    public SettingEventDispatcher(MiWorkRobotManager miWorkRobotManager, MessageFilterCache messageFilterCache,
                                  EmailRobotManager emailRobotManager, SmsRobotManager smsRobotManager) {
        this.miWorkRobotManager = miWorkRobotManager;
        this.messageFilterCache = messageFilterCache;
        this.emailRobotManager = emailRobotManager;
        this.smsRobotManager = smsRobotManager;
    }

    /**
     * 配置更新
     *
     * @param singleEvent
     */
    public void dispatch(SingleEvent singleEvent) {

        switch (singleEvent.getAction()) {
            // 同步机器人
            case SingleActionConstants.SYNC_ROBOT_ACTION:
                miWorkRobotManager.loadRobot(singleEvent.getIdList());
                emailRobotManager.loadRobot(singleEvent.getIdList());
                smsRobotManager.loadRobot(singleEvent.getIdList());
                break;
            case SingleActionConstants.SYNC_MSG_FILTER_ACTION:
                messageFilterCache.load();
                break;
            case SingleActionConstants.SYNC_RATE_LIMIT_CONFIG_ACTION:
                // TODO update limiter
                break;
            default:
                log.error("更新配置，未知动作 action = [{}]", singleEvent.getAction());
        }
    }
}
