package com.mioffice.ums.engine.event.bus;

import cn.hutool.core.thread.ExecutorBuilder;
import com.google.common.eventbus.AsyncEventBus;
import com.google.common.eventbus.EventBus;
import com.google.common.eventbus.Subscribe;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.event.SendMessageEvent;
import com.mioffice.ums.engine.event.SingleEvent;
import com.mioffice.ums.engine.talos.TalosConstant;
import com.mioffice.ums.engine.talos.TalosHelper;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.xiaomi.infra.galaxy.talos.client.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.12
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class SendMqEventBus {

    private final EventBus eventBus = new AsyncEventBus("sendMessageEventBus", ExecutorBuilder.create()
            .setCorePoolSize(100)
            .setMaxPoolSize(100 * 2)
            .setWorkQueue(new LinkedBlockingQueue<>(200 * 1024))
            .setThreadFactory(new NamedThreadFactory("eventbus-to-talos"))
            .buildFinalizable());

    private final TalosHelper talosHelper;

    public SendMqEventBus(TalosHelper talosHelper) {
        this.talosHelper = talosHelper;
    }

    @PostConstruct
    public void init() {
        eventBus.register(new SendMessageListener(talosHelper));
    }

    public void postEvent(Object event) {
        this.eventBus.post(event);
    }

    public static class SendMessageListener {

        private final TalosHelper talosHelper;

        public SendMessageListener(TalosHelper talosHelper) {
            this.talosHelper = talosHelper;
        }

        @Subscribe
        public void listen(SendMessageEvent sendMessageEvent) {
            List<MessageUserInfo> userInfoList = sendMessageEvent.getUserInfoList();
            log.info("投递消息到MQ, userInfoList = [{}]", JsonUtils.toJson(userInfoList));

            List<String> dataList = userInfoList.stream().map(p -> JsonUtils.toJson(p)).collect(Collectors.toList());

            MessageChannelEnum messageChannelEnum = sendMessageEvent.getMessageChannelEnum();
            try {
                switch (messageChannelEnum) {
                    case MI_WORK:
                        talosHelper.produce(TalosConstant.TOPIC_NAME_MI_WORK, dataList);
                        break;
                    case EMAIL:
                        talosHelper.produce(TalosConstant.TOPIC_NAME_EMAIL, dataList);
                        break;
                    case SMS:
                        talosHelper.produce(TalosConstant.TOPIC_NAME_SMS, dataList);
                        break;
                    default:
                        log.error("投递消息渠道不支持, userInfoList = [{}]", JsonUtils.toJson(userInfoList));
                }
            } catch (Exception e) {
                log.error("talos 投递消息失败", e);
            }
        }

        @Subscribe
        public void listen(SingleEvent singleEvent) {
            try {
                log.info("发送信令广播 sendMessageEvent = [{}]", JsonUtils.toJson(singleEvent));
                List<String> list = new ArrayList<>();
                list.add(JsonUtils.toJson(singleEvent));
                talosHelper.produce(TalosConstant.TOPIC_NAME_SETTING, list);
            } catch (Exception e) {
                log.error("talos 投递消息失败", e);
            }
        }
    }
}
