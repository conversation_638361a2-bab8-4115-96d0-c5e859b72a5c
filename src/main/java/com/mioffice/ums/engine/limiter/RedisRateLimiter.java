package com.mioffice.ums.engine.limiter;

import com.mioffice.ums.engine.entity.bo.RateConfigBO;
import com.mioffice.ums.engine.enums.constants.RedisConstants;
import com.mioffice.ums.engine.exceptions.LimitException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * <AUTHOR>
 * @since 2020/8/27
 */
@Slf4j
public class RedisRateLimiter {

    private final RedissonClient redissonClient;
    private final List<Object> keys = new ArrayList<>(16);
    private final String rateLimitKey;
    private final String script;
    private final String rateConfField;
    private final String rateMsConfField;

    private Long rateCorrespondingMs = 1000L;

    private Long rate = 50L;

    public RedisRateLimiter(RedissonClient redissonClient, String rateLimitKey, String script) {
        this.redissonClient = redissonClient;
        this.rateLimitKey = rateLimitKey;
        this.script = script;
        this.rateConfField = String.format("%s-%s", rateLimitKey, RedisConstants.RATE_LIMIT_FIELD);
        this.rateMsConfField = String.format("%s-%s", rateLimitKey, RedisConstants.RATE_MS_FIELD);
        start();
    }

    public RedisRateLimiter(RedissonClient redissonClient, String rateLimitKey, String script, Long rateCorrespondingMs,
                            Long rate) {
        this.redissonClient = redissonClient;
        this.rateLimitKey = rateLimitKey;
        this.script = script;
        this.rateConfField = String.format("%s-%s", rateLimitKey, RedisConstants.RATE_LIMIT_FIELD);
        this.rateMsConfField = String.format("%s-%s", rateLimitKey, RedisConstants.RATE_MS_FIELD);
        this.rateCorrespondingMs  =rateCorrespondingMs;
        this.rate = rate;
        start();
    }

    public void acquire(long timeout) throws LimitException {
        if (timeout < 0) {
            timeout = 0;
        }
        long ms = redissonClient.getScript(StringCodec.INSTANCE).eval(
                RScript.Mode.READ_WRITE,
                script,
                RScript.ReturnType.INTEGER,
                keys,
                System.currentTimeMillis()
        );
        if (timeout > 0 && ms > timeout) {
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(timeout));
            throw new LimitException(String.format("timeout: %d target: %d", timeout, ms));
        }
        if (ms > 0) {
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(ms));
        }
        log.debug("等待令牌桶的时间: {} ms, rateLimitKey: {}", ms, rateLimitKey);
    }

    public long acquire() {
        return redissonClient.getScript(StringCodec.INSTANCE).eval(
                RScript.Mode.READ_WRITE,
                script,
                RScript.ReturnType.INTEGER,
                keys,
                System.currentTimeMillis()
        );
    }

    /**
     * interval = rate_ms / rate
     *
     * @return
     */
    public Long getIntervalMs() {
        return rateCorrespondingMs / rate;
    }

    public void start() {
        keys.add(RedisConstants.RATE_LIMIT_KEY);
        keys.add(rateLimitKey);
        keys.add(rateConfField);
        keys.add(rateMsConfField);

        RMap<String, String> map = redissonClient.getMap(RedisConstants.RATE_LIMIT_KEY, StringCodec.INSTANCE);
        if (!map.containsKey(RedisConstants.RATE_LIMIT_FIELD)) {
            map.put(rateConfField, String.valueOf(rate));
            map.put(rateMsConfField, String.valueOf(rateCorrespondingMs));
        }
    }

    public void setRate(RateConfigBO rateConf) {
        if (rateConf == null) {
            return;
        }
        RMap<String, String> map = redissonClient.getMap(RedisConstants.RATE_LIMIT_KEY, StringCodec.INSTANCE);
        if (rateConf.getRate() > 0) {
            map.put(rateConfField, String.valueOf(rateConf.getRate()));
        }
        if (rateConf.getRateMs() > 0) {
            map.put(rateMsConfField, String.valueOf(rateConf.getRateMs()));
        }
    }
}
