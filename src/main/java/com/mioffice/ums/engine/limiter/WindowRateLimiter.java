package com.mioffice.ums.engine.limiter;

import com.mioffice.ums.engine.exceptions.LimitException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScript;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;

import java.util.Collections;
import java.util.List;

/**
 * 时间窗口限流器
 * 实现基于appId维度的时间窗口限流：
 * - 1秒内最多50个请求
 * - 1分钟内最多1000个请求
 * 
 * <AUTHOR>
 * @since 2020/8/27
 */
@Slf4j
public class WindowRateLimiter {

    private final RedissonClient redissonClient;
    private final String script;
    private final int secondLimit;
    private final int minuteLimit;

    public WindowRateLimiter(RedissonClient redissonClient, String script) {
        this(redissonClient, script, 50, 1000);
    }

    public WindowRateLimiter(RedissonClient redissonClient, String script, int secondLimit, int minuteLimit) {
        this.redissonClient = redissonClient;
        this.script = script;
        this.secondLimit = secondLimit;
        this.minuteLimit = minuteLimit;
    }

    /**
     * 尝试获取许可
     * 
     * @param appId 应用ID
     * @return true 获取成功，false 被限流
     * @throws LimitException 当被限流时抛出异常
     */
    public boolean tryAcquire(String appId) throws LimitException {
        List<Object> result = redissonClient.getScript(StringCodec.INSTANCE).eval(
                RScript.Mode.READ_WRITE,
                script,
                RScript.ReturnType.MULTI,
                Collections.singletonList(appId),
                System.currentTimeMillis(),
                secondLimit,
                minuteLimit
        );

        if (result == null || result.isEmpty()) {
            throw new LimitException("Script execution failed");
        }

        Long success = (Long) result.get(0);
        String message = (String) result.get(1);
        Long currentCount = result.size() > 2 ? (Long) result.get(2) : 0L;
        Long limitValue = result.size() > 3 ? (Long) result.get(3) : 0L;

        if (success == 0) {
            log.warn("Rate limit exceeded for appId: {}, reason: {}, current: {}, limit: {}", 
                    appId, message, currentCount, limitValue);
            throw new LimitException(String.format("Rate limit exceeded: %s, current: %d, limit: %d", 
                    message, currentCount, limitValue));
        }

        log.debug("Rate limit check passed for appId: {}, second_count: {}, minute_count: {}", 
                appId, result.get(2), result.get(3));
        return true;
    }

    /**
     * 静默尝试获取许可，不抛出异常
     * 
     * @param appId 应用ID
     * @return true 获取成功，false 被限流
     */
    public boolean tryAcquireQuietly(String appId) {
        try {
            return tryAcquire(appId);
        } catch (LimitException e) {
            log.debug("Rate limit check failed for appId: {}, reason: {}", appId, e.getMessage());
            return false;
        }
    }

    public int getSecondLimit() {
        return secondLimit;
    }

    public int getMinuteLimit() {
        return minuteLimit;
    }
} 