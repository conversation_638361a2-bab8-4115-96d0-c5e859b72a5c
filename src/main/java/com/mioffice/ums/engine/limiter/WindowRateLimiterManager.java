package com.mioffice.ums.engine.limiter;

import com.mioffice.ums.engine.config.WindowRateLimitScriptConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 时间窗口限流器管理器
 * 
 * <AUTHOR>
 * @since 2020/8/27
 */
@Slf4j
@Component
public class WindowRateLimiterManager {

    private final ConcurrentMap<String, WindowRateLimiter> limiterCache = new ConcurrentHashMap<>();
    private final WindowRateLimitScriptConfig scriptConfig;
    private final RedissonClient redissonClient;

    public WindowRateLimiterManager(RedissonClient redissonClient, WindowRateLimitScriptConfig scriptConfig) {
        this.redissonClient = redissonClient;
        this.scriptConfig = scriptConfig;
    }

    /**
     * 获取默认配置的限流器 (1秒50个，1分钟1000个)
     * 
     * @param limitKey 限流键
     * @return 限流器实例
     */
    public WindowRateLimiter getLimiter(String limitKey) {
        return limiterCache.computeIfAbsent(limitKey, k -> {
            log.info("create new WindowRateLimiter with default config, key: {}", k);
            return new WindowRateLimiter(redissonClient, scriptConfig.getScript());
        });
    }

    /**
     * 获取自定义配置的限流器
     * 
     * @param limitKey 限流键
     * @param secondLimit 每秒限制
     * @param minuteLimit 每分钟限制
     * @return 限流器实例
     */
    public WindowRateLimiter getLimiter(String limitKey, int secondLimit, int minuteLimit) {
        String cacheKey = String.format("%s:%d:%d", limitKey, secondLimit, minuteLimit);
        return limiterCache.computeIfAbsent(cacheKey, k -> {
            log.info("create new WindowRateLimiter with custom config, key: {}, secondLimit: {}, minuteLimit: {}", 
                    limitKey, secondLimit, minuteLimit);
            return new WindowRateLimiter(redissonClient, scriptConfig.getScript(), secondLimit, minuteLimit);
        });
    }

    /**
     * 清除缓存中的限流器
     * 
     * @param limitKey 限流键
     */
    public void removeLimiter(String limitKey) {
        limiterCache.remove(limitKey);
        log.info("removed WindowRateLimiter for key: {}", limitKey);
    }

    /**
     * 清除所有缓存的限流器
     */
    public void clearAll() {
        limiterCache.clear();
        log.info("cleared all WindowRateLimiters");
    }

    /**
     * 获取缓存中的限流器数量
     * 
     * @return 限流器数量
     */
    public int getCacheSize() {
        return limiterCache.size();
    }
} 