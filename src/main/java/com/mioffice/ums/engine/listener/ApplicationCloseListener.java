package com.mioffice.ums.engine.listener;

import com.mioffice.ums.engine.talos.TalosHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Service;

/**
 * Listen for spring container closure events
 *
 * <AUTHOR>
 * @date 2020/8/11 14:15
 */
@Service
public class ApplicationCloseListener implements ApplicationListener<ContextClosedEvent> {

    private final Logger logger = LoggerFactory.getLogger(ApplicationCloseListener.class);

    @Autowired
    private TalosHelper talosHelper;

    @Override
    public void onApplicationEvent(ContextClosedEvent contextClosedEvent) {
        logger.info("Start closing talos producers and consumers...");
        talosHelper.shutdownAll();
        logger.info("Successfully closing talos producers and consumers.");
    }
}
