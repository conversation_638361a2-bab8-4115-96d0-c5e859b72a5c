package com.mioffice.ums.engine.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.mioffice.ums.engine.entity.bo.EmailRequestBO;
import com.mioffice.ums.engine.entity.bo.EmailResponseBO;
import com.mioffice.ums.engine.entity.info.EmailRobotInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.EmailRobotTypeEnum;
import com.mioffice.ums.engine.exceptions.NotFoundSenderException;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.robot.BaseEmailRobot;
import com.mioffice.ums.engine.robot.InnerEmailRobot;
import com.mioffice.ums.engine.robot.OutEmailRobot;
import com.mioffice.ums.engine.robot.Status;
import com.mioffice.ums.engine.template.TemplateParse;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.mioffice.ums.engine.utils.KeyCenterUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.01
 */
@Slf4j
public class EmailRobotManager {

    private EmailRobotInfoMapper emailRobotInfoMapper;

    private TemplateParse templateParse;

    private OutEmailRobot outEmailRobot;

    private String outGuardUrl;

    private String outGuardMultipleFileUrl;

    private final ConcurrentMap<String, BaseEmailRobot> senderMap = Maps.newConcurrentMap();

    public void init() {
        this.loadRobot();
    }

    private void loadRobot() {
        emailRobotInfoMapper.selectList(Wrappers.<EmailRobotInfo>lambdaQuery().eq(EmailRobotInfo::getStopFlag, 1))
                .forEach(this::loadRobot);

        this.outEmailRobot = new OutEmailRobot(outGuardUrl, outGuardMultipleFileUrl, null);
    }

    public void loadRobot(List<String> senders) {
        emailRobotInfoMapper.selectList(Wrappers.<EmailRobotInfo>lambdaQuery().eq(EmailRobotInfo::getStopFlag, 1)
                        .in(EmailRobotInfo::getSender, senders))
                .forEach(this::loadRobot);
    }

    private void loadRobot(EmailRobotInfo robotInfo) {
        if (EmailRobotTypeEnum.INNER.getType() == robotInfo.getType()) {
            Status status = new Status();
            BeanUtils.copyProperties(robotInfo, status);
            BaseEmailRobot emailRobot = null;
            try {
                emailRobot = new InnerEmailRobot(robotInfo.getMailHost(), KeyCenterUtil.decrypt(robotInfo.getToken()),
                        status);
                emailRobot.init();
                senderMap.put(robotInfo.getSender(), emailRobot);
            } catch (Exception e) {
                log.error("创建机器人失败 sender = [{}]", robotInfo.getSender(), e);
            }
        }
    }

    private boolean isInnerEmail(String email) {
        return email.endsWith("@xiaomi.com");
    }

    /**
     * 邮件组
     *
     * @param sender 发送机器人账号
     * @param messageUserInfo
     */
    public EmailResponseBO sendMsg(String sender, MessageUserInfo messageUserInfo) throws Exception {

        Map<String, Object> params = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());
        String content = templateParse.parseContent(messageUserInfo.getMsgTmpId(), params);
        String title = templateParse.parseTitle(messageUserInfo.getMsgTmpId(), params);

        log.info("EmailRobotManager send email, title = [{}]", title);

        EmailRequestBO emailRequestBO = new EmailRequestBO();
        emailRequestBO.setHtml(content);
        emailRequestBO.setTitle(title);
        emailRequestBO.setTo(messageUserInfo.getEmail());
        emailRequestBO.setCcTo(messageUserInfo.getCcEmail());
        emailRequestBO.setAttachUrl(messageUserInfo.getAttachEmailUrl());

        if (isInnerEmail(messageUserInfo.getEmail())) {
            assertExistRobot(sender);
            return senderMap.get(sender).sendMsg(emailRequestBO);
        } else {
            return outEmailRobot.sendMsg(emailRequestBO);
        }

    }

    private void assertExistRobot(String sender) throws NotFoundSenderException {
        if (!senderMap.containsKey(sender)) {
            throw new NotFoundSenderException();
        }
    }

    public void setEmailRobotInfoMapper(EmailRobotInfoMapper emailRobotInfoMapper) {
        this.emailRobotInfoMapper = emailRobotInfoMapper;
    }

    public void setTemplateParse(TemplateParse templateParse) {
        this.templateParse = templateParse;
    }

    public void setOutGuardUrl(String outGuardUrl) {
        this.outGuardUrl = outGuardUrl;
    }

    public void setOutGuardMultipleFileUrl(String outGuardMultipleFileUrl) {
        this.outGuardMultipleFileUrl = outGuardMultipleFileUrl;
    }

    public Set<String> effectiveRobot(){
        return senderMap.keySet();
    }
}
