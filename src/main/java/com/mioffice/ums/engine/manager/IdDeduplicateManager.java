package com.mioffice.ums.engine.manager;

import com.mioffice.ums.engine.enums.MessageChannelEnum;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * id去重
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.05
 */
public class IdDeduplicateManager {
    private RedissonClient redissonClient;

    private Map<MessageChannelEnum, RSet<Long>> sendMessageIdSetMap;

    public void init() {
        sendMessageIdSetMap = new HashMap<>();
        RSet<Long> larkSendMessageIdSet = redissonClient.getSet("larkSendMessageIdSet");
        larkSendMessageIdSet.expire(Duration.ofDays(1L));
        RSet<Long> mailSendMessageIdSet = redissonClient.getSet("mailSendMessageIdSet");
        mailSendMessageIdSet.expire(Duration.ofDays(1L));
        RSet<Long> smsSendMessageIdSet = redissonClient.getSet("smsSendMessageIdSet");
        smsSendMessageIdSet.expire(Duration.ofDays(1L));
        RSet<Long> miworkSaasSendMessageIdSet = redissonClient.getSet("miworkSaasSendMessageIdSet");
        miworkSaasSendMessageIdSet.expire(Duration.ofDays(1L));
        sendMessageIdSetMap.put(MessageChannelEnum.MI_WORK, larkSendMessageIdSet);
        sendMessageIdSetMap.put(MessageChannelEnum.EMAIL, mailSendMessageIdSet);
        sendMessageIdSetMap.put(MessageChannelEnum.SMS, smsSendMessageIdSet);
        sendMessageIdSetMap.put(MessageChannelEnum.MI_WORK_SAAS, miworkSaasSendMessageIdSet);
    }

    public boolean contains(MessageChannelEnum messageChannelEnum, Long id) {
//        return bloomFilter.contains(id);
        return sendMessageIdSetMap.containsKey(messageChannelEnum) &&
                sendMessageIdSetMap.get(messageChannelEnum).contains(id);
    }

    public boolean add(MessageChannelEnum messageChannelEnum, Long id) {
        return sendMessageIdSetMap.containsKey(messageChannelEnum) &&
                sendMessageIdSetMap.get(messageChannelEnum).add(id);
    }

    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
}
