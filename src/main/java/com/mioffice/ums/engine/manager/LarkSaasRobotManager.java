package com.mioffice.ums.engine.manager;

import java.io.IOException;
import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import com.larksuite.appframework.sdk.client.MessageDestination;
import com.larksuite.appframework.sdk.client.MessageDestinations;
import com.larksuite.appframework.sdk.client.message.Message;
import com.larksuite.appframework.sdk.utils.JsonUtil;
import com.mioffice.ums.engine.entity.bo.MiWorkJsonAndSdkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MsgFormatTypeEnum;
import com.mioffice.ums.engine.message.LarkSaasMessageHelper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.utils.JsonUtils;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.19
 */
@Setter
@Slf4j
public class LarkSaasRobotManager extends MiWorkRobotManager<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> {

    private LarkSaasMessageHelper larkSaasMessageHelper;

    @Override
    public MiWorkResponseBO sendMsg(String appId, MessageUserInfo messageUserInfo, MiWorkRobot.Session session) throws Exception {
        assertExistRobot(appId);
        MiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> miWorkRobot = robotCache.get(appId).get();
        MessageTemplateInfo messageTemplateInfo = templateParse.get(messageUserInfo.getMsgTmpId());

        MiWorkJsonAndSdkRequestBO miWorkJsonAndSdkRequestBO = new MiWorkJsonAndSdkRequestBO();

        MsgFormatTypeEnum msgFormatTypeEnum = MsgFormatTypeEnum.get(messageTemplateInfo.getMsgFormatType());
        miWorkJsonAndSdkRequestBO.setFormatTypeEnum(msgFormatTypeEnum);
        miWorkJsonAndSdkRequestBO.setSession(session);

        if (msgFormatTypeEnum == MsgFormatTypeEnum.SDK) {
            Message message = larkSaasMessageHelper.parseIi8nSdk(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setMessage(message);
        } else if (msgFormatTypeEnum == MsgFormatTypeEnum.JSON) {
            String message = larkSaasMessageHelper.parseJson(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setJsonMsg(message);
        } else {
            // 兼容老版sdk消息
            Message message = larkSaasMessageHelper.parseIi8nSdk(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setMessage(message);
            log.error("飞书消息内容格式为 0, 默认走 sdk 发送");
        }

        MiWorkResponseBO resp;
        MessageDestination messageDestination = miWorkJsonAndSdkRequestBO.getSession().toMessageDestination();
        if (miWorkJsonAndSdkRequestBO.getFormatTypeEnum() == MsgFormatTypeEnum.JSON) {
            String msg = replaceMessageDestination(messageDestination, miWorkJsonAndSdkRequestBO.getJsonMsg());
            log.info("saas发送json消息：{}", msg);
            String msgId = miWorkRobot.getLarkSaasAppInstance().getLarkClient().sendChatMessage(msg);
            resp = new MiWorkResponseBO(msgId);
            resp.setMessageJson(msg);
        } else {
            String msgId = miWorkRobot.getLarkSaasAppInstance().getLarkClient().sendChatMessage(messageDestination, miWorkJsonAndSdkRequestBO.getMessage());
            resp = new MiWorkResponseBO(msgId);
            resp.setMessageJson(JsonUtils.toJson(miWorkJsonAndSdkRequestBO.getMessage()));
        }

        return resp;
    }

    private String replaceMessageDestination(MessageDestination messageDestination, String msg) throws IOException {
        Map<String, Object> jsonMsg = JsonUtil.larkFormatToObject(msg, new TypeReference<Map<String, Object>>() {
        });

        jsonMsg.remove("open_id");
        jsonMsg.remove("root_id");
        jsonMsg.remove("chat_id");
        jsonMsg.remove("user_id");
        jsonMsg.remove("email");

        String destIdentity = messageDestination.identity();
        if (messageDestination instanceof MessageDestinations.OpenId) {
            jsonMsg.put("open_id", destIdentity);
        } else if (messageDestination instanceof MessageDestinations.ChatId) {
            jsonMsg.put("chat_id", destIdentity);
        } else if (messageDestination instanceof MessageDestinations.UserId) {
            jsonMsg.put("user_id", destIdentity);
        } else if (messageDestination instanceof MessageDestinations.Email) {
            jsonMsg.put("email", destIdentity);
        }

        return JsonUtil.larkFormatToJsonString(jsonMsg);
    }
}
