package com.mioffice.ums.engine.manager;

import java.io.IOException;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.larksuite.appframework.sdk.client.MessageDestination;
import com.larksuite.appframework.sdk.client.MessageDestinations;
import com.larksuite.appframework.sdk.client.message.CardMessage;
import com.larksuite.appframework.sdk.client.message.Message;
import com.larksuite.appframework.sdk.utils.JsonUtil;
import com.mioffice.ums.engine.entity.bo.MiWorkJsonAndSdkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MsgFormatTypeEnum;
import com.mioffice.ums.engine.message.LarkSaasMessageHelper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.utils.JsonUtils;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.19
 */
@Setter
@Slf4j
public class LarkSaasV1RobotManager extends MiWorkRobotManager<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> {

    private LarkSaasMessageHelper larkSaasMessageHelper;

    @Override
    public MiWorkResponseBO sendMsg(String appId, MessageUserInfo messageUserInfo, MiWorkRobot.Session session) throws Exception {
        assertExistRobot(appId);
        MiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> miWorkRobot = robotCache.get(appId).get();
        MessageTemplateInfo messageTemplateInfo = templateParse.get(messageUserInfo.getMsgTmpId());

        MiWorkJsonAndSdkRequestBO miWorkJsonAndSdkRequestBO = new MiWorkJsonAndSdkRequestBO();

        MsgFormatTypeEnum msgFormatTypeEnum = MsgFormatTypeEnum.get(messageTemplateInfo.getMsgFormatType());
        miWorkJsonAndSdkRequestBO.setFormatTypeEnum(msgFormatTypeEnum);
        miWorkJsonAndSdkRequestBO.setSession(session);

        if (msgFormatTypeEnum == MsgFormatTypeEnum.SDK) {
            Message message = larkSaasMessageHelper.parseIi8nSdkV1(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setMessage(message);
        } else if (msgFormatTypeEnum == MsgFormatTypeEnum.JSON) {
            String message = larkSaasMessageHelper.parseJson(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setJsonMsg(message);
        } else {
            // 兼容老版sdk消息
            Message message = larkSaasMessageHelper.parseIi8nSdkV1(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setMessage(message);
            log.error("飞书消息内容格式为 0, 默认走 sdk 发送");
        }

        MiWorkResponseBO resp;
        MessageDestination messageDestination = miWorkJsonAndSdkRequestBO.getSession().toMessageDestination();
        if (miWorkJsonAndSdkRequestBO.getFormatTypeEnum() == MsgFormatTypeEnum.JSON) {
            String msg = replaceMessageDestinationV1(messageDestination, miWorkJsonAndSdkRequestBO.getJsonMsg());
            log.info("saas发送json消息V1接口版本：{}", msg);
            String msgId = miWorkRobot.getLarkSaasAppInstance().getLarkClient().sendChatMessageV1(msg,
                    messageDestination instanceof MessageDestinations.ChatId ? "chat_id" : "user_id");
            resp = new MiWorkResponseBO(msgId);
            resp.setMessageJson(msg);
        } else {
            String msgId = miWorkRobot.getLarkSaasAppInstance().getLarkClient().sendChatMessageV1(messageDestination,
                    (CardMessage) miWorkJsonAndSdkRequestBO.getMessage());
            resp = new MiWorkResponseBO(msgId);
            resp.setMessageJson(JsonUtils.toJson(miWorkJsonAndSdkRequestBO.getMessage()));
        }

        return resp;
    }

    private String replaceMessageDestinationV1(MessageDestination messageDestination, String msg) throws IOException {
        ObjectNode jsonMsg = JsonUtil.larkFormatToObject(msg, new TypeReference<ObjectNode>() {
        });

        jsonMsg.put("msg_type", "interactive");

        if (jsonMsg.has("content")) {
            jsonMsg.put("content", JsonUtil.larkFormatToJsonString(jsonMsg.get("content")));
        } else if (jsonMsg.has("card")) {
            if (jsonMsg.has("update_multi")) {
                boolean update_multi = jsonMsg.get("update_multi").asBoolean();
                if (jsonMsg.get("card").has("config")) {
                    ObjectNode config = (ObjectNode) jsonMsg.get("card").get("config");
                    config.remove("wide_screen_mode");
                    config.put("update_multi", update_multi);
                    jsonMsg.remove("update_multi");
                }
            }
            jsonMsg.put("content", JsonUtil.larkFormatToJsonString(jsonMsg.get("card")));
            jsonMsg.remove("card");
        }

        String destIdentity = messageDestination.identity();
        jsonMsg.put("receive_id", destIdentity);

        return JsonUtil.larkFormatToJsonString(jsonMsg);
    }
}
