package com.mioffice.ums.engine.manager;

import com.larksuite.appframework.sdk.client.message.Message;
import com.mioffice.ums.engine.entity.bo.MiWorkJsonAndSdkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MsgFormatTypeEnum;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.robot.miwork.JsonAndSdkMiWorkRobot;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName MessageV1MiWorkRobotManager
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/3/28 11:25
 **/
@Slf4j
public class MessageV1MiWorkRobotManager extends MiWorkRobotManager<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> {
    @Override
    public MiWorkResponseBO sendMsg(String appId, MessageUserInfo messageUserInfo, MiWorkRobot.Session session) throws Exception {
        assertExistRobot(appId);
        MiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> miWorkRobot = robotCache.get(appId).get();
        JsonAndSdkMiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> jsonAndSdkMiWorkRobot = new JsonAndSdkMiWorkRobot<>(miWorkRobot);
        MessageTemplateInfo messageTemplateInfo = templateParse.get(messageUserInfo.getMsgTmpId());

        MiWorkJsonAndSdkRequestBO miWorkJsonAndSdkRequestBO = new MiWorkJsonAndSdkRequestBO();

        MsgFormatTypeEnum msgFormatTypeEnum = MsgFormatTypeEnum.get(messageTemplateInfo.getMsgFormatType());
        miWorkJsonAndSdkRequestBO.setFormatTypeEnum(msgFormatTypeEnum);
        miWorkJsonAndSdkRequestBO.setSession(session);

        if (msgFormatTypeEnum == MsgFormatTypeEnum.SDK) {
            Message message = jsonAndSdkMiWorkRobot.getMiWorkMessageHelper().parseIi8nSdkV1(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setMessage(message);
        } else if (msgFormatTypeEnum == MsgFormatTypeEnum.JSON) {
            String message = jsonAndSdkMiWorkRobot.getMiWorkMessageHelper().parseJson(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setJsonMsg(message);
        } else {
            // 兼容老版sdk消息
            Message message = jsonAndSdkMiWorkRobot.getMiWorkMessageHelper().parseIi8nSdkV1(messageUserInfo);
            miWorkJsonAndSdkRequestBO.setMessage(message);
            log.error("飞书消息内容格式为 0, 默认走 sdk 发送");
        }

        return jsonAndSdkMiWorkRobot.sendMsgV1(miWorkJsonAndSdkRequestBO);
    }
}
