package com.mioffice.ums.engine.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.larksuite.appframework.sdk.client.LarkClient;
import com.larksuite.appframework.sdk.client.message.Message;
import com.larksuite.appframework.sdk.core.protocol.common.Group;
import com.larksuite.appframework.sdk.exception.LarkClientException;
import com.mioffice.ums.engine.config.LarkSaasMapConfig;
import com.mioffice.ums.engine.entity.bo.GroupInfosBO;
import com.mioffice.ums.engine.entity.bo.MiWorkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.RobotInfo;
import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.mioffice.ums.engine.exceptions.NotFoundRobotException;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.message.MiWorkMessageHelper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.robot.Status;
import com.mioffice.ums.engine.template.TemplateParse;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.mioffice.ums.engine.utils.KeyCenterUtil;
import com.mioffice.ums.engine.utils.LocalCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 消息机器人管理中心
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.03
 */
@Slf4j
public class MiWorkRobotManager<MiWorkReq extends MiWorkRequestBO, MiWorkResp extends MiWorkResponseBO> {

    private static final Integer PAGE_SIZE = 200;

    private static final String MIWORK_ROBOT_CACHE = "miwork_robots";

    /**
     * key - appId
     * value MiworkRobot
     */
    protected final LoadingCache<String, Optional<MiWorkRobot<MiWorkReq, MiWorkResp>>> robotCache =
            LocalCacheUtil.initLoadingCache(MIWORK_ROBOT_CACHE,
                    10000,
                    12 * 60,
                    new CacheLoader<String, Optional<MiWorkRobot<MiWorkReq, MiWorkResp>>>() {
                        @Override
                        public Optional<MiWorkRobot<MiWorkReq, MiWorkResp>> load(String key) {
                            return getRobot(key);
                        }
                    }
            );

    protected RobotInfoMapper robotInfoMapper;

    protected TemplateParse templateParse;

    private LarkSaasMapConfig larkSaasMapConfig;

    /**
     * 初始化资源
     */
    public void init() {
        this.loadRobot();
    }

    public void loadRobot() {
        List<RobotInfo> robotInfoList = robotInfoMapper.selectList(Wrappers.lambdaQuery());
        robotCacheProcess(robotInfoList);
    }

    /**
     * 加载robot
     */
    public void loadRobot(List<String> appIdList) {
        if (CollectionUtils.isNotEmpty(appIdList)) {
            List<RobotInfo> robotInfoList =
                    robotInfoMapper.selectList(Wrappers.<RobotInfo>lambdaQuery().in(RobotInfo::getAppId, appIdList));
            robotCacheProcess(robotInfoList);
        }
    }

    private void robotCacheProcess(List<RobotInfo> robotInfoList) {
        if (CollectionUtils.isNotEmpty(robotInfoList)) {
            for (RobotInfo robotInfo : robotInfoList) {
                try {
                    if (robotInfo.getStopFlag() == (byte) RobotStatusEnum.ROBOT_OFF.getCode()) {
                        robotCache.invalidate(robotInfo.getAppId());
                    } else {
                        robotCache.refresh(robotInfo.getAppId());
                    }
                } catch (Exception e) {
                    log.error("创建飞书机器人失败 appId = [{}]", robotInfo.getAppId(), e);
                }
            }
        }
    }

    /**
     * 加载机器人
     *
     * @param appId 机器人ak
     * @return
     */
    public Optional<MiWorkRobot<MiWorkReq, MiWorkResp>> getRobot(String appId) {
        RobotInfo robotInfo = robotInfoMapper.selectOne(
                Wrappers.<RobotInfo>lambdaQuery().eq(RobotInfo::getAppId, appId).eq(RobotInfo::getStopFlag, 1)
                        .last("LIMIT 1"));
        if (Objects.nonNull(robotInfo)) {
            try {
                robotInfo.setAppSecret(KeyCenterUtil.decrypt(robotInfo.getAppSecret()));
                MiWorkRobot<MiWorkReq, MiWorkResp> miWorkRobot =
                        new MiWorkRobot<>(robotInfo.getAppId(), robotInfo.getAppSecret(), robotInfo.getAppShortName(),
                                templateParse, larkSaasMapConfig);
                Status status = new Status();
                BeanUtils.copyProperties(robotInfo, status);
                miWorkRobot.setStatus(status);
                return Optional.of(miWorkRobot);
            } catch (Throwable th) {
                log.error("创建飞书机器人失败 appId = [{}]", robotInfo.getAppId(), th);
            }
        }
        return Optional.empty();
    }

    public void assertExistRobot(String appId) throws NotFoundRobotException {
        try {
            Optional<MiWorkRobot<MiWorkReq, MiWorkResp>> optionalMiWorkRobot = robotCache.get(appId);
            if (Objects.isNull(optionalMiWorkRobot) || !optionalMiWorkRobot.isPresent()) {
                log.error("创建飞书机器人失败 appId = [{}]无对应信息", appId);
                throw new NotFoundRobotException();
            }
        } catch (Throwable ex) {
            log.error("创建飞书机器人失败 appId = [{}]", appId, ex);
            throw new NotFoundRobotException();
        }
    }

    /**
     * 指定机器人发送消息
     *
     * @param appId           机器人appId
     * @param messageUserInfo 消息体
     * @param session         会话
     * @return
     * @throws NotFoundRobotException
     * @throws LarkClientException
     */
    public MiWorkResponseBO sendMsg(String appId, MessageUserInfo messageUserInfo, MiWorkRobot.Session session)
            throws Exception {
        assertExistRobot(appId);
        MiWorkRobot<MiWorkReq, MiWorkResp> miWorkRobot = robotCache.get(appId).get();
        MiWorkMessageHelper miWorkMessageHelper = miWorkRobot.getMiWorkMessageHelper();
        Message message = miWorkMessageHelper.parseIi8nSdk(messageUserInfo);

        MiWorkRequestBO miWorkRequestBO = new MiWorkRequestBO();
        miWorkRequestBO.setSession(session);
        miWorkRequestBO.setMessage(message);

        MiWorkResponseBO miWorkResponseBO = miWorkRobot.sendMsg(miWorkRequestBO);
        miWorkResponseBO.setMessageJson(JsonUtils.toJson(message));
        return miWorkResponseBO;
    }

    /**
     * 上传文件到飞书saaS 平台
     *
     * @param url 图片的url
     * @return imageKey 可以直接在飞书的聊天中使用
     */
    public String uploadImageForLark(String appId, String url)
            throws Exception {
        assertExistRobot(appId);
        try (InputStream inputStream = getImageStream(url)) {
            LarkClient larkClient = robotCache.get(appId).get().getLarkAppInstance().getLarkClient();
            LarkClient.UploadImageResult uploadImageResult =
                    larkClient.uploadImage(UUID.randomUUID().toString(), inputStream);
            return uploadImageResult.getImageKey();
        } catch (Exception e) {
            log.error("上传图片失败", e);
            throw e;
        }
    }

    /**
     * 上传文件到飞书saaS 平台
     *
     * @param url 图片的url
     * @return imageKey 可以直接在飞书的聊天中使用
     */
    public String uploadSaasImageForLark(String appId, String url)
            throws Exception {
        assertExistRobot(appId);
        try (InputStream inputStream = getImageStream(url)) {
            LarkClient larkClient = robotCache.get(appId).get().getLarkSaasAppInstance().getLarkClient();
            LarkClient.UploadImageResult uploadImageResult =
                    larkClient.uploadImage(UUID.randomUUID().toString(), inputStream);
            return uploadImageResult.getImageKey();
        } catch (Exception e) {
            log.error("上传图片失败", e);
            throw e;
        }
    }

    public LarkClient.GroupInfoResult fetchGroupInfo(String appId, String chatId) throws Exception {
        assertExistRobot(appId);
        LarkClient larkClient = robotCache.get(appId).get().getLarkAppInstance().getLarkClient();
        return larkClient.fetchGroupInfo(chatId);
    }

    public GroupInfosBO fetchGroupInfos(String appId, List<String> chatIdList) {

        List<LarkClient.GroupInfoResult> groupInfoResults = chatIdList.parallelStream().map(p -> {
            try {
                return fetchGroupInfo(appId, p);
            } catch (Exception e) {
                LarkClient.GroupInfoResult groupInfoResult = new LarkClient.GroupInfoResult();
                groupInfoResult.setChatId(p);
                groupInfoResult.setName("");
                return groupInfoResult;
            }
        }).collect(Collectors.toList());
        GroupInfosBO groupInfosBO = new GroupInfosBO();
        groupInfosBO.setGroupInfoResults(groupInfoResults);
        return groupInfosBO;
    }

    public LarkClient.GroupListResult fetchGroupList(String appId) throws Exception {
        assertExistRobot(appId);
        LarkClient larkClient = robotCache.get(appId).get().getLarkAppInstance().getLarkClient();
        LarkClient.GroupListResult groupListResult = larkClient.fetchGroupList(PAGE_SIZE, null);
        List<Group> allGroups = groupListResult.getGroups();
        String pageToken = groupListResult.getPageToken();
        boolean hasMore = groupListResult.getHasMore();
        while (hasMore) {
            LarkClient.GroupListResult groupListResultTmp = larkClient.fetchGroupList(PAGE_SIZE, pageToken);
            hasMore = groupListResultTmp.getHasMore();
            pageToken = groupListResultTmp.getPageToken();
            if (CollectionUtils.isEmpty(groupListResultTmp.getGroups())) {
                break;
            }
            allGroups.addAll(groupListResultTmp.getGroups());
            groupListResult.setGroups(allGroups);
        }
        return groupListResult;
    }

    public boolean checkUserRead(String appId, String messageId, String userName) throws Exception {
        assertExistRobot(appId);
        LarkClient larkClient = robotCache.get(appId).get().getLarkAppInstance().getLarkClient();
        return larkClient.checkUserRead(messageId, userName);
    }

    public boolean retractMessage(String appId, String messageId) throws Exception {
        assertExistRobot(appId);
        LarkClient larkClient = robotCache.get(appId).get().getLarkAppInstance().getLarkClient();
        return larkClient.retractMessage(messageId);
    }

    /**
     * <p>
     * 将url的网络流转成字节流，重新上传
     * </p>
     *
     * @param url 图片地址
     * @return InputStream
     */
    public static InputStream getImageStream(String url) throws IOException {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setReadTimeout(5000);
            connection.setConnectTimeout(5000);
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                return connection.getInputStream();
            } else {
                throw new RuntimeException("图片下载失败 responseCode = " + responseCode);
            }
        } catch (Exception e) {
            log.error("下载文件失败，url = [{}]", url, e);
            throw e;
        }
    }

    public void setRobotInfoMapper(RobotInfoMapper robotInfoMapper) {
        this.robotInfoMapper = robotInfoMapper;
    }

    public void setTemplateParse(TemplateParse templateParse) {
        this.templateParse = templateParse;
    }

    public void setLarkSaasMapConfig(LarkSaasMapConfig larkSaasMapConfig) {
        this.larkSaasMapConfig = larkSaasMapConfig;
    }

    public Set<String> effectiveRobot() {
        return robotCache.asMap().keySet();
    }
}
