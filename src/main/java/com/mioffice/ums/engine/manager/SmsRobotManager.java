package com.mioffice.ums.engine.manager;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.gson.Gson;
import com.mioffice.ums.engine.entity.bo.SmsReqBo;
import com.mioffice.ums.engine.entity.bo.SmsResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.SmsRobotInfo;
import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.mioffice.ums.engine.exceptions.NotFoundRobotException;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import com.mioffice.ums.engine.robot.SmsRobot;
import com.mioffice.ums.engine.robot.Status;
import com.mioffice.ums.engine.template.TemplateParse;
import com.mioffice.ums.engine.utils.AesUtils;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.mioffice.ums.engine.utils.LocalCacheUtil;
import com.xiaomi.miliao.thrift.ClientFactory;
import com.xiaomi.sms.SmsUserconnectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.02
 */
@Slf4j
public class SmsRobotManager {

    private static final String SMS_ROBOT_CACHE = "sms_robots";
    private SmsRobotInfoMapper smsRobotInfoMapper;

    protected final LoadingCache<String, Optional<SmsRobot>> robotCache =
            LocalCacheUtil.initLoadingCache(SMS_ROBOT_CACHE,
                    10000,
                    12 * 60,
                    new CacheLoader<String, Optional<SmsRobot>>() {
                        @Override
                        public Optional<SmsRobot> load(String contentTypeKey) {
                            return getRobot(contentTypeKey);
                        }
                    }
            );
    /**
     * gson 线程安全 全局共享一个
     */
    private final Gson gson;

    private TemplateParse templateParse;

    public void init() {
        this.loadRobot();
    }

    public SmsRobotManager(Gson gson) {
        this.gson = gson;
    }

    public void loadRobot() {
        List<SmsRobotInfo> robotInfoList =
                smsRobotInfoMapper.selectList(Wrappers.lambdaQuery());
        robotCacheProcess(robotInfoList);
    }

    public void loadRobot(List<String> contentTypeKeyList) {
        if (CollectionUtils.isNotEmpty(contentTypeKeyList)) {
            List<SmsRobotInfo> robotInfoList =
                    smsRobotInfoMapper.selectList(
                            Wrappers.<SmsRobotInfo>lambdaQuery().in(SmsRobotInfo::getStopFlag, contentTypeKeyList));
            robotCacheProcess(robotInfoList);
        }
    }

    private void robotCacheProcess(List<SmsRobotInfo> robotInfoList) {
        if (CollectionUtils.isNotEmpty(robotInfoList)) {
            for (SmsRobotInfo smsRobotInfo : robotInfoList) {
                try {
                    if (smsRobotInfo.getStopFlag() == (byte) RobotStatusEnum.ROBOT_OFF.getCode()) {
                        robotCache.invalidate(smsRobotInfo.getContentTypeKey());
                    } else {
                        robotCache.refresh(smsRobotInfo.getContentTypeKey());
                    }
                } catch (Throwable ex) {
                    log.error("创建短信机器人失败 contentTypeKey = [{}]", smsRobotInfo.getContentTypeKey(), ex);
                }
            }
        }
    }

    /**
     * 根据短信模板号获取机器人
     *
     * @param contentTypeKey 短信模板号
     * @return
     */
    public Optional<SmsRobot> getRobot(String contentTypeKey) {
        SmsRobotInfo smsRobotInfo = smsRobotInfoMapper.selectOne(
                Wrappers.<SmsRobotInfo>lambdaQuery().eq(SmsRobotInfo::getContentTypeKey, contentTypeKey)
                        .eq(SmsRobotInfo::getStopFlag, 1).last("LIMIT 1"));
        if (Objects.nonNull(smsRobotInfo)) {
            Status status = new Status();
            BeanUtils.copyProperties(smsRobotInfo, status);
            SmsRobot smsRobot = null;
            try {
                SmsUserconnectService.Iface client = ClientFactory.getClient(SmsUserconnectService.Iface.class);
                smsRobot = new SmsRobot(smsRobotInfo.getContentTypeKey(), client, gson);
                return Optional.of(smsRobot);
            } catch (Throwable ex) {
                log.error("创建短信机器人失败 contentTypeKey = [{}]", contentTypeKey, ex);
            }
        }
        return Optional.empty();
    }

    public SmsResponseBO sendMsg(String contentKey, MessageUserInfo messageUserInfo) throws Exception {
        this.assertExistRobot(contentKey);
        SmsRobot smsRobot = robotCache.get(contentKey).get();

        Map<String, Object> params = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());
        String content = templateParse.parseContent(messageUserInfo.getMsgTmpId(), params);

        SmsReqBo smsReqBo = new SmsReqBo();
        smsReqBo.setPhone(messageUserInfo.getPhone());
        smsReqBo.setContent(content);

        // 手机号解密
        decryptPhone(smsReqBo);

        return smsRobot.sendMsg(smsReqBo);
    }

    private void decryptPhone(SmsReqBo smsReqBo) {
        if (StringUtils.isNotBlank(smsReqBo.getPhone())) {
            try {
                smsReqBo.setPhone(AesUtils.decrypt(smsReqBo.getPhone()));
            } catch (Exception e) {
                log.error("手机号解密错误", e);
            }
        }
    }

    private void assertExistRobot(String contentTypeKey) throws NotFoundRobotException {
        try {
            Optional<SmsRobot> optionalSmsRobot = robotCache.get(contentTypeKey);
            if (Objects.isNull(optionalSmsRobot) || !optionalSmsRobot.isPresent()) {
                log.error("创建短信机器人失败 contentTypeKey = [{}]对应的信息不存在", contentTypeKey);
                throw new NotFoundRobotException();
            }
        } catch (Throwable ex) {
            log.error("创建短信机器人失败 contentTypeKey = [{}]", contentTypeKey, ex);
            throw new NotFoundRobotException();
        }
    }

    public void setSmsRobotInfoMapper(SmsRobotInfoMapper smsRobotInfoMapper) {
        this.smsRobotInfoMapper = smsRobotInfoMapper;
    }

    public void setTemplateParse(TemplateParse templateParse) {
        this.templateParse = templateParse;
    }

    public Set<String> effectiveRobot(){
        return robotCache.asMap().keySet();
    }
}
