package com.mioffice.ums.engine.manager;

import com.mioffice.ums.engine.config.MqProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Set;

/**
 * 统一Topic管理器
 * 用于管理从Talos迁移到RocketMQ的通用Topic
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class UnifiedTopicManager {

    @Autowired
    private MqProperties mqProperties;

    @PostConstruct
    public void init() {
        log.info("UnifiedTopicManager initialized. Enabled: {}, Topic: {}, RobotIds: {}", 
                mqProperties.getUnifiedTopic().isEnabled(), 
                mqProperties.getUnifiedTopic().getName(), 
                mqProperties.getUnifiedTopic().getRobotIds());
    }

    /**
     * 判断机器人ID是否在统一Topic白名单中
     * 
     * @param robotId 机器人ID
     * @return true表示应该使用统一Topic
     */
    public boolean shouldUseUnifiedTopic(String robotId) {
        if (!mqProperties.getUnifiedTopic().isEnabled()) {
            return false;
        }
        return StringUtils.isNotBlank(robotId) && mqProperties.getUnifiedTopic().getRobotIds().contains(robotId);
    }

    /**
     * 获取统一Topic名称
     * 
     * @return 统一Topic名称
     */
    public String getUnifiedTopicName() {
        return mqProperties.getUnifiedTopic().getName();
    }

    /**
     * 判断是否启用了统一Topic功能
     * 
     * @return true表示启用
     */
    public boolean isUnifiedTopicEnabled() {
        return mqProperties.getUnifiedTopic().isEnabled();
    }

    /**
     * 获取统一Topic的机器人ID集合
     * 
     * @return 机器人ID集合的副本
     */
    public Set<String> getUnifiedTopicRobotIds() {
        return new HashSet<>(mqProperties.getUnifiedTopic().getRobotIds());
    }
} 