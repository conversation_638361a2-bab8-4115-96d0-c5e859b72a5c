package com.mioffice.ums.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.engine.entity.info.DevUserInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.29
 */
public interface DevUserInfoMapper extends BaseMapper<DevUserInfo> {
    int updateBatch(List<DevUserInfo> list);

    int updateBatchSelective(List<DevUserInfo> list);

    int batchInsert(@Param("list") List<DevUserInfo> list);

    int insertOrUpdate(DevUserInfo record);

    int insertOrUpdateSelective(DevUserInfo record);
}