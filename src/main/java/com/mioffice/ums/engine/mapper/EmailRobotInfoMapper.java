package com.mioffice.ums.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.engine.entity.info.EmailRobotInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.09
 */
public interface EmailRobotInfoMapper extends BaseMapper<EmailRobotInfo> {
    int updateBatch(List<EmailRobotInfo> list);

    int updateBatchSelective(List<EmailRobotInfo> list);

    int batchInsert(@Param("list") List<EmailRobotInfo> list);

    int insertOrUpdate(EmailRobotInfo record);

    int insertOrUpdateSelective(EmailRobotInfo record);
}