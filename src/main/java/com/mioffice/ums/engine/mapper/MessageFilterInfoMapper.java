package com.mioffice.ums.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.engine.entity.info.MessageFilterInfo;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.13
 */
public interface MessageFilterInfoMapper extends BaseMapper<MessageFilterInfo> {
    int updateBatch(List<MessageFilterInfo> list);

    int updateBatchSelective(List<MessageFilterInfo> list);

    int batchInsert(@Param("list") List<MessageFilterInfo> list);

    int insertOrUpdate(MessageFilterInfo record);

    int insertOrUpdateSelective(MessageFilterInfo record);
}