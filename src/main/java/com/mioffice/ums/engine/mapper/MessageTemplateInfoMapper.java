package com.mioffice.ums.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.22
 */
public interface MessageTemplateInfoMapper extends BaseMapper<MessageTemplateInfo> {
    int updateBatch(List<MessageTemplateInfo> list);

    int updateBatchSelective(List<MessageTemplateInfo> list);

    int batchInsert(@Param("list") List<MessageTemplateInfo> list);

    int insertOrUpdate(MessageTemplateInfo record);

    int insertOrUpdateSelective(MessageTemplateInfo record);
}