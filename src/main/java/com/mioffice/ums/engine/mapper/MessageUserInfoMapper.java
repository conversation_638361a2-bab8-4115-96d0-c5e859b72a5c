package com.mioffice.ums.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mioffice.ums.engine.entity.bo.AvgCostTimeBO;
import com.mioffice.ums.engine.entity.bo.CountAndCostTimeBO;
import com.mioffice.ums.engine.entity.bo.ErrorTypeListBo;
import com.mioffice.ums.engine.entity.bo.ExtraIdCostTimeBO;
import com.mioffice.ums.engine.entity.bo.ExtraIdCountBO;
import com.mioffice.ums.engine.entity.bo.MessageGroupDeptRecordBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.12.02
 */
public interface MessageUserInfoMapper extends BaseMapper<MessageUserInfo> {
    MessageUserInfo selectByIdFromMaster(@Param("id") Long id);

    int updateBatch(List<MessageUserInfo> list);

    int updateBatchSelective(List<MessageUserInfo> list);

    int batchInsert(@Param("list") List<MessageUserInfo> list);

    int insertOrUpdate(MessageUserInfo record);

    int insertOrUpdateSelective(MessageUserInfo record);

    void updateAddRetryCount(@Param("idList") List<Long> idList,
                             @Param("count") int count,
                             @Param("messageStatus") Byte messageStatus,
                             @Param("currentMs") long currentMs);

    void addRetryCount(@Param("count") long count, @Param("currentMs") long currentMs, @Param("id") long id);

    List<MessageGroupDeptRecordBO> selectByGroupPage(Page<MessageGroupDeptRecordBO> page,
                                                     @Param("extraId") String extraId,
                                                     @Param("deptLevel2IdList") List<String> deptLevel2IdList,
                                                     @Param("deptLevel3IdList") List<String> deptLevel3IdList,
                                                     @Param("deptLevel4IdList") List<String> deptLevel4IdList);

    List<ExtraIdCountBO> selectCountByExtraIdList(@Param("extraIdList") List<String> extraIdList);

    List<ExtraIdCostTimeBO> selectCostTimeByExtraIdList(@Param("extraIdList") List<String> extraIdList);

    List<ErrorTypeListBo> selectErrCountByExtraIdList(@Param("extraIdList") List<String> extraIdListList,
                                                      @Param("typeList") List<Byte> typeList);

    Long selectAverageCostTimeByExtraIdList(@Param("extraIdList") List<String> extraIdList);

    List<AvgCostTimeBO> getAvgCostTimeByExtraIdList(@Param("extraIdList") List<String> extraIdList);

    List<ExtraIdCostTimeBO> selectSuccessOrInterruptCostTimeByExtraIdList(
            @Param("extraIdList") List<String> extraIdList);

    /***
     * 查询未读的员工名单列表
     * @param subExtraIdList 子任务extraId列表
     * @return
     */
    List<String> queryUnReadDetail(@Param("parentExtraId") String parentExtraId,
                                   @Param("subExtraIdList") List<String> subExtraIdList);

    IPage<String> selectPageUnReadDetail(Page<String> page,
                                         @Param("searchKey") String searchKey,
                                         @Param("whiteList") Set<String> whiteList,
                                         @Param("parentExtraId") String parentExtraId,
                                         @Param("subExtraIdList") List<String> subExtraIdList);

    CountAndCostTimeBO countByExtraIdListAndStatus(@Param("parentExtraId") String parentExtraId,
                                                   @Param("messageStatus") Byte messageStatus);

    CountAndCostTimeBO countToDo(@Param("parentExtraId") String parentExtraId);

    CountAndCostTimeBO selectReadCount(@Param("parentExtraId") String parentExtraId,
                                       @Param("subExtraIdList") List<String> subExtraIdList);

    CountAndCostTimeBO selectReadCount1(@Param("parentExtraId") String parentExtraId,
                                        @Param("subExtraIdList") List<String> subExtraIdList);

    List<String> queryReadDetail(@Param("subExtraIdList") List<String> subExtraIdList);

    List<String> queryUnReadAppId(@Param("startId") Long startId);

    /**
     * 分页查询非白名单且发送中消息
     */
    IPage<MessageUserInfo> selectPageSendingExcludeWhiteList(
        Page<MessageUserInfo> page,
        @Param("excludeAppId") String excludeAppId,
        @Param("whiteList") List<String> whiteList);

    /**
     * 分页查询排除指定appId列表且发送中消息
     */
    List<MessageUserInfo> selectPageSendingExcludeAppIds(
        @Param("excludeAppIds") List<String> excludeAppIds,
        @Param("limit") int limit,
        @Param("offset") int offset
    );
}
