package com.mioffice.ums.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.engine.entity.info.RobotInfo;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * ${todo}
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.22
 */
public interface RobotInfoMapper extends BaseMapper<RobotInfo> {
    int updateBatch(List<RobotInfo> list);

    int updateBatchSelective(List<RobotInfo> list);

    int batchInsert(@Param("list") List<RobotInfo> list);

    int insertOrUpdate(RobotInfo record);

    int insertOrUpdateSelective(RobotInfo record);
}