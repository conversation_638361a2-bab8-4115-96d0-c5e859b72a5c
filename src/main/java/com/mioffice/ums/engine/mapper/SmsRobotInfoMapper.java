package com.mioffice.ums.engine.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mioffice.ums.engine.entity.info.SmsRobotInfo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *      ${todo}
 * </p>
 * <AUTHOR>
 * @date    2020.09.02 
 */
public interface SmsRobotInfoMapper extends BaseMapper<SmsRobotInfo> {
    int updateBatch(List<SmsRobotInfo> list);

    int updateBatchSelective(List<SmsRobotInfo> list);

    int batchInsert(@Param("list") List<SmsRobotInfo> list);

    int insertOrUpdate(SmsRobotInfo record);

    int insertOrUpdateSelective(SmsRobotInfo record);
}