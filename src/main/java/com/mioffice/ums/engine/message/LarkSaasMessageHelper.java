package com.mioffice.ums.engine.message;

import com.larksuite.appframework.sdk.client.message.card.CardComponent;
import com.larksuite.appframework.sdk.client.message.card.element.Image;
import com.larksuite.appframework.sdk.client.message.card.module.Module;
import com.larksuite.appframework.sdk.client.message.card.module.Note;
import com.larksuite.appframework.sdk.client.message.card.objects.Text;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.enums.I18nEnum;
import com.mioffice.ums.engine.template.TemplateParse;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class LarkSaasMessageHelper extends MiWorkMessageHelper {
    public LarkSaasMessageHelper(TemplateParse templateParse) {
        super(templateParse);
    }

    protected List<Module> modules(I18nEnum i18nEnum, MessageTemplateInfo messageTemplateInfo,
                                   Map<String, Object> imageEntries, Map<String, Object> params) throws Exception {
        Map<String, Object> imageMap = imageEntries;
        if (messageTemplateInfo.getSaasImages() != null) {
            imageMap = JsonUtils.toMap(messageTemplateInfo.getSaasImages());
        }
        return super.modules(i18nEnum, messageTemplateInfo, imageMap, params);
    }

    protected List<Module> bottomSource() {
        List<Module> modules = new ArrayList<>();
        // 最下面的 note 标识
        List<CardComponent> cardComponents = new ArrayList<>();
        cardComponents.add(
                new Image("img_v3_02ml_c35824ba-ff6d-4ac7-aa7f-526521d10f8g", new Text(Text.Mode.PLAIN_TEXT, "hover")));
        cardComponents.add(new Text(Text.Mode.PLAIN_TEXT, "来自消息服务中心"));
        modules.add(new Note(cardComponents));
        return modules;
    }
}
