package com.mioffice.ums.engine.message;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.larksuite.appframework.sdk.client.message.CardMessage;
import com.larksuite.appframework.sdk.client.message.Message;
import com.larksuite.appframework.sdk.client.message.card.Card;
import com.larksuite.appframework.sdk.client.message.card.CardComponent;
import com.larksuite.appframework.sdk.client.message.card.Config;
import com.larksuite.appframework.sdk.client.message.card.Header;
import com.larksuite.appframework.sdk.client.message.card.TemplateColor;
import com.larksuite.appframework.sdk.client.message.card.element.Button;
import com.larksuite.appframework.sdk.client.message.card.element.Element;
import com.larksuite.appframework.sdk.client.message.card.element.Image;
import com.larksuite.appframework.sdk.client.message.card.module.Action;
import com.larksuite.appframework.sdk.client.message.card.module.Div;
import com.larksuite.appframework.sdk.client.message.card.module.Hr;
import com.larksuite.appframework.sdk.client.message.card.module.Img;
import com.larksuite.appframework.sdk.client.message.card.module.Module;
import com.larksuite.appframework.sdk.client.message.card.module.Note;
import com.larksuite.appframework.sdk.client.message.card.objects.I18n;
import com.larksuite.appframework.sdk.client.message.card.objects.Text;
import com.larksuite.appframework.sdk.utils.JsonUtil;
import com.mioffice.ums.engine.entity.bo.CardActionBO;
import com.mioffice.ums.engine.entity.bo.CardImgBO;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.I18nEnum;
import com.mioffice.ums.engine.template.TemplateParse;
import com.mioffice.ums.engine.utils.FileUtil;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.mioffice.ums.engine.utils.MessageUtil;
import com.mioffice.ums.engine.utils.RegexUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * build the miwork message
 *
 * <AUTHOR>
 * @date 2020/8/12 12:51
 */
public class MiWorkMessageHelper {

    private final TemplateParse templateParse;

    public MiWorkMessageHelper(TemplateParse templateParse) {
        this.templateParse = templateParse;
    }

    public Message parseSdk(MessageUserInfo messageUserInfo, I18nEnum i18nEnum) throws Exception {
        Long messageTemplateInfoId = messageUserInfo.getMsgTmpId();
        Map<String, Object> params = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());

        MessageTemplateInfo messageTemplateInfo = templateParse.get(messageTemplateInfoId);
        Map<String, Object> imageEntries = JsonUtils.toMap(messageTemplateInfo.getImages());

        // title
        String title = templateParse.parseTitle(messageTemplateInfoId, params, i18nEnum);
        Header header;
        if (StringUtils.isNotBlank(messageTemplateInfo.getTheme())) {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, title),
                    TemplateColor.valueOf(messageTemplateInfo.getTheme().toUpperCase()));
        } else {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, title));
        }
        Card card = new Card(new Config(true), header);
        // content
        List<Module> modules = Lists.newArrayList();
        // add cut-off rule
        modules.addAll(modules(i18nEnum, messageTemplateInfo, imageEntries, params));
        modules.addAll(bottomSource());
        card.setModules(modules);
        return new CardMessage(card.toObjectForJson());
    }

    public Message parseSdkV1(MessageUserInfo messageUserInfo, I18nEnum i18nEnum) throws Exception {
        Long messageTemplateInfoId = messageUserInfo.getMsgTmpId();
        Map<String, Object> params = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());

        MessageTemplateInfo messageTemplateInfo = templateParse.get(messageTemplateInfoId);
        Map<String, Object> imageEntries = JsonUtils.toMap(messageTemplateInfo.getImages());

        // title
        String title = templateParse.parseTitle(messageTemplateInfoId, params, i18nEnum);
        Header header;
        if (StringUtils.isNotBlank(messageTemplateInfo.getTheme())) {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, title),
                    TemplateColor.valueOf(messageTemplateInfo.getTheme().toUpperCase()));
        } else {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, title));
        }
        Card card = new Card(new Config(true), header);
        // content
        List<Module> modules = Lists.newArrayList();
        // add cut-off rule
        modules.addAll(modules(i18nEnum, messageTemplateInfo, imageEntries, params));
        modules.addAll(bottomSource());
        card.setModules(modules);
        return new CardMessage(JsonUtil.larkFormatToJsonString(card.toObjectForJson()));
    }

    public Message parseIi8nSdk(MessageUserInfo messageUserInfo) throws Exception {
        Long msgTmpId = messageUserInfo.getMsgTmpId();
        MessageTemplateInfo messageTemplateInfo = templateParse.get(msgTmpId);
        // 仅有一种语言，不需要区分国际化
        if (StringUtils.isNotBlank(messageTemplateInfo.getContentCn()) &&
                StringUtils.isBlank(messageTemplateInfo.getTitleCn())) {
            // 纯英文
            return parseSdk(messageUserInfo, I18nEnum.en_us);
        } else if (StringUtils.isBlank(messageTemplateInfo.getContentEn()) &&
                StringUtils.isBlank(messageTemplateInfo.getTitleEn())) {
            // 纯中文
            return parseSdk(messageUserInfo, I18nEnum.zh_cn);
        }

        Map<String, Object> imageEntries = JsonUtils.toMap(messageTemplateInfo.getImages());
        Map<String, Object> varDataParams = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());

        // 支持国际化
        String titleCn = templateParse.parseTitle(msgTmpId, varDataParams, I18nEnum.zh_cn);
        String titleEn = templateParse.parseTitle(msgTmpId, varDataParams, I18nEnum.en_us);

        Header header;
        if (StringUtils.isNotBlank(messageTemplateInfo.getTheme())) {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, new I18n(titleCn, titleEn, titleEn)),
                    TemplateColor.valueOf(messageTemplateInfo.getTheme().toUpperCase()));
        } else {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, new I18n(titleCn, titleEn, titleEn)));
        }
        Card card = new Card(new Config(true), header);

        List<Module> modulesCn = modules(I18nEnum.zh_cn, messageTemplateInfo, imageEntries, varDataParams);
        modulesCn.addAll(bottomSource());
        List<Module> modulesEn = modules(I18nEnum.en_us, messageTemplateInfo, imageEntries, varDataParams);
        modulesEn.addAll(bottomSource());

        card.setZhCnModules(modulesCn);
        card.setEnUsModules(modulesEn);
        card.setJaJpModules(modulesEn);

        return new CardMessage(card.toObjectForJson());
    }

    public Message parseIi8nSdkV1(MessageUserInfo messageUserInfo) throws Exception {
        Long msgTmpId = messageUserInfo.getMsgTmpId();
        MessageTemplateInfo messageTemplateInfo = templateParse.get(msgTmpId);
        // 仅有一种语言，不需要区分国际化
        if (StringUtils.isNotBlank(messageTemplateInfo.getContentCn()) &&
                StringUtils.isBlank(messageTemplateInfo.getTitleCn())) {
            // 纯英文
            return parseSdkV1(messageUserInfo, I18nEnum.en_us);
        } else if (StringUtils.isBlank(messageTemplateInfo.getContentEn()) &&
                StringUtils.isBlank(messageTemplateInfo.getTitleEn())) {
            // 纯中文
            return parseSdkV1(messageUserInfo, I18nEnum.zh_cn);
        }

        Map<String, Object> imageEntries = JsonUtils.toMap(messageTemplateInfo.getImages());
        Map<String, Object> varDataParams = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());

        // 支持国际化
        String titleCn = templateParse.parseTitle(msgTmpId, varDataParams, I18nEnum.zh_cn);
        String titleEn = templateParse.parseTitle(msgTmpId, varDataParams, I18nEnum.en_us);

        Header header;
        if (StringUtils.isNotBlank(messageTemplateInfo.getTheme())) {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, new I18n(titleCn, titleEn, titleEn)),
                    TemplateColor.valueOf(messageTemplateInfo.getTheme().toUpperCase()));
        } else {
            header = new Header(new Text(Text.Mode.PLAIN_TEXT, new I18n(titleCn, titleEn, titleEn)));
        }
        Card card = new Card(new Config(true), header);

        List<Module> modulesCn = modules(I18nEnum.zh_cn, messageTemplateInfo, imageEntries, varDataParams);
        modulesCn.addAll(bottomSource());
        List<Module> modulesEn = modules(I18nEnum.en_us, messageTemplateInfo, imageEntries, varDataParams);
        modulesEn.addAll(bottomSource());

        card.setZhCnModules(modulesCn);
        card.setEnUsModules(modulesEn);
        card.setJaJpModules(modulesEn);

        return new CardMessage(JsonUtil.larkFormatToJsonString(card.toObjectForJson()));
    }

    protected List<Module> modules(I18nEnum i18nEnum, MessageTemplateInfo messageTemplateInfo,
                                   Map<String, Object> imageEntries, Map<String, Object> params) throws Exception {
        String content = templateParse.parseContent(messageTemplateInfo.getId(), params, i18nEnum);
        // 内容换行
        String[] lines = content.split("\\r?\\n", -1);
        List<Module> modules = Lists.newArrayList();
        modules.add(new Hr());
        for (String line : lines) {
            if (StringUtils.isBlank(line.trim())) {
                continue;
                //   line = "\n";
            }
            if (RegexUtil.hasFullText(MessageUtil.MessageTextRegex.IMAGE_MD_REGEX, line.trim())) {
                // 图片
                List<String> urls = MessageUtil.parseMdForImageUrl(line);
                List<CardImgBO> imgList = urls.stream().map(url -> {
                    String id = FileUtil.parseFileId(url);
                    String imageKey = imageEntries.get(id).toString();
                    return new CardImgBO(imageKey, new Text(Text.Mode.PLAIN_TEXT, ""), null, messageTemplateInfo.getImageMode());
                }).collect(Collectors.toList());
                modules.addAll(imgList);
            } else {
                // 文本
                modules.add(new Div(new Text(Text.Mode.LARK_MD, line.trim()), null, null));
            }
        }
        // 按钮
        if (messageTemplateInfo.hasCardActions()) {
            String actionCards = templateParse.parseAction(messageTemplateInfo.getId(), params, i18nEnum);
            List<CardActionBO> cardActionBOList = JsonUtils.parse(actionCards, new TypeToken<List<CardActionBO>>() {
            }.getType());
            // add cut-off rule
            modules.add(new Hr());
            // add button
            // 3 button个一行
            List<Element> actions = new ArrayList<>();
            for (CardActionBO cardActionBo : cardActionBOList) {

                Button button =
                        new Button(cardActionBo.getName(), new Text(Text.Mode.PLAIN_TEXT, cardActionBo.getName()));

                // 用户点击跳转相应链接，不回传value字段值到业务方。业务方不可知用户行为。
                String landingPageUrl = cardActionBo.getLandingUrl();
                if (StringUtils.isNoneBlank(landingPageUrl)) {
                    button.setUrl(landingPageUrl);
                }
                button.setValue(cardActionBo.getVal());

                actions.add(button);

                if (actions.size() >= 3) {
                    Action action = new Action(actions);
                    modules.add(action);
                    // 新起一个 button 行
                    actions = new ArrayList<>();
                }
            }

            if (!actions.isEmpty()) {
                Action action = new Action(actions);
                modules.add(action);
            }
        }
        return modules;
    }

    protected List<Module> bottomSource() {
        List<Module> modules = new ArrayList<>();
        // 最下面的 note 标识
        List<CardComponent> cardComponents = new ArrayList<>();
        cardComponents.add(
                new Image("img_cf588c76-f44c-4337-a431-33c2fd8695cl", new Text(Text.Mode.PLAIN_TEXT, "hover")));
        cardComponents.add(new Text(Text.Mode.PLAIN_TEXT, "来自消息服务中心"));
        modules.add(new Note(cardComponents));
        return modules;
    }

    public String parseJson(MessageUserInfo messageUserInfo) throws Exception {
        Map<String, Object> params = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());
        return templateParse.parseContent(messageUserInfo.getMsgTmpId(), params);
    }
}
