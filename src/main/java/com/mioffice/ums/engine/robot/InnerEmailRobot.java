package com.mioffice.ums.engine.robot;

import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.engine.entity.bo.EmailRequestBO;
import com.mioffice.ums.engine.entity.bo.EmailResponseBO;
import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.mioffice.ums.engine.utils.ExceptionUtils;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.sun.mail.smtp.SMTPTransport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.List;
import java.util.Properties;
import java.util.stream.Stream;
import javax.activation.DataHandler;
import javax.activation.URLDataSource;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;

/**
 * <p>
 * 邮件机器人
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.03
 */
@Slf4j
public class InnerEmailRobot extends BaseEmailRobot {

    private String host;
    private String token;
    private Status status;
    private Session session;

    public InnerEmailRobot(String host, String token, Status status) {
        this.host = host;
        this.token = token;
        this.status = status;
    }

    @Override
    public void init() {
        Properties properties = new Properties();
        properties.setProperty("mail.host", host);
        properties.setProperty("mail.transport.protocol", "smtp");
        session = Session.getInstance(new Properties(properties));
    }

    @Override
    public EmailResponseBO sendMsg(final EmailRequestBO requestBO) throws Exception {

        log.info("InnerEmailRobot send email, title = [{}]", requestBO.getTitle());

        this.status.assertStatus();

        MimeMessage message = new MimeMessage(session);
        message.setSubject(requestBO.getTitle());
        message.setFrom(new InternetAddress(token));

        MimeMultipart mimeMultipart = new MimeMultipart("mixed");

        // 人
        message.setRecipient(Message.RecipientType.TO, new InternetAddress(requestBO.getTo()));
        // 抄送人
        if (StringUtils.isNotBlank(requestBO.getCcTo())) {
            InternetAddress[] ccAddressList =
                    Stream.of(requestBO.getCcTo().split(";")).map(ExceptionUtils.uncheck(InternetAddress::new))
                            .toArray(InternetAddress[]::new);
            message.setRecipients(Message.RecipientType.CC, ccAddressList);
        }
        // 附件
        if (StringUtils.isNotBlank(requestBO.getAttachUrl())) {
            List<String> attachUrlList = JsonUtils.parse(requestBO.getAttachUrl(), new TypeToken<List<String>>() {
            }.getType());
            for (String attachUrl : attachUrlList) {
                mimeMultipart.addBodyPart(newHttpMimeBodyPart(attachUrl));
            }
        }

        // 内容
        MimeBodyPart htmlBody = new MimeBodyPart();
        htmlBody.setContent(requestBO.getHtml(), "text/html;charset=utf-8");
        mimeMultipart.addBodyPart(htmlBody);

        // 邮件体(人，内容，附件)
        message.setContent(mimeMultipart);

        return new EmailResponseBO(send(message, session));
    }

    @Override
    public EmailResponseBO sendMsgV1(EmailRequestBO emailRequestBO) throws Exception {
        return sendMsg(emailRequestBO);
    }

    @Override
    public void close() {
        this.status.setStopFlag((byte) RobotStatusEnum.ROBOT_OFF.getCode());
    }

    @Override
    public void setStatus(Status status) {
        this.status = status;
    }

    private String send(MimeMessage message, Session session) throws MessagingException {
        SMTPTransport transport = (SMTPTransport) session.getTransport();
        transport.setLocalHost("127.0.0.1");
        if (!transport.isConnected()) {
            transport.connect();
        }
        transport.sendMessage(message, message.getAllRecipients());
//        transport.close();
        return message.getMessageID();
    }

    private MimeBodyPart newHttpMimeBodyPart(String httpUrl)
            throws MalformedURLException, MessagingException, UnsupportedEncodingException {
        MimeBodyPart attachBody = new MimeBodyPart();
        URL url = new URL(httpUrl);
        URLDataSource urlDataSource = new URLDataSource(url);
        attachBody.setDataHandler(new DataHandler(urlDataSource));
        String path = url.getPath();
        if (path.indexOf('/') != -1) {
            attachBody.setFileName(
                    MimeUtility.encodeText(URLDecoder.decode(path.substring(path.lastIndexOf("/") + 1), "UTF-8")));
        } else {
            attachBody.setFileName(MimeUtility.encodeText(URLDecoder.decode(path, "UTF-8")));
        }

        return attachBody;
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        System.out.println(URLDecoder.decode("ssss你还", "UTF-8"));
    }

}
