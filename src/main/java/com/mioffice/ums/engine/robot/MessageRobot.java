package com.mioffice.ums.engine.robot;

import com.mioffice.ums.engine.entity.bo.MsgRequest;
import com.mioffice.ums.engine.entity.bo.MsgResponse;

/**
 * <AUTHOR>
 * @since 2020/9/2
 */
public interface MessageRobot<Req extends MsgRequest, Resp extends MsgResponse> {

    /**
     * 初始化
     */
    void init();

    /**
     * 发送消息
     *
     * @param req
     * @exception
     * @return
     */
    Resp sendMsg(Req req) throws Exception;

    Resp sendMsgV1(Req req) throws Exception;

    /**
     * 关闭
     */
    void close();


    void setStatus(Status status);
}
