package com.mioffice.ums.engine.robot;

import com.mioffice.ums.engine.entity.bo.MsgRequest;
import com.mioffice.ums.engine.entity.bo.MsgResponse;

/**
 * <p>
 * 装饰者接口
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.19
 */
public class MessageRobotWrapper<Req extends MsgRequest, Resp extends MsgResponse> implements MessageRobot<Req, Resp> {

    private final MessageRobot<Req, Resp> messageRobot;

    public MessageRobotWrapper(MessageRobot<Req, Resp> messageRobot) {
        this.messageRobot = messageRobot;
    }

    @Override
    public void init() {
        messageRobot.init();
    }

    @Override
    public Resp sendMsg(Req req) throws Exception {
        return messageRobot.sendMsg(req);
    }

    @Override
    public Resp sendMsgV1(Req req) throws Exception {
        return messageRobot.sendMsgV1(req);
    }

    @Override
    public void close() {
        messageRobot.close();
    }

    @Override
    public void setStatus(Status status) {
        messageRobot.setStatus(status);
    }
}
