package com.mioffice.ums.engine.robot;

import com.larksuite.appframework.sdk.AppConfiguration;
import com.larksuite.appframework.sdk.LarkAppInstance;
import com.larksuite.appframework.sdk.LarkAppInstanceFactory;
import com.larksuite.appframework.sdk.client.MessageDestination;
import com.larksuite.appframework.sdk.client.MessageDestinations;
import com.larksuite.appframework.sdk.client.message.CardMessage;
import com.larksuite.appframework.sdk.exception.LarkClientException;
import com.mioffice.ums.engine.config.LarkSaasMapConfig;
import com.mioffice.ums.engine.entity.bo.MiWorkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.mioffice.ums.engine.message.MiWorkMessageHelper;
import com.mioffice.ums.engine.template.TemplateParse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 飞书消息机器人
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.03
 */
@Slf4j
public class MiWorkRobot<MiWorkReq extends MiWorkRequestBO, MiWorkResp extends MiWorkResponseBO>
        implements MessageRobot<MiWorkRequestBO, MiWorkResponseBO> {

    private static final String API_BASE = "https://open.f.mioffice.cn";
    private static final String API_BASE_SAAS = "https://open.feishu.cn";

    private final String appId;
    private final String appSecret;
    private final String appShortName;

    protected LarkAppInstance larkAppInstance;
    protected LarkAppInstance larkSaasAppInstance;
    protected MiWorkMessageHelper miWorkMessageHelper;
    protected Status status;

    private final TemplateParse templateParse;

    private final LarkSaasMapConfig larkSaasMapConfig;

    public MiWorkRobot(String appId, String appSecret, String appShortName, TemplateParse templateParse, LarkSaasMapConfig larkSaasMapConfig) {
        this.appId = appId;
        this.appSecret = appSecret;
        this.appShortName = appShortName;
        this.templateParse = templateParse;
        this.larkSaasMapConfig = larkSaasMapConfig;
        this.init();
    }

    /**
     * 初始化
     */
    @Override
    public void init() {

        AppConfiguration appConfiguration = new AppConfiguration();
        // app name, will be used to identify a app, should be unique
        appConfiguration.setAppShortName(this.appShortName);
        appConfiguration.setAppId(this.appId);
        appConfiguration.setAppSecret(this.appSecret);
        appConfiguration.setIsIsv(false);

        this.larkAppInstance = LarkAppInstanceFactory
                .builder(appConfiguration)
                .appTicketStorage(null)
                .apiBasePath(API_BASE)
                .create();

        this.larkAppInstance.init();

        try {
            if (this.larkSaasMapConfig != null && this.larkSaasMapConfig.getAppIdMap() != null && this.larkSaasMapConfig.getSaasIdSecretMap() != null) {
                // 实例化saas客户端
                AppConfiguration saasAppConfiguration = new AppConfiguration();
                // app name, will be used to identify a app, should be unique
                saasAppConfiguration.setAppShortName(this.appShortName);
                String appId = this.appId;
                String appSecret = this.appSecret;
                if (this.larkSaasMapConfig.getAppIdMap().containsKey(appId)) {
                    appId = this.larkSaasMapConfig.getAppIdMap().get(appId);
                    appSecret = this.larkSaasMapConfig.getSaasIdSecretMap().get(appId);
                }
                saasAppConfiguration.setAppId(appId);
                saasAppConfiguration.setAppSecret(appSecret);
                saasAppConfiguration.setIsIsv(false);
                this.larkSaasAppInstance = LarkAppInstanceFactory
                        .builder(saasAppConfiguration)
                        .appTicketStorage(null)
                        .apiBasePath(API_BASE_SAAS)
                        .create();

                this.larkSaasAppInstance.init();
            }
        } catch (Exception e) {
            log.error("Saas机器人实例化失败", e);
        }

        if (null == miWorkMessageHelper) {
            this.miWorkMessageHelper = new MiWorkMessageHelper(templateParse);
        }
    }

    /**
     * 发送消息接口
     *
     * @param miWorkRequestBO 消息体
     * @return 消息id
     * @throws LarkClientException
     */
    @Override
    public MiWorkResponseBO sendMsg(MiWorkRequestBO miWorkRequestBO) throws Exception {
        this.status.assertStatus();
        MessageDestination messageDestination = miWorkRequestBO.getSession().toMessageDestination();
        if (Objects.isNull(messageDestination)) {
            throw new LarkClientException("user destination is null");
        }
        String msgId =
                this.larkAppInstance.getLarkClient().sendChatMessage(messageDestination, miWorkRequestBO.getMessage());
        return new MiWorkResponseBO(msgId);
    }

    @Override
    public MiWorkResponseBO sendMsgV1(MiWorkRequestBO miWorkRequestBO) throws Exception {
        this.status.assertStatus();
        MessageDestination messageDestination = miWorkRequestBO.getSession().toMessageDestination();
        if (Objects.isNull(messageDestination)) {
            throw new LarkClientException("user destination is null");
        }
        String msgId = larkAppInstance.getLarkClient().sendChatMessageV1(messageDestination,
                (CardMessage) miWorkRequestBO.getMessage());
        return new MiWorkResponseBO(msgId);
    }

    @Override
    public void close() {
        this.status.setStopFlag((byte) RobotStatusEnum.ROBOT_OFF.getCode());
    }

    @Override
    public void setStatus(Status status) {
        this.status = status;
    }

    public LarkAppInstance getLarkSaasAppInstance() {
        return larkSaasAppInstance;
    }

    public LarkAppInstance getLarkAppInstance() {
        return larkAppInstance;
    }

    public MiWorkMessageHelper getMiWorkMessageHelper() {
        return miWorkMessageHelper;
    }

    public void setMiWorkMessageHelper(MiWorkMessageHelper miWorkMessageHelper) {
        this.miWorkMessageHelper = miWorkMessageHelper;
    }

    @Data
    public static class Session {

        private String openChatId;
        private String openId;
        private String userId;
        private String email;

        public MessageDestination toMessageDestination() {
            if (StringUtils.isNotBlank(openChatId)) {
                return MessageDestinations.chatId(openChatId);
            }
            if (StringUtils.isNotBlank(openId)) {
                return MessageDestinations.openId(openId);
            }
            if (StringUtils.isNotBlank(userId)) {
                return MessageDestinations.userId(userId);
            }
            if (StringUtils.isNotBlank(email)) {
                return MessageDestinations.email(email);
            }

            return null;
        }
    }

}
