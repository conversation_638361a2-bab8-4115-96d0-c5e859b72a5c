package com.mioffice.ums.engine.robot;

import com.github.kevinsawicki.http.HttpRequest;
import com.google.gson.reflect.TypeToken;
import com.mioffice.ums.engine.entity.bo.EmailRequestBO;
import com.mioffice.ums.engine.entity.bo.EmailResponseBO;
import com.mioffice.ums.engine.entity.bo.ResultBo;
import com.mioffice.ums.engine.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.09
 */

public class OutEmailRobot extends BaseEmailRobot {

    private Status status;
    private String url;
    private String multipleFileUrl;

    public OutEmailRobot(String url, String multipleFileUrl, Status status) {
        this.status = status;
        this.url = url;
        this.multipleFileUrl = multipleFileUrl;
    }

    @Override
    public void init() {

    }

    @Override
    public EmailResponseBO sendMsg(EmailRequestBO requestBO) throws Exception {

        if (Objects.nonNull(this.status)) {
            this.status.assertStatus();
        }


        Map<String, Object> params = new HashMap<>(5);
        params.put("address", requestBO.getTo());
        if (StringUtils.isNotBlank(requestBO.getCcTo())) {
            params.put("ccAddress", requestBO.getCcTo());
        }
        params.put("mailType", 25);
        params.put("title", requestBO.getTitle());
        params.put("body", requestBO.getHtml());
        List<String> attachUrlList = new ArrayList<>();
        // 附件
        if (StringUtils.isNotBlank(requestBO.getAttachUrl())) {
            attachUrlList = JsonUtils.parse(requestBO.getAttachUrl(), new TypeToken<List<String>>() {
            }.getType());
        }
        // 单附件跟多附件接口url不同
        HttpRequest httpRequest = HttpRequest.post(attachUrlList.size() > 1 ? multipleFileUrl : url);
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            httpRequest.part(entry.getKey(), entry.getValue().toString());
        }
        for (String attachUrl : attachUrlList) {
            try (InputStream is = getInputStream(attachUrl)) {
                String fileName = StringUtils.substringAfterLast(StringUtils.substringBefore(attachUrl, "?"), "/");
                if (StringUtils.isBlank(fileName)) {
                    fileName = UUID.randomUUID().toString();
                }
                httpRequest.part("attachedFile", fileName, getContentType(attachUrl), is);
            }
        }

        int status = httpRequest.code();
        if (status == HttpStatus.SC_OK) {
            String body = httpRequest.body();
            ResultBo resultBo = JsonUtils.parse(body, ResultBo.class);
            if (resultBo.getCode() == 0) {
                return new EmailResponseBO(UUID.randomUUID().toString().concat("-").concat(String.valueOf(resultBo.getCode())).concat("-").concat(resultBo.getResult()));
            } else {
                throw new RuntimeException(String.format("邮件发送失败 code = [%s], description = [%s]", resultBo.getCode(), resultBo.getDescription()));
            }
        } else {
            throw new RuntimeException(String.format("请求失败 httpStatus = [%s]", status));
        }
    }

    private String getContentType(String urlString) throws IOException {
        URL url = new URL(urlString);
        URLConnection connection = url.openConnection();
        return connection.getContentType();
    }

    private InputStream getInputStream(String urlString) throws IOException {
        URL url = new URL(urlString);
        return url.openStream();
    }

    @Override
    public EmailResponseBO sendMsgV1(EmailRequestBO emailRequestBO) throws Exception {
        return sendMsg(emailRequestBO);
    }

    @Override
    public void close() {

    }

    @Override
    public void setStatus(Status status) {
        this.status = status;
    }
}
