package com.mioffice.ums.engine.robot;

import com.google.gson.Gson;
import com.mioffice.ums.engine.entity.bo.SmsReqBo;
import com.mioffice.ums.engine.entity.bo.SmsResponseBO;
import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.xiaomi.sms.SmsUserconnectService;
import org.json.JSONObject;

/**
 * <p>
 * 短信机器人
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.03
 */
public class SmsRobot implements MessageRobot<SmsReqBo, SmsResponseBO> {

    private final String contentTypeKey;
    private final SmsUserconnectService.Iface client;
    private final Gson gson;
    private Status status;


    public SmsRobot(String contentTypeKey, SmsUserconnectService.Iface client, Gson gson) {
        this.contentTypeKey = contentTypeKey;
        this.client = client;
        this.gson = gson;
    }

    @Override
    public void init() {

    }

    @Override
    public SmsResponseBO sendMsg(SmsReqBo smsReqBo) throws Exception {
        JSONObject message = new JSONObject();
        message.put("msg", smsReqBo.getContent());
        String resultJsonStr = client.sendMessageByPhone(smsReqBo.getPhone(), contentTypeKey, message.toString());
        return gson.fromJson(resultJsonStr, SmsResponseBO.class);
    }

    @Override
    public SmsResponseBO sendMsgV1(SmsReqBo smsReqBo) throws Exception {
        return sendMsg(smsReqBo);
    }

    @Override
    public void close() {
        this.status.setStopFlag((byte) RobotStatusEnum.ROBOT_OFF.getCode());
    }

    @Override
    public void setStatus(Status status) {
        this.status = status;
    }
}
