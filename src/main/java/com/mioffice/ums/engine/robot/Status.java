package com.mioffice.ums.engine.robot;

import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.mioffice.ums.engine.exceptions.RobotStopException;
import lombok.Data;

/**
 * 机器人状态类
 *
 * <AUTHOR>
 */
@Data
public class Status {

    /**
     * 是否停用
     */
    private Byte stopFlag;

    public void assertStatus() throws RobotStopException {
        if (stopFlag == RobotStatusEnum.ROBOT_OFF.getCode()) {
            // 机器人停用
            throw new RobotStopException();
        }
    }
}