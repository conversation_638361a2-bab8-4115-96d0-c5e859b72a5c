package com.mioffice.ums.engine.robot.miwork;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.larksuite.appframework.sdk.client.MessageDestination;
import com.larksuite.appframework.sdk.client.MessageDestinations;
import com.larksuite.appframework.sdk.exception.LarkClientException;
import com.larksuite.appframework.sdk.utils.JsonUtil;
import com.mioffice.ums.engine.entity.bo.MiWorkJsonAndSdkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.enums.MsgFormatTypeEnum;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.utils.JsonUtils;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.19
 */
public class JsonAndSdkMiWorkRobot<MiWorkJsonAndSdkReq extends MiWorkJsonAndSdkRequestBO, MiWorkJsonAndSdkResp extends MiWorkResponseBO>
        extends MiWorkRobotWrapper<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> {

    private final MiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> messageRobot;

    public JsonAndSdkMiWorkRobot(MiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> messageRobot) {
        super(messageRobot);
        this.messageRobot = messageRobot;
    }

    public MiWorkResponseBO sendMsg(MiWorkJsonAndSdkReq miWorkJsonAndSdkRequestBO) throws Exception {
        if (miWorkJsonAndSdkRequestBO.getFormatTypeEnum() == MsgFormatTypeEnum.SDK) {
            MiWorkResponseBO miWorkResponseBO = super.sendMsg(miWorkJsonAndSdkRequestBO);
            miWorkResponseBO.setMessageJson(JsonUtils.toJson(miWorkJsonAndSdkRequestBO.getMessage()));
            return miWorkResponseBO;
        } else if (miWorkJsonAndSdkRequestBO.getFormatTypeEnum() == MsgFormatTypeEnum.JSON) {
            return this.sendJsonMsg(miWorkJsonAndSdkRequestBO);
        } else {
            // 兼容老板sdk消息
            MiWorkResponseBO miWorkResponseBO = super.sendMsg(miWorkJsonAndSdkRequestBO);
            miWorkResponseBO.setMessageJson(JsonUtils.toJson(miWorkJsonAndSdkRequestBO.getMessage()));
            return miWorkResponseBO;
        }
    }

    public MiWorkResponseBO sendMsgV1(MiWorkJsonAndSdkReq miWorkJsonAndSdkRequestBO) throws Exception {
        if (miWorkJsonAndSdkRequestBO.getFormatTypeEnum() == MsgFormatTypeEnum.SDK) {
            MiWorkResponseBO miWorkResponseBO = super.sendMsgV1(miWorkJsonAndSdkRequestBO);
            miWorkResponseBO.setMessageJson(JsonUtils.toJson(miWorkJsonAndSdkRequestBO.getMessage()));
            return miWorkResponseBO;
        } else if (miWorkJsonAndSdkRequestBO.getFormatTypeEnum() == MsgFormatTypeEnum.JSON) {
            return this.sendJsonMsgV1(miWorkJsonAndSdkRequestBO);
        } else {
            // 兼容老板sdk消息
            MiWorkResponseBO miWorkResponseBO = super.sendMsgV1(miWorkJsonAndSdkRequestBO);
            miWorkResponseBO.setMessageJson(JsonUtils.toJson(miWorkJsonAndSdkRequestBO.getMessage()));
            return miWorkResponseBO;
        }
    }

    private MiWorkResponseBO sendJsonMsg(MiWorkJsonAndSdkRequestBO miWorkJsonAndSdkRequestBO)
            throws LarkClientException, IOException {
        MessageDestination messageDestination = miWorkJsonAndSdkRequestBO.getSession().toMessageDestination();
        if (Objects.isNull(messageDestination)) {
            throw new LarkClientException("user destination is null");
        }

        String msg = replaceMessageDestination(messageDestination, miWorkJsonAndSdkRequestBO.getJsonMsg());
        String msgId = messageRobot.getLarkAppInstance().getLarkClient().sendChatMessage(msg);

        MiWorkResponseBO miWorkResponseBO = new MiWorkResponseBO(msgId);
        miWorkResponseBO.setMessageJson(msg);
        return miWorkResponseBO;
    }

    private MiWorkResponseBO sendJsonMsgV1(MiWorkJsonAndSdkRequestBO miWorkJsonAndSdkRequestBO)
            throws LarkClientException, IOException {
        MessageDestination messageDestination = miWorkJsonAndSdkRequestBO.getSession().toMessageDestination();
        if (Objects.isNull(messageDestination)) {
            throw new LarkClientException("user destination is null");
        }

        String msg = replaceMessageDestinationV1(messageDestination, miWorkJsonAndSdkRequestBO.getJsonMsg());
        // 目前仅支持群及个人发送
        String msgId = messageRobot.getLarkAppInstance().getLarkClient().sendChatMessageV1(msg,
                messageDestination instanceof MessageDestinations.ChatId ? "chat_id" : "user_id");

        MiWorkResponseBO miWorkResponseBO = new MiWorkResponseBO(msgId);
        miWorkResponseBO.setMessageJson(msg);
        return miWorkResponseBO;
    }

    private String replaceMessageDestination(MessageDestination messageDestination, String msg) throws IOException {
        Map<String, Object> jsonMsg = JsonUtil.larkFormatToObject(msg, new TypeReference<Map<String, Object>>() {
        });

        jsonMsg.remove("open_id");
        jsonMsg.remove("root_id");
        jsonMsg.remove("chat_id");
        jsonMsg.remove("user_id");
        jsonMsg.remove("email");

        String destIdentity = messageDestination.identity();
        if (messageDestination instanceof MessageDestinations.OpenId) {
            jsonMsg.put("open_id", destIdentity);
        } else if (messageDestination instanceof MessageDestinations.ChatId) {
            jsonMsg.put("chat_id", destIdentity);
        } else if (messageDestination instanceof MessageDestinations.UserId) {
            jsonMsg.put("user_id", destIdentity);
        } else if (messageDestination instanceof MessageDestinations.Email) {
            jsonMsg.put("email", destIdentity);
        }

        return JsonUtil.larkFormatToJsonString(jsonMsg);
    }

    private String replaceMessageDestinationV1(MessageDestination messageDestination, String msg) throws IOException {
        ObjectNode jsonMsg = JsonUtil.larkFormatToObject(msg, new TypeReference<ObjectNode>() {
        });

        jsonMsg.put("msg_type", "interactive");

        if (jsonMsg.has("content")) {
            jsonMsg.put("content", JsonUtil.larkFormatToJsonString(jsonMsg.get("content")));
        } else if (jsonMsg.has("card")) {
            if (jsonMsg.has("update_multi")) {
                boolean update_multi = jsonMsg.get("update_multi").asBoolean();
                if (jsonMsg.get("card").has("config")) {
                    ObjectNode config = (ObjectNode) jsonMsg.get("card").get("config");
                    config.remove("wide_screen_mode");
                    config.put("update_multi", update_multi);
                    jsonMsg.remove("update_multi");
                }
            }
            jsonMsg.put("content", JsonUtil.larkFormatToJsonString(jsonMsg.get("card")));
            jsonMsg.remove("card");
        }

        String destIdentity = messageDestination.identity();
        jsonMsg.put("receive_id", destIdentity);

        return JsonUtil.larkFormatToJsonString(jsonMsg);
    }
}
