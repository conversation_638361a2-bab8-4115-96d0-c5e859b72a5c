package com.mioffice.ums.engine.robot.miwork;

import com.larksuite.appframework.sdk.LarkAppInstance;
import com.mioffice.ums.engine.entity.bo.MiWorkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.message.MiWorkMessageHelper;
import com.mioffice.ums.engine.robot.MessageRobotWrapper;
import com.mioffice.ums.engine.robot.MiWorkRobot;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.19
 */
public class MiWorkRobotWrapper<MiWorkReq extends MiWorkRequestBO, MiWorkResp extends MiWorkResponseBO> extends MessageRobotWrapper<MiWorkRequestBO, MiWorkResponseBO> {

    private final MiWorkRobot<MiWorkReq, MiWorkResp> messageRobot;

    public MiWorkRobotWrapper(MiWorkRobot<MiWorkReq, MiWorkResp> messageRobot) {
        super(messageRobot);
        this.messageRobot = messageRobot;
    }

    @Override
    public MiWorkResponseBO sendMsg(MiWorkRequestBO req) throws Exception {
        return messageRobot.sendMsg(req);
    }

    @Override
    public MiWorkResponseBO sendMsgV1(MiWorkRequestBO req) throws Exception {
        return messageRobot.sendMsgV1(req);
    }

    public LarkAppInstance getLarkAppInstance() {
        return messageRobot.getLarkAppInstance();
    }

    public MiWorkMessageHelper getMiWorkMessageHelper() {
        return messageRobot.getMiWorkMessageHelper();
    }

}
