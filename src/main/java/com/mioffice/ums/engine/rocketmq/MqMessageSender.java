package com.mioffice.ums.engine.rocketmq;

import api.ClientFactory;
import api.config.ConfigKey;
import api.producer.NormalProducer;
import com.mioffice.ums.engine.config.RocketMqProperties;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

import static com.mioffice.ums.engine.enums.constants.RedisConstants.TOPIC_PRODUCER_GROUP;

/**
 * MQ消息发送服务
 * 独立的消息发送组件，避免循环依赖
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class MqMessageSender {

    @Autowired
    private RocketMqProperties rocketMqProperties;

    private NormalProducer producer;

    @PostConstruct
    public void init() {
        try {
            producer = ClientFactory.createNormalProducer(getProducerProperties());
            producer.start();
            log.info("MQ消息发送服务初始化成功");
        } catch (Exception e) {
            log.error("初始化MQ消息发送服务失败", e);
        }
    }

    /**
     * 发送消息到指定Topic
     *
     * @param topicName           Topic名称
     * @param messageUserInfoList 消息列表
     * @return 是否发送成功
     */
    public boolean sendToTopic(String topicName, List<MessageUserInfo> messageUserInfoList) {
        if (producer == null) {
            log.error("MQ生产者未初始化");
            return false;
        }

        try {
            for (MessageUserInfo messageUserInfo : messageUserInfoList) {
                String messageBody = JsonUtils.toJson(messageUserInfo);
                Message message = new Message(topicName, messageBody.getBytes());
                message.setKeys(messageUserInfo.getId().toString());

                producer.send(message);
                log.debug("消息发送成功, topic: {}, messageId: {}", topicName, messageUserInfo.getId());
            }
            return true;
        } catch (InterruptedException e) {
            log.warn("发送消息失败, topic: {}, messageUserInfoList: {}", topicName, messageUserInfoList, e);
            Thread.currentThread().interrupt(); // 重新设置线程的中断标志
            return false;
        } catch (Exception e) {
            log.error("发送消息失败, topic: {}, messageUserInfoList: {}", topicName, messageUserInfoList, e);
            return false;
        }
    }

    /**
     * 发送单条消息到指定Topic
     *
     * @param topicName       Topic名称
     * @param messageUserInfo 消息
     * @return 是否发送成功
     */
    public boolean sendToTopic(String topicName, MessageUserInfo messageUserInfo) {
        return sendToTopic(topicName, Collections.singletonList(messageUserInfo));
    }

    public boolean sendDelayToTopic(String topicName, MessageUserInfo messageUserInfo, long deliverTime) {
        if (producer == null) {
            log.error("MQ生产者未初始化");
            return false;
        }

        try {
            String messageBody = JsonUtils.toJson(messageUserInfo);
            Message message = new Message(topicName, messageBody.getBytes());
            message.setKeys(messageUserInfo.getId().toString());
            message.setStartDeliverTime(deliverTime);

            producer.send(message);
            log.debug("消息发送成功, topic: {}, messageId: {}", topicName, messageUserInfo.getId());
            return true;
        } catch (InterruptedException e) {
            log.warn("发送消息失败, topic: {}, messageUserInfo: {}, 线程被中断", topicName, messageUserInfo, e);
            Thread.currentThread().interrupt(); // 重新设置线程的中断标志
            return false;
        } catch (Exception e) {
            log.error("发送消息失败, topic: {}", topicName, e);
            return false;
        }
    }

    private Properties getProducerProperties() {
        Properties properties = new Properties();
        properties.setProperty(ConfigKey.ACCESS_KEY, rocketMqProperties.getAk());
        properties.setProperty(ConfigKey.SECRET_KEY, rocketMqProperties.getSk());
        properties.setProperty(ConfigKey.NAME_SERVER_ADDR, rocketMqProperties.getNameServer());
        properties.setProperty(ConfigKey.PRODUCER_GROUP, TOPIC_PRODUCER_GROUP);
        properties.setProperty(ConfigKey.ENABLE_MSG_TRACE, "true");
        return properties;
    }
} 