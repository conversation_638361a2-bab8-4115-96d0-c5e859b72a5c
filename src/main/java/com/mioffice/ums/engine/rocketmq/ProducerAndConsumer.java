package com.mioffice.ums.engine.rocketmq;

import api.consumer.NormalConsumer;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

/**
 * @ClassName RocketMqProducerAndConsumer
 * @Description RocketMQ生产者和消费者包装类
 * <AUTHOR>
 * @Date 2024/3/26 16:07
 **/

@Data
@Slf4j
public class ProducerAndConsumer {
    private String robotKey;
    private String topic;
    private MqMessageSender producer;
    private NormalConsumer consumer;

    @Async
    public void produce(List<MessageUserInfo> userInfoList) {
        try {
            boolean result = producer.sendToTopic(topic, userInfoList);
            if (!result) {
                log.error("消息入队失败,robotKey = [{}],topic = [{}],messageCount = [{}]",
                        robotKey, topic, userInfoList.size());
            } else {
                log.debug("消息入队成功,robotKey = [{}],topic = [{}],messageCount = [{}]",
                        robotKey, topic, userInfoList.size());
            }
        } catch (Exception e) {
            log.error("消息入队异常,robotKey = [{}],topic = [{}],messageCount = [{}]",
                    robotKey, topic, userInfoList.size(), e);
        }
    }
}
