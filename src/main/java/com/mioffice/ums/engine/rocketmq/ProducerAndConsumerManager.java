package com.mioffice.ums.engine.rocketmq;

import api.ClientFactory;
import api.config.ConfigKey;
import api.consumer.NormalConsumer;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.mioffice.ums.engine.cache.TopicCache;
import com.mioffice.ums.engine.config.MqProperties;
import com.mioffice.ums.engine.config.RocketMqProperties;
import com.mioffice.ums.engine.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.engine.entity.info.EmailRobotInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.RobotInfo;
import com.mioffice.ums.engine.entity.info.SmsRobotInfo;
import com.mioffice.ums.engine.manager.UnifiedTopicManager;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import static com.mioffice.ums.engine.enums.constants.RedisConstants.TOPIC_CONSUMER_GROUP;

/**
 * @ClassName RocketMqHelper
 * @Description RocketMq生产者消费者管理类
 * <AUTHOR>
 * @Date 2024/3/26 14:46
 **/

@Component
@Slf4j
public class ProducerAndConsumerManager {

    @Autowired
    private RocketMqProperties rocketMqProperties;

    @Autowired
    private TopicCache topicCache;

    @Autowired
    private RobotInfoMapper robotInfoMapper;

    @Autowired
    private EmailRobotInfoMapper emailRobotInfoMapper;

    @Autowired
    private SmsRobotInfoMapper smsRobotInfoMapper;

    @Autowired
    private UnifiedTopicManager unifiedTopicManager;

    @Autowired
    private UniversalMessageConsumer universalMessageConsumer;

    @Autowired
    private MqMessageSender mqMessageSender;

    @Autowired
    private MqProperties mqProperties;

    private Map<String, ProducerAndConsumer> robotProducerAndConsumerMap = Maps.newConcurrentMap();


    public void init() {
        // 初始化统一Topic消费者
        initUnifiedTopicConsumer();

        // 初始化专属队列消费者
        initSpecificTopicConsumers();
    }

    /**
     * 初始化统一Topic消费者
     */
    private void initUnifiedTopicConsumer() {
        if (!unifiedTopicManager.isUnifiedTopicEnabled()) {
            log.info("统一Topic功能未启用，跳过消费者初始化");
            return;
        }

        try {
            String unifiedTopicName = unifiedTopicManager.getUnifiedTopicName();

            // 创建统一消费者
            NormalConsumer unifiedConsumer = ClientFactory.createNormalConsumer(
                    getConsumerProperties(unifiedTopicName),
                    universalMessageConsumer);
            unifiedConsumer.subscribe(unifiedTopicName, "*");
            unifiedConsumer.start();

            log.info("统一Topic消费者初始化成功, topic: {}", unifiedTopicName);
        } catch (Exception e) {
            log.error("初始化统一Topic消费者失败", e);
        }
    }

    /**
     * 初始化专属队列消费者
     */
    private void initSpecificTopicConsumers() {
        // 飞书
        List<String> robotKeyList =
                robotInfoMapper.selectList(Wrappers.<RobotInfo>lambdaQuery().eq(RobotInfo::getStopFlag, 1)).stream()
                        .map(RobotInfo::getAppId).collect(Collectors.toList());
        // 邮箱
        robotKeyList.addAll(emailRobotInfoMapper.selectList(
                        Wrappers.<EmailRobotInfo>lambdaQuery().eq(EmailRobotInfo::getStopFlag, 1)).stream()
                .map(EmailRobotInfo::getSender).collect(Collectors.toList()));

        // 短信
        robotKeyList.addAll(
                smsRobotInfoMapper.selectList(Wrappers.<SmsRobotInfo>lambdaQuery().eq(SmsRobotInfo::getStopFlag, 1))
                        .stream().map(SmsRobotInfo::getContentTypeKey).collect(Collectors.toList()));

        // 移除不走专属队列的机器人
        if (!mqProperties.getExclusiveQueue().getExcludeBots().isEmpty()) {
            robotKeyList.removeAll(mqProperties.getExclusiveQueue().getExcludeBots());
        }

        if (CollectionUtils.isNotEmpty(robotKeyList)) {
            robotKeyList.forEach(this::tryInitConsumerForRobot);
        }
    }

    /**
     * 发送消息到统一Topic
     *
     * @param messageUserInfoList 消息列表
     * @return 是否发送成功
     */
    public boolean sendToUnifiedTopic(List<MessageUserInfo> messageUserInfoList) {
        try {
            String topicName = unifiedTopicManager.getUnifiedTopicName();
            boolean result = mqMessageSender.sendToTopic(topicName, messageUserInfoList);
            if (result) {
                log.debug("消息发送到统一Topic成功, topic: {}, messageCount: {}", topicName, messageUserInfoList.size());
            }
            return result;
        } catch (Exception e) {
            log.error("发送消息到统一Topic失败", e);
            return false;
        }
    }

    /**
     * 发送消息到专属Topic
     *
     * @param topicName           Topic名称
     * @param messageUserInfoList 消息列表
     * @return 是否发送成功
     */
    public boolean sendToSpecificTopic(String topicName, List<MessageUserInfo> messageUserInfoList) {
        try {
            boolean result = mqMessageSender.sendToTopic(topicName, messageUserInfoList);
            if (result) {
                log.debug("消息发送到专属Topic成功, topic: {}, messageCount: {}", topicName, messageUserInfoList.size());
            }
            return result;
        } catch (Exception e) {
            log.error("发送消息到专属Topic失败, topic: {}", topicName, e);
            return false;
        }
    }

    /**
     * 判断机器人ID是否应该使用统一Topic
     *
     * @param robotId 机器人ID
     * @return true表示应该使用统一Topic
     */
    public boolean shouldUseUnifiedTopic(String robotId) {
        return unifiedTopicManager.shouldUseUnifiedTopic(robotId);
    }

    /**
     * 获取统一Topic使用的机器人ID集合
     *
     * @return 机器人ID集合
     */
    public Set<String> getUnifiedTopicRobotIds() {
        return unifiedTopicManager.getUnifiedTopicRobotIds();
    }

    /**
     * 判断统一Topic功能是否启用
     *
     * @return true表示启用
     */
    public boolean isUnifiedTopicEnabled() {
        return unifiedTopicManager.isUnifiedTopicEnabled();
    }

    /**
     * 为机器人初始化消费者
     */
    private void tryInitConsumerForRobot(String robotKey) {
        List<AppTopicDetailBO> appTopicDetailBOList = topicCache.getByRobotId(robotKey);
        log.info("机器人{},专属队列:{}", robotKey, CollectionUtils.isNotEmpty(appTopicDetailBOList) ?
                JsonUtils.toJson(appTopicDetailBOList) : StringUtils.EMPTY);

        if (CollectionUtils.isNotEmpty(appTopicDetailBOList)) {
            AppTopicDetailBO appTopicDetailBO = appTopicDetailBOList.get(
                    ThreadLocalRandom.current().nextInt(0, appTopicDetailBOList.size()));
            if (Objects.nonNull(appTopicDetailBO)) {
                String topic = appTopicDetailBO.getTopic();

                try {
                    // 创建消费者，使用通用的消息处理逻辑
                    NormalConsumer consumer = ClientFactory.createNormalConsumer(
                            getConsumerProperties(topic),
                            universalMessageConsumer);

                    consumer.subscribe(topic, "*");
                    consumer.start();

                    ProducerAndConsumer producerAndConsumer = new ProducerAndConsumer();
                    producerAndConsumer.setRobotKey(robotKey);
                    producerAndConsumer.setTopic(topic);
                    producerAndConsumer.setProducer(mqMessageSender);
                    producerAndConsumer.setConsumer(consumer);

                    robotProducerAndConsumerMap.put(robotKey, producerAndConsumer);

                    log.info("机器人{}专属队列消费者初始化成功, topic: {}", robotKey, topic);

                } catch (Exception ex) {
                    log.error("init RocketMqConsumer for robotKey:{} fail", robotKey, ex);
                }
            }
        }
    }

    private Properties getConsumerProperties(String topic) {
        Properties properties = new Properties();
        properties.setProperty(ConfigKey.ACCESS_KEY, rocketMqProperties.getAk());
        properties.setProperty(ConfigKey.SECRET_KEY, rocketMqProperties.getSk());
        properties.setProperty(ConfigKey.NAME_SERVER_ADDR, rocketMqProperties.getNameServer());
        properties.setProperty(ConfigKey.CONSUMER_GROUP, topic + "-" + TOPIC_CONSUMER_GROUP);
        properties.setProperty(ConfigKey.ENABLE_MSG_TRACE, "true");
        return properties;
    }

    public Optional<ProducerAndConsumer> getRobotProducerAndConsumer(String robotKey) {
        if (robotProducerAndConsumerMap.containsKey(robotKey)) {
            return Optional.ofNullable(robotProducerAndConsumerMap.get(robotKey));
        } else {
            tryInitConsumerForRobot(robotKey);
            return Optional.ofNullable(robotProducerAndConsumerMap.get(robotKey));
        }
    }

    public Set<String> getEnableTopicRobotIdSet() {
        return robotProducerAndConsumerMap.keySet();
    }

    public Optional<ProducerAndConsumer> getSysProducerAndConsumer(String sysId) {
        AppTopicDetailBO appTopicDetail = topicCache.getBySysId(sysId);
        if (Objects.nonNull(appTopicDetail) && StringUtils.isNotBlank(appTopicDetail.getTopic())) {
            return robotProducerAndConsumerMap.values()
                    .stream()
                    .filter(robotProducerAndConsumer -> robotProducerAndConsumer.getRobotKey()
                            .equals(appTopicDetail.getTopic())).findFirst();
        }
        return Optional.empty();
    }

}
