package com.mioffice.ums.engine.rocketmq;

import api.ClientFactory;
import api.config.ConfigKey;
import api.consumer.NormalConsumer;
import api.producer.NormalProducer;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mioffice.ums.engine.cache.TopicCache;
import com.mioffice.ums.engine.config.RocketMqProperties;
import com.mioffice.ums.engine.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.engine.entity.info.EmailRobotInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.RobotInfo;
import com.mioffice.ums.engine.entity.info.SmsRobotInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import static com.mioffice.ums.engine.enums.constants.RedisConstants.TOPIC_CONSUMER_GROUP;
import static com.mioffice.ums.engine.enums.constants.RedisConstants.TOPIC_FIELD;
import static com.mioffice.ums.engine.enums.constants.RedisConstants.TOPIC_PRODUCER_GROUP;
import com.mioffice.ums.engine.limiter.LimiterManager;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import com.mioffice.ums.engine.sender.EmailMessageSender;
import com.mioffice.ums.engine.sender.MiWorkMessageSenderV1;
import com.mioffice.ums.engine.sender.SmsMessageSender;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * @ClassName RocketMqHelper
 * @Description RocketMq生产者消费者管理类
 * <AUTHOR>
 * @Date 2024/3/26 14:46
 **/

@Component
@Slf4j
public class ProducerAndConsumerManager {

    @Autowired
    private RocketMqProperties rocketMqProperties;

    @Autowired
    private TopicCache topicCache;

    @Autowired
    private RobotInfoMapper robotInfoMapper;

    @Autowired
    private EmailRobotInfoMapper emailRobotInfoMapper;

    @Autowired
    private SmsRobotInfoMapper smsRobotInfoMapper;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @Autowired
    private LimiterManager limiterManager;

    @Autowired
    private MiWorkMessageSenderV1 miWorkMessageSenderV1;

    @Autowired
    private EmailMessageSender emailMessageSender;

    @Autowired
    private SmsMessageSender smsMessageSender;

    @Autowired
    private RedissonClient redissonClient;

    @NacosValue(value = "${ums.engine.retry.limit:50}", autoRefreshed = true)
    private Integer retryLimit;

    @NacosValue(value = "${ums.engine.rate-limit.miwork.rate-ms:1000}", autoRefreshed = true)
    private long miworkRateMs;
    @NacosValue(value = "${ums.engine.rate-limit.miwork.rate:50}", autoRefreshed = true)
    private long miworkRate;
    @NacosValue(value = "${ums.engine.rate-limit.email.rate-ms:1000}", autoRefreshed = true)
    private long emailRateMs;
    @NacosValue(value = "${ums.engine.rate-limit.email.rate:10}", autoRefreshed = true)
    private long emailRate;
    @NacosValue(value = "${ums.engine.rate-limit.sms.rate-ms:1000}", autoRefreshed = true)
    private long smsRateMs;
    @NacosValue(value = "${ums.engine.rate-limit.sms.rate:10}", autoRefreshed = true)
    private long smsRate;
    @NacosValue(value = "${ums.engine.topic.exclude-bots}", autoRefreshed = true)
    private String topicExcludeBots;
    @NacosValue(value = "${ums.engine.topic.delay-min-seconds:1}", autoRefreshed = true)
    private int topicDelayMinSeconds;
    @NacosValue(value = "${ums.engine.topic.delay-max-seconds:10}", autoRefreshed = true)
    private int topicDelayMaxSeconds;

    private Map<String, ProducerAndConsumer> robotProducerAndConsumerMap = Maps.newConcurrentMap();

    private static final String LOG_FMT =
            "ms = [{}],robotKey = [{}],topic = [{}],messageId = [{}],extraId = [{}],channel = [{}]";

    private static final String LOG_FMT1 =
            "robotKey = [{}],topic = [{}],messageId = [{}],extraId = [{}],channel = [{}]";

    public void init() {
        // 飞书
        List<String> robotKeyList =
                robotInfoMapper.selectList(Wrappers.<RobotInfo>lambdaQuery().eq(RobotInfo::getStopFlag, 1)).stream()
                        .map(RobotInfo::getAppId).collect(
                                Collectors.toList());
        // 邮箱
        robotKeyList.addAll(emailRobotInfoMapper.selectList(
                        Wrappers.<EmailRobotInfo>lambdaQuery().eq(EmailRobotInfo::getStopFlag, 1)).stream()
                .map(EmailRobotInfo::getSender).collect(
                        Collectors.toList()));

        // 短信
        robotKeyList.addAll(
                smsRobotInfoMapper.selectList(Wrappers.<SmsRobotInfo>lambdaQuery().eq(SmsRobotInfo::getStopFlag, 1))
                        .stream().map(SmsRobotInfo::getContentTypeKey).collect(
                                Collectors.toList()));

        // 移除不走专属队列的机器人
        if (StringUtils.isNotBlank(topicExcludeBots)) {
            List<String> topicExcludeBotList = Splitter.on(",").splitToList(topicExcludeBots);
            robotKeyList.removeAll(topicExcludeBotList);
        }

        if (CollectionUtils.isNotEmpty(robotKeyList)) {
            robotKeyList.forEach(this::tryInitProducerAndConsumer);
        }
    }

    private void tryInitProducerAndConsumer(String robotKey) {
        if (robotProducerAndConsumerMap.containsKey(robotKey)) {
            return;
        }
        List<AppTopicDetailBO> appTopicDetailBOList = topicCache.getByRobotId(robotKey);
        log.info("机器人{},专属队列:{}", robotKey, CollectionUtils.isNotEmpty(appTopicDetailBOList) ?
                JsonUtils.toJson(appTopicDetailBOList) : StringUtils.EMPTY);
        if (CollectionUtils.isNotEmpty(appTopicDetailBOList)) {
            AppTopicDetailBO appTopicDetailBO = appTopicDetailBOList.get(
                    ThreadLocalRandom.current().nextInt(0, appTopicDetailBOList.size()));
            if (Objects.nonNull(appTopicDetailBO)) {

                String topic = appTopicDetailBO.getTopic();

                NormalProducer producer = ClientFactory.createNormalProducer(getProducerProperties());

                NormalConsumer consumer = ClientFactory.createNormalConsumer(
                        getConsumerProperties(topic),
                        (list, consumeConcurrentlyContext) -> {
                            for (int i = 0; i < list.size(); i++) {
                                String body = new String(list.get(i).getBody());
                                MessageUserInfo messageUserInfo =
                                        JsonUtils.parse(body, MessageUserInfo.class);
                                if (Objects.isNull(messageUserInfo) ||
                                        messageUserInfo.getRetryCount() >= retryLimit) {
                                    continue;
                                }
                                boolean result = true;
                                // 大于0代表被限流
                                long waitMs = 0;
                                RLock lock = redissonClient.getLock(
                                        String.format("%s:lock:%s", TOPIC_FIELD, messageUserInfo.getId().toString()));
                                try {
                                    if (lock.tryLock()) {
                                        // 发送消息
                                        if (MessageChannelEnum.MI_WORK ==
                                                MessageChannelEnum.get(messageUserInfo.getChannel())) {
                                            waitMs = limiterManager.getLimiter(messageUserInfo.getAppId(),
                                                            miworkRateMs,
                                                            miworkRate)
                                                    .acquire();
                                            if (waitMs > 0) {
                                                log.info(
                                                        "小米办公限流," + LOG_FMT,
                                                        waitMs,
                                                        messageUserInfo.getAppId(),
                                                        topic,
                                                        messageUserInfo.getId(),
                                                        messageUserInfo.getExtraId(),
                                                        messageUserInfo.getChannel());
                                                result = false;
                                            } else {
                                                result = miWorkMessageSenderV1.send(messageUserInfo);
                                            }
                                        } else if (MessageChannelEnum.EMAIL ==
                                                MessageChannelEnum.get(messageUserInfo.getChannel())) {
                                            waitMs = limiterManager.getLimiter(messageUserInfo.getAppId(),
                                                            emailRateMs, emailRate)
                                                    .acquire();
                                            if (waitMs > 0) {
                                                log.info(
                                                        "邮件限流," + LOG_FMT,
                                                        waitMs,
                                                        messageUserInfo.getAppId(),
                                                        topic,
                                                        messageUserInfo.getId(),
                                                        messageUserInfo.getExtraId(),
                                                        messageUserInfo.getChannel());
                                                result = false;
                                            } else {
                                                result = emailMessageSender.send(messageUserInfo);
                                            }
                                        } else if (MessageChannelEnum.SMS ==
                                                MessageChannelEnum.get(messageUserInfo.getChannel())) {
                                            waitMs =
                                                    limiterManager.getLimiter(messageUserInfo.getAppId(), smsRateMs,
                                                                    smsRate)
                                                            .acquire();
                                            if (waitMs > 0) {
                                                log.info(
                                                        "短信限流," + LOG_FMT,
                                                        waitMs,
                                                        messageUserInfo.getAppId(),
                                                        topic,
                                                        messageUserInfo.getId(),
                                                        messageUserInfo.getExtraId(), messageUserInfo.getChannel());
                                                result = false;
                                            } else {
                                                result = smsMessageSender.send(messageUserInfo);
                                            }
                                        }
                                    }
                                } catch (Throwable e) {
                                    result = false;
                                    log.error(
                                            "消息发送失败," + LOG_FMT1,
                                            messageUserInfo.getAppId(),
                                            topic,
                                            messageUserInfo.getId(),
                                            messageUserInfo.getExtraId(), messageUserInfo.getChannel(),
                                            e);
                                } finally {
                                    lock.unlock();
                                    if (!result) {
                                        MessageUserInfo messageUserInfoInDb =
                                                messageUserInfoMapper.selectByIdFromMaster(
                                                        messageUserInfo.getId());
                                        if (Objects.nonNull(messageUserInfoInDb)
                                                && messageUserInfoInDb.getMessageStatus() != null
                                                && messageUserInfoInDb.getMessageStatus() == MessageStatusEnum.SENDING.getStatus()) {
                                            messageUserInfoInDb.setMessageStatus(MessageStatusEnum.SEND_FAIL.getStatus());
                                            messageUserInfoMapper.updateById(messageUserInfoInDb);
                                        }
                                    }
                                }
                            }
                            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                        });

                try {
                    producer.start();

                    consumer.subscribe(appTopicDetailBO.getTopic(), "*");
                    consumer.start();

                    ProducerAndConsumer producerAndConsumer = new ProducerAndConsumer();
                    producerAndConsumer.setRobotKey(robotKey);
                    producerAndConsumer.setTopic(appTopicDetailBO.getTopic());
                    producerAndConsumer.setProducer(producer);
                    producerAndConsumer.setConsumer(consumer);

                    robotProducerAndConsumerMap.put(robotKey, producerAndConsumer);

                } catch (Exception ex) {
                    log.error("init RocketMqProducerAndConsumer for robotKey:{} fail", robotKey, ex);
                }
            }
        }
    }

    private Properties getProducerProperties() {
        Properties properties = new Properties();
        properties.setProperty(ConfigKey.ACCESS_KEY, rocketMqProperties.getAk());
        properties.setProperty(ConfigKey.SECRET_KEY, rocketMqProperties.getSk());
        properties.setProperty(ConfigKey.NAME_SERVER_ADDR, rocketMqProperties.getNameServer());
        properties.setProperty(ConfigKey.PRODUCER_GROUP, TOPIC_PRODUCER_GROUP);
        properties.setProperty(ConfigKey.ENABLE_MSG_TRACE, "true");
        return properties;
    }

    private Properties getConsumerProperties(String topic) {
        Properties properties = new Properties();
        properties.setProperty(ConfigKey.ACCESS_KEY, rocketMqProperties.getAk());
        properties.setProperty(ConfigKey.SECRET_KEY, rocketMqProperties.getSk());
        properties.setProperty(ConfigKey.NAME_SERVER_ADDR, rocketMqProperties.getNameServer());
        properties.setProperty(ConfigKey.CONSUMER_GROUP, topic + "-" + TOPIC_CONSUMER_GROUP);
        properties.setProperty(ConfigKey.ENABLE_MSG_TRACE, "true");
        return properties;
    }

    public Optional<ProducerAndConsumer> getRobotProducerAndConsumer(String robotKey) {
        if (robotProducerAndConsumerMap.containsKey(robotKey)) {
            return Optional.ofNullable(robotProducerAndConsumerMap.get(robotKey));
        } else {
            tryInitProducerAndConsumer(robotKey);
            return Optional.ofNullable(robotProducerAndConsumerMap.get(robotKey));
        }
    }

    public Set<String> getEnableTopicRobotIdSet() {
        return robotProducerAndConsumerMap.keySet();
    }

    public Optional<ProducerAndConsumer> getSysProducerAndConsumer(String sysId) {
        AppTopicDetailBO appTopicDetail = topicCache.getBySysId(sysId);
        if (Objects.nonNull(appTopicDetail) && StringUtils.isNotBlank(appTopicDetail.getTopic())) {
            return robotProducerAndConsumerMap.values()
                    .stream()
                    .filter(robotProducerAndConsumer -> robotProducerAndConsumer.getRobotKey()
                            .equals(appTopicDetail.getTopic())).findFirst();
        }
        return Optional.empty();
    }

}
