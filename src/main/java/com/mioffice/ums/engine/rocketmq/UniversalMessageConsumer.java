package com.mioffice.ums.engine.rocketmq;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
// Splitter不再需要，直接使用Set配置
import com.mioffice.ums.engine.config.MqProperties;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.exceptions.LimitException;
import com.mioffice.ums.engine.limiter.WindowRateLimiter;
import com.mioffice.ums.engine.limiter.WindowRateLimiterManager;
import com.mioffice.ums.engine.limiter.LimiterManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.sender.EmailMessageSender;
import com.mioffice.ums.engine.sender.MiWorkMessageSender;
import com.mioffice.ums.engine.sender.MiWorkMessageSenderV1;
import com.mioffice.ums.engine.sender.SmsMessageSender;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;


import static com.mioffice.ums.engine.enums.constants.RedisConstants.TOPIC_FIELD;

/**
 * 通用消息消费者
 * 用于处理所有RocketMQ消息（专属队列和统一Topic）
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Component
public class UniversalMessageConsumer implements MessageListenerConcurrently {

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @Autowired
    private WindowRateLimiterManager windowRateLimiterManager;

    @Autowired
    private LimiterManager limiterManager;

    @Autowired
    private MiWorkMessageSenderV1 miWorkMessageSenderV1;

    @Autowired
    private MiWorkMessageSender miWorkMessageSender;

    @Autowired
    private EmailMessageSender emailMessageSender;

    @Autowired
    private SmsMessageSender smsMessageSender;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private MqProperties mqProperties;

    @Autowired
    private MqMessageSender mqMessageSender;


    private static final String LOG_FMT = "appId = [{}],topic = [{}],messageId = [{}],extraId = [{}],channel = [{}]";

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> messages, 
                                                  ConsumeConcurrentlyContext context) {
        for (MessageExt message : messages) {
            try {
                processMessage(message);
            } catch (Exception e) {
                log.error("处理RocketMQ消息失败, messageId: {}, topic: {}", 
                        message.getMsgId(), message.getTopic(), e);
                // 继续处理其他消息，不阻塞整个批次
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 处理单条消息
     */
    private void processMessage(MessageExt message) {
        String body = new String(message.getBody());
        MessageUserInfo messageUserInfo = JsonUtils.parse(body, MessageUserInfo.class);
        
        if (Objects.isNull(messageUserInfo) || messageUserInfo.getRetryCount() >= mqProperties.getRetry().getLimit()) {
            log.warn("消息无效或重试次数超限, messageId: {}, retryCount: {}", 
                    message.getMsgId(), 
                    messageUserInfo != null ? messageUserInfo.getRetryCount() : "null");
            return;
        }

        boolean result = true;
        long waitMs = 0;
        String topic = message.getTopic();
        
        RLock lock = redissonClient.getLock(
                String.format("%s:lock:%s", TOPIC_FIELD, messageUserInfo.getId().toString()));
        boolean isLock = false;
        try {
            isLock = lock.tryLock();
            if (isLock) {
                log.info("获取锁成功, messageId: {}, 线程id为：{}", messageUserInfo.getId(), Thread.currentThread().getId());
                // 根据渠道发送消息，使用时间窗口限流器
                MessageChannelEnum channel = MessageChannelEnum.get(messageUserInfo.getChannel());
                switch (channel) {
                    case MI_WORK:
                        WindowRateLimiter limiter = windowRateLimiterManager.getLimiter(
                                messageUserInfo.getAppId(), 
                                mqProperties.getRateLimit().getMiwork().getSecondLimit(), 
                                mqProperties.getRateLimit().getMiwork().getMinuteLimit());

                        if (limiter.tryAcquireQuietly(messageUserInfo.getAppId())) {
                            dispatchMiWorkMessage(topic, messageUserInfo);
                        } else {
                            log.info("小米办公限流," + LOG_FMT,
                                    messageUserInfo.getAppId(), topic,
                                    messageUserInfo.getId(), messageUserInfo.getExtraId(),
                                    messageUserInfo.getChannel());
                            result = false;
                        }
                        break;
                        
                    case EMAIL:
                        waitMs = limiterManager.getLimiter(messageUserInfo.getAppId(), 
                                mqProperties.getRateLimit().getEmail().getRateMs(), 
                                mqProperties.getRateLimit().getEmail().getRate()).acquire();
                        if (waitMs > 0) {
                            log.info("邮件限流," + LOG_FMT, waitMs, messageUserInfo.getAppId(), topic, 
                                    messageUserInfo.getId(), messageUserInfo.getExtraId(), messageUserInfo.getChannel());
                            result = false;
                        } else {
                            emailMessageSender.send(messageUserInfo);
                        }
                        break;
                        
                    case SMS:
                        waitMs = limiterManager.getLimiter(messageUserInfo.getAppId(), 
                                mqProperties.getRateLimit().getSms().getRateMs(), 
                                mqProperties.getRateLimit().getSms().getRate()).acquire();
                        if (waitMs > 0) {
                            log.info("短信限流," + LOG_FMT, waitMs, messageUserInfo.getAppId(), topic, 
                                    messageUserInfo.getId(), messageUserInfo.getExtraId(), messageUserInfo.getChannel());
                            result = false;
                        } else {
                            smsMessageSender.send(messageUserInfo);
                        }
                        break;
                        
                    default:
                        log.error("不支持的消息渠道: {}", channel);
                        result = false;
                        break;
                }
            } else {
                log.warn("获取锁失败, messageId: {}, 线程id为：{}", messageUserInfo.getId(), Thread.currentThread().getId());
                result = false;
            }
        } catch (Throwable e) {
            result = false;
            log.error("消息发送失败," + LOG_FMT, messageUserInfo.getAppId(), topic, 
                    messageUserInfo.getId(), messageUserInfo.getExtraId(), messageUserInfo.getChannel(), e);
        } finally {
            boolean isUnlockSuccess = false;
            if (lock.isHeldByCurrentThread()) {
                log.warn("释放锁成功, messageId: {}, 线程id为：{}", messageUserInfo.getId(), Thread.currentThread().getId());
                lock.unlock();
                isUnlockSuccess = true;
            }

            if (isLock && !isUnlockSuccess) {
                log.warn("释放锁失败, messageId: {}, 线程id为：{}", messageUserInfo.getId(), Thread.currentThread().getId());
            }
            
            // 处理发送失败的情况
            if (!result) {
                long deliverTime = System.currentTimeMillis() + ThreadLocalRandom.current().nextInt(
                        mqProperties.getUnifiedTopic().getDelayMinSeconds(), 
                        mqProperties.getUnifiedTopic().getDelayMaxSeconds()) * 1000L;
                mqMessageSender.sendDelayToTopic(topic, messageUserInfo, deliverTime);
            }
        }
    }

    
    /**
     * 分发小米办公消息。
     * topic = {@link MqProperties.UnifiedTopic#getName()} 单独处理，走白名单逻辑
     * 其他 走正常 V1 逻辑
     *
     * @param topic           消息主题
     * @param messageUserInfo 消息用户信息
     * @return 发送结果
     */
    private boolean dispatchMiWorkMessage(String topic, MessageUserInfo messageUserInfo) {
        // 非统一主题直接使用 V1 发送器
        if (!isUnifiedTopic(topic)) {
            return miWorkMessageSenderV1.send(messageUserInfo);
        }
        
        // 统一主题：根据白名单配置选择发送器
        return isInWhiteList(messageUserInfo.getAppId()) 
            ? miWorkMessageSenderV1.send(messageUserInfo)  // 白名单内使用 V1 发送器
            : miWorkMessageSender.send(messageUserInfo);   // 白名单外使用普通发送器
    }
    
    /**
     * 检查是否为统一主题
     */
    private boolean isUnifiedTopic(String topic) {
        return mqProperties.getUnifiedTopic().getName().equals(topic);
    }
    
    /**
     * 检查应用ID是否在白名单中
     * 直接使用Set配置，无需字符串分割和缓存
     */
    private boolean isInWhiteList(String appId) {
        // 白名单功能未启用或配置为空，默认认为在白名单中（使用 V1 发送器）
        if (!mqProperties.getLarkApiUpgrade().isEnableWhiteList() || 
            mqProperties.getLarkApiUpgrade().getWhiteList().isEmpty()) {
            return true;
        }
        
        // 直接使用Set配置检查白名单
        return mqProperties.getLarkApiUpgrade().getWhiteList().contains(appId);
    }
} 