package com.mioffice.ums.engine.sender;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.mioffice.ums.engine.cache.MessageFilterCache;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.ErrorTypeEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.utils.MessageUtil;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.01
 */
@Slf4j
public abstract class DefaultMessageSender implements MessageSender {

    private final MessageFilterCache messageFilterCache;
    private final IdDeduplicateManager idDeduplicateManager;
    private final MessageUserInfoMapper messageUserInfoMapper;

    /**
     * 和限流相关的错误码,来自于
     * https://open.f.mioffice.cn/document/ukTMukTMukTM/ugjM14COyUjL4ITN
     * https://open.f.mioffice.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create#c98c3220
     */
    private static final Set<Integer> limitErrCodeSet = new HashSet<>(Arrays.asList(99991400, 230020, 11232, 11233,
            11247, 90217, 90227, 190005, 232019, 1000005));

    protected DefaultMessageSender(MessageFilterCache messageFilterCache,
                                   IdDeduplicateManager idDeduplicateManager,
                                   MessageUserInfoMapper messageUserInfoMapper) {
        this.messageFilterCache = messageFilterCache;
        this.idDeduplicateManager = idDeduplicateManager;
        this.messageUserInfoMapper = messageUserInfoMapper;
    }

    protected boolean check(MessageUserInfo messageUserInfo) {
        // 去重判断
        if (idDeduplicateManager.contains(MessageChannelEnum.get(messageUserInfo.getChannel()),
                messageUserInfo.getId())) {
            log.info("消息重复，丢弃发送 id = {},extraId = {}", messageUserInfo.getId(), messageUserInfo.getExtraId());
            return false;
        }

        // 消息正在中断
        if (messageFilterCache.contain(messageUserInfo.getExtraId())) {
            log.info("消息中断，丢弃发送 id = {},extraId = {}", messageUserInfo.getId(), messageUserInfo.getExtraId());
            messageUserInfo.setUpdateTime(System.currentTimeMillis());
            messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
            messageUserInfo.setErrorLog("消息中断，丢弃发送");

            // 更新前将重试次数置空，防止将已经更新的重试次数覆盖
            messageUserInfo.setRetryCount(null);
            messageUserInfoMapper.updateById(messageUserInfo);
            return false;
        }

        MessageUserInfo messageUserInfoInDb = messageUserInfoMapper.selectByIdFromMaster(messageUserInfo.getId());
        if (Objects.isNull(messageUserInfoInDb)) {
            log.warn("消息不存在 id = {},extraId = {}", messageUserInfo.getId(), messageUserInfo.getExtraId());

            MessageUserInfo update = MessageUserInfo.newUpdateTimeInstant();
            update.setId(messageUserInfo.getId());
            update.setMessageStatus(MessageStatusEnum.SEND_FAIL.getStatus());
            update.setErrorLog("消息不存在，等待下次调度");

            messageUserInfoMapper.updateById(messageUserInfo);
            return false;
        }
        // 发送成功、已中断、撤回的不再发送
        if (MessageStatusEnum.SEND_SUCCESS.getStatus() == messageUserInfoInDb.getMessageStatus() ||
                MessageStatusEnum.SEND_INTERRUPT.getStatus() == messageUserInfoInDb.getMessageStatus() ||
                MessageStatusEnum.RETRACTED.getStatus() == messageUserInfoInDb.getMessageStatus()
        ) {
            messageUserInfo.setMessageStatus(messageUserInfoInDb.getMessageStatus());
            log.info("消息 status = {}，丢弃发送 id = {},extraId = {}", messageUserInfoInDb.getMessageStatus(),
                    messageUserInfo.getId(), messageUserInfo.getExtraId());
            return false;
        }
        return true;
    }

    protected void updateSendFail(MessageUserInfo messageUserInfo, Throwable e) {
        messageUserInfo.setUpdateTime(System.currentTimeMillis());
        messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_FAIL.getStatus());
        messageUserInfo.setErrorLog(Optional.of(e.getMessage()).orElse(StringUtils.EMPTY));
        // 解析错误码
        errType(e, MessageChannelEnum.get(messageUserInfo.getChannel()), messageUserInfo);

        // 更新前将重试次数置空，防止将已经更新的重试次数覆盖
        messageUserInfo.setRetryCount(null);
        messageUserInfoMapper.updateById(messageUserInfo);
    }

    public static void errType(Throwable e, MessageChannelEnum channelEnum, MessageUserInfo messageUserInfo) {
        if (StringUtils.isNotBlank(e.getMessage())) {
            if (channelEnum == MessageChannelEnum.MI_WORK) {
                if (MessageUtil.templateError(e.getMessage())) {
                    log.info(
                            "id = [{}] 错误状态=[{}] 消息状态=[{}] error_log = [{}] retryCount = [{}]的消息消息状态更改为了中断状态",
                            ErrorTypeEnum.LARK_CARD_ERR, messageUserInfo.getId(),
                            messageUserInfo.getMessageStatus(),
                            messageUserInfo.getErrorLog(), messageUserInfo.getRetryCount());
                    messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                } else {
                    ObjectNode err400Json = MessageUtil.feishu400ErrorResponseJson(e.getMessage());
                    if (Objects.nonNull(err400Json)) {
                        if (err400Json.has("code") && !limitErrCodeSet.contains(err400Json.get("code").asInt())) {
                            log.info(
                                    "id = [{}] 错误状态=[{}] 消息状态=[{}] error_log = [{}] retryCount = [{}]的消息消息状态更改为了中断状态",
                                    ErrorTypeEnum.LARK_CARD_ERR, messageUserInfo.getId(),
                                    messageUserInfo.getMessageStatus(),
                                    messageUserInfo.getErrorLog(), messageUserInfo.getRetryCount());
                            messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                        }
                    } else {
//                        ObjectNode err500Json = MessageUtil.feishu500ErrorResponseJson(e.getMessage());
//                        if (Objects.nonNull(err500Json)) {
//                            log.info(
//                                    "id = [{}] 错误状态=[{}] 消息状态=[{}] error_log = [{}] retryCount = [{}]的消息消息状态更改为了中断状态",
//                                    ErrorTypeEnum.LARK_CARD_ERR, messageUserInfo.getId(),
//                                    messageUserInfo.getMessageStatus(),
//                                    messageUserInfo.getErrorLog(), messageUserInfo.getRetryCount());
//                            messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
//                        } else {
                            String code = MessageUtil.parseErrorCode(e.getMessage());
                            if (StringUtils.isBlank(code)) {
                                return;
                            }
                            ErrorTypeEnum errorTypeEnum = ErrorTypeEnum.getByCode(code);
                            messageUserInfo.setErrorType(errorTypeEnum.getType());
                            if (errorTypeEnum != ErrorTypeEnum.NO_KNOW) {
                                log.info(
                                        "id = [{}] 错误状态=[{}] 消息状态=[{}] error_log = [{}] retryCount = [{}]的消息消息状态更改为了中断状态",
                                        errorTypeEnum, messageUserInfo.getId(),
                                        messageUserInfo.getMessageStatus(),
                                        messageUserInfo.getErrorLog(), messageUserInfo.getRetryCount());
                                messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                            }
//                        }
                    }
                }
            } else if (channelEnum == MessageChannelEnum.EMAIL) {
                if (e.getMessage().startsWith("553") || e.getMessage().startsWith(
                        "421")) {
                    log.info(
                            "id = [{}]  error_log = [{}] retryCount = [{}]的消息消息状态更改为了中断状态",
                            messageUserInfo.getId(),
                            messageUserInfo.getErrorLog(), messageUserInfo.getRetryCount());
                    messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                }
            } else if (channelEnum == MessageChannelEnum.SMS) {
                if (!e.getMessage().startsWith("消息发送失败 code = [101004]")) {
                    log.info(
                            "id = [{}]  error_log = [{}] retryCount = [{}]的消息消息状态更改为了中断状态",
                            messageUserInfo.getId(),
                            messageUserInfo.getErrorLog(), messageUserInfo.getRetryCount());
                    messageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                }
            }
        }
    }
}
