package com.mioffice.ums.engine.sender;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.mioffice.ums.engine.cache.MessageFilterCache;
import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.manager.EmailRobotManager;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.01
 */
@Slf4j
@Component
public class EmailMessageSender extends DefaultMessageSender {

    private final IdDeduplicateManager idDeduplicateManager;
    private final MessageUserInfoMapper messageUserInfoMapper;
    private final EmailRobotManager emailRobotManager;

    @Autowired
    private TemplateCache templateCache;

    @NacosValue(value = "${ums.engine.executives:}", autoRefreshed = true)
    private String executives;

    public EmailMessageSender(MessageFilterCache messageFilterCache,
                              IdDeduplicateManager idDeduplicateManager,
                              MessageUserInfoMapper messageUserInfoMapper,
                              EmailRobotManager emailRobotManager) {
        super(messageFilterCache, idDeduplicateManager, messageUserInfoMapper);
        this.idDeduplicateManager = idDeduplicateManager;
        this.messageUserInfoMapper = messageUserInfoMapper;
        this.emailRobotManager = emailRobotManager;
    }

    @Override
    public boolean send(MessageUserInfo messageUserInfo) {
        // 发送前检查
        if (!check(messageUserInfo)) {
            return true;
        }

        log.info("EmailMessageSender send email begin");

        String sender = messageUserInfo.getAppId();
        boolean result = true;
        try {
            String msgId = emailRobotManager.sendMsg(sender, messageUserInfo).getMsgId();

            MessageUserInfo updateMessageUserInfo = MessageUserInfo.newUpdateTimeInstant();
            updateMessageUserInfo.setId(messageUserInfo.getId());
            updateMessageUserInfo.setMessageId(msgId);
            updateMessageUserInfo.setMessageStatus(MessageStatusEnum.SEND_SUCCESS.getStatus());

            messageUserInfoMapper.updateById(updateMessageUserInfo);
            idDeduplicateManager.add(MessageChannelEnum.EMAIL, messageUserInfo.getId());
            log.info("消息发送成功 id = [{}]", updateMessageUserInfo.getId());
        } catch (Exception e) {
            log.error("消息发送失败", e);
            updateSendFail(messageUserInfo, e);
            result = false;
        }

        log.info("EmailMessageSender send email end");
        return result;
    }
}
