package com.mioffice.ums.engine.sender;

import cn.hutool.core.bean.BeanUtil;
import com.mioffice.ums.engine.cache.MessageFilterCache;
import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.MessageUserSaasInfo;
import com.mioffice.ums.engine.enums.ContentFlagEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.enums.SendSaasEnum;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.MessageV1MiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserSaasInfoMapper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.template.TemplateParse;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2024.03.28
 */
@Slf4j
@Component
public class MiWorkMessageSenderV1 extends DefaultMessageSender {

    private final MessageUserInfoMapper messageUserInfoMapper;
    private final MessageUserSaasInfoMapper messageUserSaasInfoMapper;
    private final MessageV1MiWorkRobotManager messageV1MiWorkRobotManager;
    private final IdDeduplicateManager idDeduplicateManager;


    public MiWorkMessageSenderV1(MessageUserInfoMapper messageUserInfoMapper,
                                 MessageUserSaasInfoMapper messageUserSaasInfoMapper,
                                 MessageV1MiWorkRobotManager messageV1MiWorkRobotManager,
                                 IdDeduplicateManager idDeduplicateManager,
                                 MessageFilterCache messageFilterCache) {
        super(messageFilterCache, idDeduplicateManager, messageUserInfoMapper);
        this.messageUserInfoMapper = messageUserInfoMapper;
        this.messageUserSaasInfoMapper = messageUserSaasInfoMapper;
        this.messageV1MiWorkRobotManager = messageV1MiWorkRobotManager;
        this.idDeduplicateManager = idDeduplicateManager;
    }

    @Override
    public boolean send(MessageUserInfo messageUserInfo) {
        // 发送前检查
        if (!check(messageUserInfo)) {
            return true;
        }

        MiWorkRobot.Session session = new MiWorkRobot.Session();
        session.setUserId(messageUserInfo.getUsername());
        session.setEmail(messageUserInfo.getEmail());
        session.setOpenChatId(messageUserInfo.getChatId());

        boolean result = true;
        try {
            MiWorkResponseBO miWorkResponseBO =
                    messageV1MiWorkRobotManager.sendMsg(messageUserInfo.getAppId(), messageUserInfo, session);

            MessageUserInfo updateMessageUserInfo = MessageUserInfo.newUpdateTimeInstant();
            updateMessageUserInfo.setId(messageUserInfo.getId());
            updateMessageUserInfo.setMessageId(miWorkResponseBO.getMsgId());
            updateMessageUserInfo.setMessageStatus(MessageStatusEnum.SEND_SUCCESS.getStatus());

            if (messageUserInfo.getContentFlag() == ContentFlagEnum.LARK_CONTENT.getFlag()) {
                log.info("FinalContent = [{}]", miWorkResponseBO.getMessageJson());
                updateMessageUserInfo.setFinalContent(miWorkResponseBO.getMessageJson());
            }

            messageUserInfoMapper.updateById(updateMessageUserInfo);
            idDeduplicateManager.add(MessageChannelEnum.MI_WORK, messageUserInfo.getId());
            log.info("消息发送成功 messageId = [{}], extraId = [{}]",
                    messageUserInfo.getId(),
                    messageUserInfo.getExtraId());
        } catch (Throwable e) {
            log.error("消息发送失败 messageId = [{}], extraId = [{}]",
                    messageUserInfo.getId(),
                    messageUserInfo.getExtraId(), e);
            updateSendFail(messageUserInfo, e);
            result = false;
        }
        if (result) {
            try {
                sendLarkSaas(messageUserInfo);
            } catch (Throwable e) {
                log.info("保存saas消息错误", e);
            }
        }

        return result;
    }

    protected void sendLarkSaas(MessageUserInfo messageUserInfo) {
        if (messageUserInfo.getIsSendSaas() == null || SendSaasEnum.YES.getCode() != messageUserInfo.getIsSendSaas()) {
            return;
        }

        MessageUserSaasInfo messageUserSaasInfo = BeanUtil.copyProperties(messageUserInfo, MessageUserSaasInfo.class,
                "id", "messageId", "messageStatus", "errorLog", "retryCount", "createTime", "updateTime");
        long time = System.currentTimeMillis();
        messageUserSaasInfo.setCreateTime(time);
        messageUserSaasInfo.setUpdateTime(time);
        messageUserSaasInfo.setUserInfoId(messageUserInfo.getId());
        messageUserSaasInfo.setMessageStatus(MessageStatusEnum.SENDING.getStatus());
        messageUserSaasInfo.setIsV1Channel((byte) 1);
        messageUserSaasInfoMapper.insert(messageUserSaasInfo);
    }
}
