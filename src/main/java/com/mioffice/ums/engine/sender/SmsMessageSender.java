package com.mioffice.ums.engine.sender;

import com.mioffice.ums.engine.cache.MessageFilterCache;
import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.entity.bo.SmsResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.SmsRobotManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.02
 */
@Slf4j
@Component
public class SmsMessageSender extends DefaultMessageSender {

    private final MessageUserInfoMapper messageUserInfoMapper;
    private final IdDeduplicateManager idDeduplicateManager;
    private final SmsRobotManager smsRobotManager;

    @Autowired
    private TemplateCache templateCache;

    protected SmsMessageSender(MessageFilterCache messageFilterCache,
                               IdDeduplicateManager idDeduplicateManager,
                               MessageUserInfoMapper messageUserInfoMapper,
                               SmsRobotManager smsRobotManager) {
        super(messageFilterCache, idDeduplicateManager, messageUserInfoMapper);
        this.messageUserInfoMapper = messageUserInfoMapper;
        this.idDeduplicateManager = idDeduplicateManager;
        this.smsRobotManager = smsRobotManager;
    }

    @Override
    public boolean send(MessageUserInfo messageUserInfo) {
        // 发送前检查
        if (!check(messageUserInfo)) {
            return true;
        }

        boolean result = true;

        try {
            String contentKey = messageUserInfo.getAppId();
            SmsResponseBO smsResponseBO = smsRobotManager.sendMsg(contentKey, messageUserInfo);
            if (smsResponseBO.getErrorCode() == 0) {
                MessageUserInfo updateMessageUserInfo = MessageUserInfo.newUpdateTimeInstant();
                updateMessageUserInfo.setId(messageUserInfo.getId());
                updateMessageUserInfo.setMessageId(smsResponseBO.getMsgId());
                updateMessageUserInfo.setMessageStatus(MessageStatusEnum.SEND_SUCCESS.getStatus());

                messageUserInfoMapper.updateById(updateMessageUserInfo);
                idDeduplicateManager.add(MessageChannelEnum.SMS, messageUserInfo.getId());
            } else {
                throw new RuntimeException(
                        String.format("消息发送失败 code = [%d], desc =[%s]", smsResponseBO.getErrorCode(),
                                smsResponseBO.getDesc()));
            }
        } catch (Exception e) {
            log.error("消息发送失败", e);
            updateSendFail(messageUserInfo, e);
            result = false;
        }
        return result;
    }
}
