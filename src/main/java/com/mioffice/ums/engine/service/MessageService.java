package com.mioffice.ums.engine.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 消息逻辑
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.11
 */
public interface MessageService extends IService<MessageUserInfo> {

    /**
     * 无专属队列的失败重试
     */
    void retrySendFailMsg();

    /**
     * 有专属队列的失败重试
     */
    void retrySendFailMsgOfTopic();

    /**
     * 无专属队列的发送中重试
     */
    void retrySendingMsg(Long timeMills);

    /**
     * 有专属队列的发送中重试
     */
    void retrySendingMsgOfTopic(Long timeMills);

    /**
     * 统一Topic失败消息重试
     */
    void retryUnifiedTopicFailedMessages();

    /**
     * 统一Topic发送中消息重试
     */
    void retryUnifiedTopicSendingMessages(Long timeMills);

    /**
     * 删除过期的消息
     * 凌晨2点
     *
     * @param timeMills
     */
    void deleteExpiredMessage(Long timeMills);

    /**
     * 扫描发送成功的飞书消息的已读情况
     */
    void scanMessageRead(Integer shardIndex, Integer shardTotal);

    /**
     * 扫描发送成功的且未读的飞书消息appId
     */
    void scanUnReadAppId();

    void asyncUploadImage(Set<String> imageUrlList, MessageTemplateInfo messageTemplateInfo, String appId)
            throws Exception;

    void asyncUploadSaasImage(Set<String> imageUrlList, MessageTemplateInfo messageTemplateInfo, String appId) throws Exception;

    void asyncRetractMessage(String extraId);

    void asyncSendMsg2EventBus(List<MessageUserInfo> userInfoList);
}
