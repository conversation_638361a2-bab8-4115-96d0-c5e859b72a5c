package com.mioffice.ums.engine.service.grpc.client;

import com.google.protobuf.Empty;
import com.mioffice.ums.engine.entity.bo.AddTemplateRequestBO;
import com.xiaomi.info.grpc.client.annotation.RpcClientAutowired;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddMessageRuleRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddMessageRuleResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AddTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApplyCancelQueryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApplyCancelQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApplyUserSearchQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListQueryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalListQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalPassOrRejectQueryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppApprovalPassOrRejectQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListQueryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppListServiceBlockingClient;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppManagerSearchQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppStartOrStopQueryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppStartOrStopQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppSummary;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppUseApplyQueryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppUseApplyQueryInfoResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppUserSearchQueryInfo;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.CancelAppTopicRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.CancelAppTopicResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.CreateAppTopicRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.CreateAppTopicResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteMessageTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.DeleteMessageTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRuleDetailRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetMessageRuleDetailResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetSysBotListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetSysBotListResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetTemplatePageRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.GetTemplatePageResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageTemplateDetailRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.MessageTemplateDetailResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppTopicRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryAppTopicResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryRobotAppTopicRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.QueryRobotAppTopicResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.SysSummaryResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateMessageRuleRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateMessageRuleResponse;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.UpdateTemplateResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/9/19 11:00 上午
 * version: 1.0.0
 */
@Slf4j
@Component
public class AppListGrpcClient {

    @RpcClientAutowired("ums-open-app-list-server")
    AppListServiceBlockingClient client;

    public AppListQueryInfoResponse getAppPage(String name, String username, Long page, Long size, String appName,
                                               String managerUsernameList, String applyUsername, String channel,
                                               String appSysStatus, String beginDate, String endDate) {
        AppListQueryInfo.Builder appListQueryInfoBuilder = AppListQueryInfo.newBuilder();
        appListQueryInfoBuilder
                .setPage(page)
                .setSize(size);
        if (StringUtils.isNotBlank(name)) {
            appListQueryInfoBuilder.setLoginName(name);
        }
        if (StringUtils.isNotBlank(username)) {
            appListQueryInfoBuilder.setLoginUsername(username);
        }
        if (StringUtils.isNotBlank(appName)) {
            appListQueryInfoBuilder.setAppName(appName);
        }
        if (StringUtils.isNotBlank(managerUsernameList)) {
            appListQueryInfoBuilder.setAppManagers(managerUsernameList);
        }
        if (StringUtils.isNotBlank(applyUsername)) {
            appListQueryInfoBuilder.setApplyUsername(applyUsername);
        }
        if (StringUtils.isNotBlank(channel)) {
            appListQueryInfoBuilder.setChannel(channel);
        }
        if (StringUtils.isNotBlank(appSysStatus)) {
            appListQueryInfoBuilder.setAppSysStatus(appSysStatus);
        }
        if (StringUtils.isNotBlank(beginDate)) {
            appListQueryInfoBuilder.setBeginDate(beginDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            appListQueryInfoBuilder.setEndDate(endDate);
        }
        AppListQueryInfo appListQueryInfo = appListQueryInfoBuilder.build();
        return client.appListQuery(appListQueryInfo);
    }

    public AppStartOrStopQueryInfoResponse appStart(Long id, String username, String name, List<String> roleList) {
        AppStartOrStopQueryInfo.Builder appStartOrStopQueryInfoBuilder = AppStartOrStopQueryInfo.newBuilder();
        appStartOrStopQueryInfoBuilder.setId(id);
        appStartOrStopQueryInfoBuilder.setName(name);
        appStartOrStopQueryInfoBuilder.setUsername(username);
        appStartOrStopQueryInfoBuilder.addAllRole(roleList);
        AppStartOrStopQueryInfo build = appStartOrStopQueryInfoBuilder.build();
        return client.appStart(build);
    }

    public AppStartOrStopQueryInfoResponse appStop(Long id, String username, String name, List<String> roleList) {
        AppStartOrStopQueryInfo.Builder appStartOrStopQueryInfoBuilder = AppStartOrStopQueryInfo.newBuilder();
        appStartOrStopQueryInfoBuilder.setId(id);
        appStartOrStopQueryInfoBuilder.setName(name);
        appStartOrStopQueryInfoBuilder.setUsername(username);
        appStartOrStopQueryInfoBuilder.addAllRole(roleList);
        AppStartOrStopQueryInfo build = appStartOrStopQueryInfoBuilder.build();
        return client.appStop(build);
    }

    public AppUseApplyQueryInfoResponse appUseApply(Long id, String applyName, String applyUsername) {
        AppUseApplyQueryInfo.Builder appUseApplyQueryInfoBuilder = AppUseApplyQueryInfo.newBuilder();
        appUseApplyQueryInfoBuilder.setId(id);
        appUseApplyQueryInfoBuilder.setApplyName(applyName);
        appUseApplyQueryInfoBuilder.setApplyUsername(applyUsername);
        AppUseApplyQueryInfo build = appUseApplyQueryInfoBuilder.build();
        return client.appUseApply(build);
    }

    public AppManagerSearchQueryInfoResponse appManagerSearch(String searchWord) {
        AppUserSearchQueryInfo.Builder builder = AppUserSearchQueryInfo.newBuilder();
        builder.setSearchWord(searchWord);
        AppUserSearchQueryInfo build = builder.build();
        return client.appManagerSearch(build);
    }

    public AppApplyUserSearchQueryInfoResponse appApplyUserSearch(String searchWord) {
        AppUserSearchQueryInfo.Builder builder = AppUserSearchQueryInfo.newBuilder();
        builder.setSearchWord(searchWord);
        AppUserSearchQueryInfo build = builder.build();
        return client.appApplyUserSearch(build);
    }

    public AppApplyCancelQueryInfoResponse appApplyCancel(String username, String name, Long id,
                                                          List<String> roleList) {
        AppApplyCancelQueryInfo.Builder builder = AppApplyCancelQueryInfo.newBuilder();
        builder.setUsername(username).setName(name).setId(id);
        builder.addAllRole(roleList);
        AppApplyCancelQueryInfo build = builder.build();
        return client.appApplyCancel(build);
    }

    public AppApprovalListQueryInfoResponse getAppApprovalPage(String username, Long page, Long size, String appName,
                                                               String managerUsernameList, String channel,
                                                               String beginDate, String endDate) {
        AppApprovalListQueryInfo.Builder builder = AppApprovalListQueryInfo.newBuilder();
        builder.setPage(page).setSize(size).setUsername(username);
        if (StringUtils.isNotBlank(appName)) {
            builder.setAppName(appName);
        }
        if (StringUtils.isNotBlank(managerUsernameList)) {
            builder.setAppManagers(managerUsernameList);
        }
        if (StringUtils.isNotBlank(channel)) {
            builder.setChannel(channel);
        }
        if (StringUtils.isNotBlank(beginDate)) {
            builder.setBeginDate(beginDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            builder.setEndDate(endDate);
        }
        AppApprovalListQueryInfo build = builder.build();
        return client.appApprovalListQuery(build);
    }

    public AppApprovalPassOrRejectQueryInfoResponse appApprovalPass(String username, String name, Long id) {
        AppApprovalPassOrRejectQueryInfo.Builder builder = AppApprovalPassOrRejectQueryInfo.newBuilder();
        builder.setUsername(username).setName(name).setId(id);
        AppApprovalPassOrRejectQueryInfo build = builder.build();
        return client.appApprovalPass(build);
    }

    public AppApprovalPassOrRejectQueryInfoResponse appApprovalReject(String username, String name, Long id,
                                                                      String reason) {
        AppApprovalPassOrRejectQueryInfo.Builder builder = AppApprovalPassOrRejectQueryInfo.newBuilder();
        builder.setUsername(username).setId(id).setName(name).setReason(reason);
        AppApprovalPassOrRejectQueryInfo build = builder.build();
        return client.appApprovalReject(build);
    }

    /**
     * 根据模板id获取模板详情
     *
     * @param id
     * @return
     */
    public MessageTemplateDetailResponse getMessageTemplateDetail(Long id) {
        MessageTemplateDetailRequest.Builder builder = MessageTemplateDetailRequest.newBuilder();
        builder.setMessageTemplateId(id);
        return client.getMessageTemplateDetail(builder.build());
    }

    /**
     * 根据模板编号获取模板详情
     *
     * @param bizId
     * @return
     */
    public MessageTemplateDetailResponse getMessageTemplateDetail(String bizId) {
        MessageTemplateDetailRequest.Builder builder = MessageTemplateDetailRequest.newBuilder();
        builder.setMessageTemplateBizId(bizId);
        return client.getMessageTemplateDetail(builder.build());
    }

    // 模板删除
    public DeleteMessageTemplateResponse deleteMessageTemplate(List<Long> ids, String username, String name,
                                                               List<String> roleList) {
        DeleteMessageTemplateRequest.Builder builder = DeleteMessageTemplateRequest.newBuilder();
        builder.addAllIds(ids);
        builder.addAllRole(roleList);
        builder.setName(name);
        builder.setUsername(username);
        return client.deleteMessageTemplate(builder.build());
    }

    // 模板列表接口(邮件、飞书、短信共用)
    public GetTemplatePageResponse getTemplatePage(Long page, Long size, Byte channel, Long appSysId,
                                                   String templateName) {
        GetTemplatePageRequest.Builder builder = GetTemplatePageRequest.newBuilder();
        builder.setAppSysId(appSysId).setChannel((int) channel).setPage(page).setSize(size)
                .setTemplateName(templateName);
        return client.getTemplatePage(builder.build());
    }

    // 添加模板
    public AddTemplateResponse addTemplate(AddTemplateRequestBO addTemplateRequestBO) {
        Long appSysId = addTemplateRequestBO.getAppSysId();
        Integer channel = addTemplateRequestBO.getChannel();
        String name = addTemplateRequestBO.getName();
        String templateContent = addTemplateRequestBO.getTemplateContent();
        String templateName = addTemplateRequestBO.getTemplateName();
        String username = addTemplateRequestBO.getUsername();
        List<String> botBizIds = addTemplateRequestBO.getBotBizIds();
        List<String> roleList = addTemplateRequestBO.getRoleList();
        AddTemplateRequest.Builder builder = AddTemplateRequest.newBuilder();
        builder.setAppSysId(appSysId)
                .setChannel(channel)
                .setName(name)
                .setTemplateContent(templateContent)
                .setTemplateName(templateName)
                .setUsername(username)
                .addAllBotBizId(botBizIds).addAllRole(roleList);
        return client.addTemplate(builder.build());
    }

    // 获取机器人列表
    public GetSysBotListResponse getBotList(Long appSysId, Integer channel) {
        GetSysBotListRequest.Builder builder = GetSysBotListRequest.newBuilder();
        builder.setAppSysId(appSysId);
        builder.setChannel(channel);
        return client.getSysBotList(builder.build());
    }

    public List<AppSummary> getAppSummary() {
        SysSummaryResponse summaryResponse = client.getSysSummaryList(Empty.newBuilder().build());
        return summaryResponse.getAppSummaryList();
    }

    public UpdateTemplateResponse updateTemplate(UpdateTemplateRequest updateTemplateRequest) {
        return client.updateTemplate(updateTemplateRequest);
    }

    public AddMessageRuleResponse addMessageRule(Long appSysId, String appId, String ruleName, String callback,
                                                 String placeholder,
                                                 String username) {
        AddMessageRuleRequest.Builder builder = AddMessageRuleRequest.newBuilder();

        builder.setAppSysId(Objects.nonNull(appSysId) ? appSysId : 0L);
        builder.setAppId(appId);
        builder.setRuleName(ruleName);
        builder.setCallback(callback);
        builder.setPlaceholder(placeholder);
        builder.setUsername(StringUtils.isNotBlank(username) ? username : StringUtils.EMPTY);
        return client.addMessageRule(builder.build());
    }

    public UpdateMessageRuleResponse updateMessageRule(Long id, String ruleName, String callback, String placeholder,
                                                       String username) {
        UpdateMessageRuleRequest.Builder builder = UpdateMessageRuleRequest.newBuilder();
        builder.setId(id);
        builder.setRuleName(ruleName);
        builder.setCallback(callback);
        builder.setPlaceholder(placeholder);
        builder.setUsername(StringUtils.isNotBlank(username) ? username : StringUtils.EMPTY);
        return client.updateMessageRule(builder.build());
    }

    public GetMessageRuleDetailResponse getMessageRuleDetail(Long appSysId, Long ruleId) {
        GetMessageRuleDetailRequest.Builder builder = GetMessageRuleDetailRequest.newBuilder();
        builder.setAppSysId(Objects.nonNull(appSysId) ? appSysId : 0L);
        builder.setMessageRuleId(ruleId);
        return client.getMessageRuleDetail(builder.build());
    }

    public CreateAppTopicResponse createAppTopic(String name,
                                                 String appId,
                                                 String createBy,
                                                 String untilLevel2Leader,
                                                 String itOperators,
                                                 String desc) {
        CreateAppTopicRequest.Builder builder = CreateAppTopicRequest.newBuilder();
        builder.setName(name)
                .setAppId(appId)
                .setCreateBy(createBy)
                .setUntilLevel2Leader(untilLevel2Leader)
                .setItOperators(itOperators)
                .setDesc(desc);
        return client.createAppTopic(builder.build());
    }

    public CancelAppTopicResponse cancelAppTopic(String appId, String updateBy) {
        CancelAppTopicRequest.Builder builder = CancelAppTopicRequest.newBuilder();
        builder.setAppId(appId).setUpdateBy(updateBy);
        return client.cancelAppTopic(builder.build());
    }

    public QueryAppTopicResponse getAppTopic(String appId) {
        QueryAppTopicRequest.Builder builder = QueryAppTopicRequest.newBuilder();
        builder.setAppId(appId);
        return client.getAppTopic(builder.build());
    }

    public QueryRobotAppTopicResponse getRobotTopic(String robotId) {
        QueryRobotAppTopicRequest.Builder builder = QueryRobotAppTopicRequest.newBuilder();
        builder.setRobotId(robotId);
        return client.getAvailableRobotTopicList(builder.build());
    }

}
