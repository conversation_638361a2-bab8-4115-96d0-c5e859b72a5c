package com.mioffice.ums.engine.service.grpc.server;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.engine.entity.info.RobotInfo;
import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.mioffice.ums.engine.enums.constants.SingleActionConstants;
import com.mioffice.ums.engine.event.SingleEvent;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.result.ResultCode;
import com.mioffice.ums.engine.utils.KeyCenterUtil;
import com.xiaomi.info.grpc.server.annotation.RpcServer;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.*;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.util.Assert;

import java.util.Collections;


/**
 * RPC飞书机器人接口
 *
 * <AUTHOR>
 */
@Slf4j
@RpcServer
public class LarkRobotRpcService extends LarkRobotGrpc.LarkRobotImplBase {

    private final RobotInfoMapper robotInfoMapper;
    private final SendMqEventBus sendMQEventBus;

    public LarkRobotRpcService(RobotInfoMapper robotInfoMapper, SendMqEventBus sendMQEventBus) {
        this.robotInfoMapper = robotInfoMapper;
        this.sendMQEventBus = sendMQEventBus;
    }

    @Override
    public void addRobot(AddRobotRequest request, StreamObserver<RobotResponse> responseObserver) {
        RobotResponse.Builder responseBuilder = RobotResponse.newBuilder();
        RobotResponse robotResponse;
        try {
            RobotInfo robotInfo = convert(request);
            Assert.hasLength(robotInfo.getAppId(), "appId不能为空");
            Assert.hasLength(robotInfo.getAppSecret(), "appSecret不能为空");
            Assert.hasLength(robotInfo.getAppShortName(), "appShortName不能为空");
            // stop_flag 只能为开启和不开启
            Assert.isTrue(robotInfo.getStopFlag() == RobotStatusEnum.ROBOT_ON.getCode()
                    || robotInfo.getStopFlag() == RobotStatusEnum.ROBOT_OFF.getCode(), "参数stop_flag不符合规范");

            long time = System.currentTimeMillis();
            robotInfo.setCreateTime(time);
            robotInfo.setUpdateTime(time);

            robotInfo.setAppSecret(KeyCenterUtil.encrypt(robotInfo.getAppSecret()));
            try {
                robotInfoMapper.insert(robotInfo);
            } catch (DuplicateKeyException e) {
                robotInfo.setCreateTime(null);
                robotInfoMapper.insertOrUpdateSelective(robotInfo);
            }


            robotResponse = responseBuilder.setCode(ResultCode.OK.getCode()).setDesc(ResultCode.OK.getMessage()).build();

            // 发送广播信令
            sendMQEventBus.postEvent(new SingleEvent(SingleActionConstants.SYNC_ROBOT_ACTION, Collections.singletonList(robotInfo.getAppId())));

        } catch (IllegalArgumentException e1) {
            log.error("请求参数为空, 原因:{}", e1.getMessage());
            robotResponse = responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode()).setDesc(e1.getMessage()).build();
        } catch (DataProtectionException e2) {
            log.error("appId为[{}]的机器人secret加密失败", request.getAppId());
            robotResponse = responseBuilder.setCode(ResultCode.KEY_CENTER_SECRET_ERROR.getCode()).setDesc(e2.getMessage()).build();
        } catch (Exception e3) {
            log.error("添加机器人报错, 原因:{}", e3.getMessage());
            robotResponse = responseBuilder.setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setDesc(e3.getMessage()).build();
        }
        responseObserver.onNext(robotResponse);
        responseObserver.onCompleted();
    }

    @Override
    public void alterRobot(AlterRobotRequest request, StreamObserver<RobotResponse> responseObserver) {
        RobotResponse.Builder responseBuilder = RobotResponse.newBuilder();
        RobotResponse robotResponse;
        try {
            Assert.hasLength(request.getAppId(), "appId不能为空");
            int stopFlag = request.getStopFlag();
            // stop_flag 只能为开启和不开启
            Assert.isTrue(stopFlag == RobotStatusEnum.ROBOT_ON.getCode()
                    || stopFlag == RobotStatusEnum.ROBOT_OFF.getCode(), "参数stop_flag不符合规范");

            long time = System.currentTimeMillis();
            RobotInfo robotInfo = new RobotInfo();
            robotInfo.setAppId(request.getAppId());
            robotInfo.setStopFlag((byte) stopFlag);
            robotInfo.setUpdateTime(time);

            robotInfoMapper.update(robotInfo,
                    Wrappers.<RobotInfo>lambdaUpdate()
                            .eq(RobotInfo::getAppId, robotInfo.getAppId())
            );
            robotResponse = responseBuilder.setCode(ResultCode.OK.getCode()).setDesc(ResultCode.OK.getMessage()).build();

            // 发送广播信令
            sendMQEventBus.postEvent(new SingleEvent(SingleActionConstants.SYNC_ROBOT_ACTION, Collections.singletonList(robotInfo.getAppId())));
        } catch (IllegalArgumentException e1) {
            log.error("请求参数为空, 原因:{}", e1.getMessage());
            robotResponse = responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode()).setDesc(e1.getMessage()).build();
        } catch (Exception e2) {
            log.error("更新机器人报错, 原因:{}", e2.getMessage());
            robotResponse = responseBuilder.setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setDesc(e2.getMessage()).build();
        }
        responseObserver.onNext(robotResponse);
        responseObserver.onCompleted();
    }

    private RobotInfo convert(AddRobotRequest request) {
        RobotInfo robotInfo = new RobotInfo();
        robotInfo.setAppId(request.getAppId());
        robotInfo.setAppSecret(request.getAppSecret());
        robotInfo.setAppShortName(request.getAppShortName());
        robotInfo.setStopFlag((byte) request.getStopFlag());
        return robotInfo;
    }
}
