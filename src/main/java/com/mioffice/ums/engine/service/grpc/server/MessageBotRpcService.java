package com.mioffice.ums.engine.service.grpc.server;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.engine.entity.info.EmailRobotInfo;
import com.mioffice.ums.engine.entity.info.RobotInfo;
import com.mioffice.ums.engine.entity.info.SmsRobotInfo;
import com.mioffice.ums.engine.enums.BotTypeEnum;
import com.mioffice.ums.engine.enums.EmailRobotTypeEnum;
import com.mioffice.ums.engine.enums.RobotStatusEnum;
import com.mioffice.ums.engine.enums.constants.SingleActionConstants;
import com.mioffice.ums.engine.event.SingleEvent;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import com.mioffice.ums.engine.result.ResultCode;
import com.mioffice.ums.engine.utils.KeyCenterUtil;
import com.xiaomi.info.grpc.server.annotation.RpcServer;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.*;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.util.Assert;

import java.util.Collections;


/**
 * RPC飞书机器人接口
 *
 * <AUTHOR>
 */
@Slf4j
@RpcServer
public class MessageBotRpcService extends MessageBotGrpc.MessageBotImplBase {

    private final RobotInfoMapper robotInfoMapper;
    private final SendMqEventBus sendMQEventBus;
    private final EmailRobotInfoMapper emailRobotInfoMapper;
    private final SmsRobotInfoMapper smsRobotInfoMapper;

    @Value("${email.guard.inner-host}")
    private String mailHost;

    public MessageBotRpcService(RobotInfoMapper robotInfoMapper, SendMqEventBus sendMQEventBus, EmailRobotInfoMapper emailRobotInfoMapper, SmsRobotInfoMapper smsRobotInfoMapper) {
        this.robotInfoMapper = robotInfoMapper;
        this.sendMQEventBus = sendMQEventBus;
        this.emailRobotInfoMapper = emailRobotInfoMapper;
        this.smsRobotInfoMapper = smsRobotInfoMapper;
    }

    @Override
    public void addBot(AddBotRequest request, StreamObserver<BotResponse> responseObserver) {
        BotResponse.Builder responseBuilder = BotResponse.newBuilder();
        BotResponse botResponse;
        try {
            // stop_flag 只能为开启和不开启
            Assert.isTrue(request.getStopFlag() == RobotStatusEnum.ROBOT_ON.getCode()
                    || request.getStopFlag() == RobotStatusEnum.ROBOT_OFF.getCode(), "参数stop_flag不符合规范");
            long time = System.currentTimeMillis();
            if (request.getBotType() == BotTypeEnum.BOT_TYPE_EMAIL.getCode()){
                EmailRobotInfo emailRobotInfo = (EmailRobotInfo) convert(request);
                Assert.hasLength(emailRobotInfo.getSender(), "sender不能为空");
                Assert.hasLength(emailRobotInfo.getToken(), "token不能为空");
                Assert.hasLength(emailRobotInfo.getSenderName(), "senderName不能为空");

                emailRobotInfo.setToken(KeyCenterUtil.encrypt(emailRobotInfo.getToken()));
                emailRobotInfo.setCreateTime(time);
                emailRobotInfo.setUpdateTime(time);

                try {
                    emailRobotInfoMapper.insert(emailRobotInfo);
                } catch (DuplicateKeyException e) {
                    emailRobotInfo.setCreateTime(null);
                    emailRobotInfoMapper.insertOrUpdateSelective(emailRobotInfo);
                }

            }else if (request.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()){
                RobotInfo robotInfo = (RobotInfo) convert(request);
                Assert.hasLength(robotInfo.getAppId(), "appId不能为空");
                Assert.hasLength(robotInfo.getAppSecret(), "appSecret不能为空");
                Assert.hasLength(robotInfo.getAppShortName(), "appShortName不能为空");

                robotInfo.setCreateTime(time);
                robotInfo.setUpdateTime(time);

                robotInfo.setAppSecret(KeyCenterUtil.encrypt(robotInfo.getAppSecret()));

                try {
                    robotInfoMapper.insert(robotInfo);
                } catch (DuplicateKeyException e) {
                    robotInfo.setCreateTime(null);
                    robotInfoMapper.insertOrUpdateSelective(robotInfo);
                }

            }else if (request.getBotType() == BotTypeEnum.BOT_TYPE_SMS.getCode()){
                SmsRobotInfo smsRobotInfo = (SmsRobotInfo) convert(request);
                Assert.hasLength(smsRobotInfo.getSign(), "sign不能为空");
                Assert.hasLength(smsRobotInfo.getContentTypeKey(), "模板号不能为空");

                smsRobotInfo.setCreateTime(time);
                smsRobotInfo.setUpdateTime(time);

                try {
                    smsRobotInfoMapper.insert(smsRobotInfo);
                } catch (DuplicateKeyException e) {
                    smsRobotInfo.setCreateTime(null);
                    smsRobotInfoMapper.insertOrUpdateSelective(smsRobotInfo);
                }
            }

            botResponse = responseBuilder.setCode(ResultCode.OK.getCode()).setDesc(ResultCode.OK.getMessage()).build();
            // 发送广播信令
            sendMQEventBus.postEvent(new SingleEvent(SingleActionConstants.SYNC_ROBOT_ACTION, Collections.singletonList(request.getAppId())));
        } catch (IllegalArgumentException e1) {
            log.error("请求参数为空, 原因:{}", e1.getMessage());
            botResponse = responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode()).setDesc(e1.getMessage()).build();
        } catch (DataProtectionException e2) {
            log.error("appId为[{}]的机器人secret加密失败", request.getAppId());
            botResponse = responseBuilder.setCode(ResultCode.KEY_CENTER_SECRET_ERROR.getCode()).setDesc(e2.getMessage()).build();
        } catch (Exception e3) {
            log.error("添加机器人报错, 原因:{}", e3.getMessage());
            botResponse = responseBuilder.setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setDesc(e3.getMessage()).build();
        }
        responseObserver.onNext(botResponse);
        responseObserver.onCompleted();
    }

    @Override
    public void alterBot(AlterBotRequest request, StreamObserver<BotResponse> responseObserver) {
        BotResponse.Builder responseBuilder = BotResponse.newBuilder();
        BotResponse botResponse;
        try {
            Assert.hasLength(request.getAppId(), "appId不能为空");
            // stop_flag 只能为开启和不开启
            Assert.isTrue(request.getStopFlag() == RobotStatusEnum.ROBOT_ON.getCode()
                    || request.getStopFlag() == RobotStatusEnum.ROBOT_OFF.getCode(), "参数stop_flag不符合规范");

            long time = System.currentTimeMillis();
            String appId  = request.getAppId();
            String appSecret = request.getAppSecret();
            int stopFlag = request.getStopFlag();

            if (request.getBotType() == BotTypeEnum.BOT_TYPE_EMAIL.getCode()){

                Assert.hasLength(appSecret, "appSecret不能为空");

                EmailRobotInfo emailRobotInfo = new EmailRobotInfo();
                emailRobotInfo.setStopFlag((byte) stopFlag);
                emailRobotInfo.setUpdateTime(time);
                emailRobotInfo.setToken(KeyCenterUtil.encrypt(appSecret));

                emailRobotInfoMapper.update(emailRobotInfo,
                        Wrappers.<EmailRobotInfo>lambdaUpdate()
                                .eq(EmailRobotInfo::getSender, appId)
                );
            }else if (request.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()){

                Assert.hasLength(appSecret, "appSecret不能为空");

                RobotInfo robotInfo = new RobotInfo();
                robotInfo.setUpdateTime(time);
                robotInfo.setStopFlag((byte) stopFlag);
                robotInfo.setAppSecret(KeyCenterUtil.encrypt(appSecret));

                robotInfoMapper.update(robotInfo,
                        Wrappers.<RobotInfo>lambdaUpdate()
                                .eq(RobotInfo::getAppId, appId)
                );

            }else if (request.getBotType() == BotTypeEnum.BOT_TYPE_SMS.getCode()){

                SmsRobotInfo smsRobotInfo =  new SmsRobotInfo();
                smsRobotInfo.setStopFlag((byte) stopFlag);
                smsRobotInfo.setUpdateTime(time);

                smsRobotInfoMapper.update(smsRobotInfo,
                        Wrappers.<SmsRobotInfo>lambdaUpdate()
                                .eq(SmsRobotInfo::getContentTypeKey, appId)
                );
            }

            botResponse = responseBuilder.setCode(ResultCode.OK.getCode()).setDesc(ResultCode.OK.getMessage()).build();

            // 发送广播信令
            sendMQEventBus.postEvent(new SingleEvent(SingleActionConstants.SYNC_ROBOT_ACTION, Collections.singletonList(request.getAppId())));
        } catch (IllegalArgumentException e1) {
            log.error("请求参数为空, 原因:{}", e1.getMessage());
            botResponse = responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode()).setDesc(e1.getMessage()).build();
        } catch (Exception e2) {
            log.error("更新机器人报错, 原因:{}", e2.getMessage());
            botResponse = responseBuilder.setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setDesc(e2.getMessage()).build();
        }
        responseObserver.onNext(botResponse);
        responseObserver.onCompleted();
    }

    private  <T> T convert(AddBotRequest request) {
        if (request.getBotType() == BotTypeEnum.BOT_TYPE_EMAIL.getCode()){
            EmailRobotInfo emailRobotInfo = new EmailRobotInfo();
            emailRobotInfo.setSender(request.getAppId());
            emailRobotInfo.setToken(request.getAppSecret());
            emailRobotInfo.setMailHost(mailHost);
            emailRobotInfo.setSenderName(request.getAppShortName());
            emailRobotInfo.setType(EmailRobotTypeEnum.INNER.getType());
            emailRobotInfo.setStopFlag((byte) request.getStopFlag());
            return (T) emailRobotInfo;
        }else if (request.getBotType() == BotTypeEnum.BOT_TYPE_LARK.getCode()){
            RobotInfo robotInfo = new RobotInfo();
            robotInfo.setAppId(request.getAppId());
            robotInfo.setAppSecret(request.getAppSecret());
            robotInfo.setAppShortName(request.getAppShortName());
            robotInfo.setStopFlag((byte) request.getStopFlag());
            return (T) robotInfo;
        }else if (request.getBotType() == BotTypeEnum.BOT_TYPE_SMS.getCode()){
            SmsRobotInfo smsRobot = new SmsRobotInfo();
            smsRobot.setSign(request.getAppShortName());
            smsRobot.setStopFlag((byte) request.getStopFlag());
            smsRobot.setContentTypeKey(request.getAppId());
            return (T) smsRobot;
        }
        return (T) new Object();
    }
}
