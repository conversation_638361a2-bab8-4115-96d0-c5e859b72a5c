package com.mioffice.ums.engine.service.grpc.server;

import com.mioffice.ums.engine.config.MqProperties;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.larksuite.appframework.sdk.client.LarkClient;
import com.larksuite.appframework.sdk.core.protocol.common.Group;
import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.control.DevUserSendControl;
import com.mioffice.ums.engine.entity.bo.AvgCostTimeBO;
import com.mioffice.ums.engine.entity.bo.CardActionBO;
import com.mioffice.ums.engine.entity.bo.CountAndCostTimeBO;
import com.mioffice.ums.engine.entity.bo.ErrorTypeBo;
import com.mioffice.ums.engine.entity.bo.ErrorTypeListBo;
import com.mioffice.ums.engine.entity.bo.ExtraIdCostTimeBO;
import com.mioffice.ums.engine.entity.bo.ExtraIdCountBO;
import com.mioffice.ums.engine.entity.bo.GroupInfosBO;
import com.mioffice.ums.engine.entity.bo.MessageGroupDeptRecordBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageFilterInfo;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.ErrorTypeEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.enums.ReadStatusEnum;
import static com.mioffice.ums.engine.enums.constants.RedisConstants.TEMPLATE_ID_FIELD;
import com.mioffice.ums.engine.enums.SendSaasEnum;
import com.mioffice.ums.engine.enums.constants.SingleActionConstants;
import com.mioffice.ums.engine.enums.constants.UmsSystemParams;
import com.mioffice.ums.engine.event.SingleEvent;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import com.mioffice.ums.engine.manager.EnhanceMiWorkRobotManager;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageFilterInfoMapper;
import com.mioffice.ums.engine.mapper.MessageTemplateInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.result.ResultCode;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumer;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumerManager;
import com.mioffice.ums.engine.service.MessageService;
import com.mioffice.ums.engine.utils.AesUtils;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.mioffice.ums.engine.utils.MapperUtil;
import com.mioffice.ums.engine.utils.MessageUtil;
import com.xiaomi.info.grpc.server.annotation.RpcServer;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.AverageCostTimeByDate;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CardAction;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ChannelAndCostTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ChannelAndCostTimeByDate;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CommonResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CostTimeByDateAndExtraIdRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.CostTimeByDateAndExtraIdResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ErrorType;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ExtraIdRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ExtraIdUsernameRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.FinalContent;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.FinalContentResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupInfo;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupInfosRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupInfosResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupListRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupListResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.GroupRecord;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ImageKeyResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ImageUrlRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAverageCostTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAverageCostTimeRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAverageCostTimeResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAvgCostTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAvgCostTimeRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAvgCostTimeResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageExtraIdPageRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageExtraIdRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageGroupDeptResultPage;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageNumberAndTimeResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageRead;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageReadRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageReadResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageResultPage;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageResultSingleForCustomResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageResultSingleForDeptResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageScopeAndAverageCostTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageServiceGrpc;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateId;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageTemplateResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUnReadDetail;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUnReadDetailResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUnreadDetailRequest;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResponse;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResult;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.UsernameResponse;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 发送消息和创建消息模板接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020.08.11
 */

@Slf4j
@RpcServer
public class MessageRpcService extends MessageServiceGrpc.MessageServiceImplBase {

    private static final String SUCCESS_STR = "success";

    private final MessageTemplateInfoMapper messageTemplateInfoMapper;
    private final MessageUserInfoMapper messageUserInfoMapper;

    private final MessageFilterInfoMapper messageFilterInfoMapper;

    private final SendMqEventBus sendMqEventBus;

    private final MiWorkRobotManager miWorkRobotManager;

    @Autowired(required = false)
    private DevUserSendControl devUserSendControl;

    @Autowired
    private EnhanceMiWorkRobotManager enhanceMiWorkRobotManager;

    @Autowired
    private MqProperties mqProperties;

    @NacosValue(value = "${ums.engine.executives:}", autoRefreshed = true)
    private String executives;

    @Autowired
    private TemplateCache templateCache;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private MessageService messageService;

    @Autowired
    private ProducerAndConsumerManager producerAndConsumerManager;

    public MessageRpcService(MessageTemplateInfoMapper messageTemplateInfoMapper,
                             MessageUserInfoMapper messageUserInfoMapper,
                             MessageFilterInfoMapper messageFilterInfoMapper,
                             SendMqEventBus sendMqEventBus,
                             MiWorkRobotManager miWorkRobotManager) {
        this.messageTemplateInfoMapper = messageTemplateInfoMapper;
        this.messageUserInfoMapper = messageUserInfoMapper;
        this.messageFilterInfoMapper = messageFilterInfoMapper;
        this.sendMqEventBus = sendMqEventBus;
        this.miWorkRobotManager = miWorkRobotManager;
    }

    /**
     * 创建消息模板
     *
     * @param request 创建消息模板请求
     * @param responseObserver 创建消息模板返回结构体
     */
    @Override
    public void createMessageTemplate(MessageTemplateRequest request,
                                      StreamObserver<MessageTemplateResponse> responseObserver) {

        MessageTemplateResponse.Builder responseBuilder = MessageTemplateResponse.newBuilder();

        try {
            Assert.isTrue(MessageChannelEnum.get((byte) request.getChannel()) != MessageChannelEnum.NO, "消息渠道错误");
            Assert.hasText(request.getTitleCn(), "titleCn不能为空");
            Assert.hasText(request.getContentCn(), "contentCn不能为空");

            if (MessageChannelEnum.get((byte) request.getChannel()) == MessageChannelEnum.MI_WORK) {
                Assert.hasText(request.getAppId(), "appId不能为空");
            }

            MessageTemplateInfo messageTemplateInfo = processTemplate(request);

            MessageTemplateId.Builder msgIdBuilder = MessageTemplateId
                    .newBuilder()
                    .setMessageTemplateId(messageTemplateInfo.getId());

            responseBuilder.setCode(ResultCode.OK.getCode())
                    .setMessage(ResultCode.OK.getMessage())
                    .setMessageTemplateId(msgIdBuilder);

        } catch (IllegalArgumentException e) {
            log.error("创建模板异常，请求参数非法", e);
            responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode()).setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("创建模板失败", e);
            responseBuilder.setCode(ResultCode.SERVER_INNER_ERROR.getCode()).setMessage(e.getMessage());
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    private String serialCardAction(List<CardAction> actionList) {
        List<CardActionBO> cardActionBOList = actionList.stream()
                .map(p -> new CardActionBO(p.getName(), p.getLandingUrl(), p.getValMap()))
                .collect(Collectors.toList());
        return JsonUtils.toJson(cardActionBOList);
    }

    /**
     * 发送消息
     *
     * @param request 发送消息请求
     * @param responseObserver 发送消息请求返回观察器
     */
    @Override
    public void sendMessage(MessageRequest request, StreamObserver<MessageUserResponse> responseObserver) {

        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();

        long t1 = System.currentTimeMillis();
        try {
            Assert.notNull(request.getMessageTemplateId(), "消息模板不能为空");
            Assert.isTrue(request.getMessageTemplateId().getMessageTemplateId() > 0, "消息模板不能为空");
            Assert.isTrue(!request.getUsersList().isEmpty(), "发送用户列表不能为空");
            Assert.isTrue(request.getUsersList().size() <= 500, "用户列表上限为500条");

            long messageTemplateId = request.getMessageTemplateId().getMessageTemplateId();
            List<MessageUser> usersList = request.getUsersList();

            log.info("usersList = [{}]", new Gson().toJson(usersList));

            String extraId = request.getUsers(0).getExtraId();

            MessageTemplateInfo messageTemplateInfo = templateCache.get(messageTemplateId);
            long t2 = System.currentTimeMillis();

            log.info("extraId {} sendMessage t1 -- t2: {}", extraId, (t2 - t1));

            List<MessageUserInfo> userInfoList =
                    usersList.stream().map(p -> toMessageUserInfo(p, messageTemplateInfo)).collect(Collectors.toList());

            if (Objects.nonNull(devUserSendControl)) {
                devUserSendControl.filterByChannel(userInfoList);
            }

            // 黑名单控制
            // blackUserControl.control(userInfoList);
            long t3 = System.currentTimeMillis();
            log.info("extraId {} sendMessage t2 -- t3: {}", extraId, (t3 - t2));

            responseBuilder.setCode(ResultCode.OK.getCode());
            responseBuilder.setMessage(ResultCode.OK.getMessage());
            if (!userInfoList.isEmpty()) {

                messageUserInfoMapper.batchInsert(userInfoList);
                long t4 = System.currentTimeMillis();
                log.info("extraId {} sendMessage t3 -- t4: {}", extraId, (t4 - t3));

                log.info("userInfoList = [{}]", new Gson().toJson(userInfoList));

                // 组装返回的结果数据
                List<MessageUserResult> results =
                        userInfoList.stream().map(this::packMessageUserResult).collect(Collectors.toList());
                responseBuilder.addAllResults(results);

                // 去掉状态不对的用户
                userInfoList.removeIf(p -> p.getMessageStatus() != MessageStatusEnum.SENDING.getStatus());

                // 路由消息到不同的队列
                routeMessage(userInfoList, responseBuilder);
            }
        } catch (IllegalArgumentException e) {
            log.error("发消息异常，请求参数非法", e);
            responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode()).setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("发消息异常", e);
            responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode()).setMessage(e.getMessage());
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 路由消息到不同的队列
     */
    private void routeMessage(List<MessageUserInfo> userInfoList, MessageUserResponse.Builder responseBuilder) {
        if (CollectionUtils.isEmpty(userInfoList)) {
            return;
        }
        try {
            // 从MessageUserInfo获取sysId，因为MessageUser可能没有sysId字段
            String sysId = userInfoList.get(0).getSysId();
            String robotId = userInfoList.get(0).getAppId();
            
            // 1. 优先检查是否有专属队列（系统级别）
            Optional<ProducerAndConsumer> sysProducerAndConsumer = producerAndConsumerManager.getSysProducerAndConsumer(sysId);
            if (sysProducerAndConsumer.isPresent()) {
                String topicName = sysProducerAndConsumer.get().getTopic();
                boolean success = producerAndConsumerManager.sendToSpecificTopic(topicName, userInfoList);
                if (success) {
                    log.info("消息已发送到系统专属队列, sysId: {}, topic: {}, messageCount: {}", 
                            sysId, topicName, userInfoList.size());
                    return;
                }
            }
            
            // 2. 检查是否有专属队列（机器人级别），一个机器人会被多个系统使用，因此加该逻辑
            Optional<ProducerAndConsumer> robotProducerAndConsumer = producerAndConsumerManager.getRobotProducerAndConsumer(robotId);
            if (robotProducerAndConsumer.isPresent()) {
                String topicName = robotProducerAndConsumer.get().getTopic();
                boolean success = producerAndConsumerManager.sendToSpecificTopic(topicName, userInfoList);
                if (success) {
                    log.info("消息已发送到机器人专属队列, robotId: {}, topic: {}, messageCount: {}", 
                            robotId, topicName, userInfoList.size());
                    return;
                }
            }
            
            // 3. 检查是否应该使用统一Topic
            if (producerAndConsumerManager.shouldUseUnifiedTopic(robotId)) {
                log.debug("机器人ID {} 使用统一Topic发送消息", robotId);
                
                boolean success = producerAndConsumerManager.sendToUnifiedTopic(userInfoList);
                if (success) {
                    log.info("消息已发送到统一Topic, robotId: {}, messageCount: {}", robotId, userInfoList.size());
                    return;
                } else {
                    log.warn("统一Topic发送失败，回退到公共队列, robotId: {}", robotId);
                    // 继续执行公共队列逻辑
                }
            }
            
            // 4. 默认发送到公共队列（Talos）
            log.info("使用公共队列发送消息, sysId: {}, robotId: {}, messageCount: {}", 
                    sysId, robotId, userInfoList.size());
            messageService.asyncSendMsg2EventBus(userInfoList);
            
        } catch (IllegalArgumentException e) {
            log.error("路由消息参数错误", e);
        } catch (Exception e) {
            log.error("路由消息失败", e);
        }
    }

    /**
     * 内部发送消息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void innerSendMessage(MessageRequest request, StreamObserver<MessageUserResponse> responseObserver) {

        Assert.notNull(request.getMessageTemplateId(), "消息模板不能为空");
        Assert.isTrue(request.getMessageTemplateId().getMessageTemplateId() > 0, "消息模板不能为空");
        Assert.isTrue(!request.getUsersList().isEmpty(), "发送用户列表不能为空");
        Assert.isTrue(request.getUsersList().size() <= 500, "用户列表上限为500条");

        long messageTemplateId = request.getMessageTemplateId().getMessageTemplateId();
        List<MessageUser> usersList = request.getUsersList();

        MessageTemplateInfo messageTemplateInfo = messageTemplateInfoMapper.selectById(messageTemplateId);
        List<Future<MiWorkResponseBO>> futureList = usersList.stream()
                .map(p -> toMessageUserInfo(p, messageTemplateInfo))
                .map(p -> sendOneDirectMessage(p.getAppId(), p))
                .collect(Collectors.toList());

        boolean isSuccess = false;
        Exception exception = null;
        for (Future<MiWorkResponseBO> future : futureList) {
            try {
                MiWorkResponseBO miWorkResponseBO = future.get();
                log.info("innerSendMessage 发送成功, messageTemplateId = [{}], msgId = [{}]", messageTemplateId,
                        miWorkResponseBO.getMsgId());
                isSuccess = true;
            } catch (Exception e) {
                exception = e;
            }
        }

        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        if (isSuccess) {
            responseBuilder.setCode(ResultCode.OK.getCode());
            responseBuilder.setMessage(ResultCode.OK.getMessage());
        } else {
            responseBuilder.setCode(ResultCode.PARAM_ERROR.getCode());
            if (Objects.nonNull(exception)) {
                responseBuilder.setMessage(exception.getMessage());
            }

        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    private Future<MiWorkResponseBO> sendOneDirectMessage(String appId, MessageUserInfo messageUserInfo) {
        return CompletableFuture.supplyAsync(() -> {
            MiWorkRobot.Session session = new MiWorkRobot.Session();
            session.setUserId(messageUserInfo.getUsername());
            session.setEmail(messageUserInfo.getEmail());
            try {
                return enhanceMiWorkRobotManager.sendMsg(appId, messageUserInfo, session);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    private MessageUserInfo toMessageUserInfo(MessageUser messageUser, MessageTemplateInfo messageTemplateInfo) {
        MessageUserInfo messageUserInfo = processMessageUserTemplateInfo(messageUser, messageTemplateInfo);
        messageUserInfo.setId(IdWorker.getId());

        // 消息状态
        if (messageUser.getMessageStatue() == 0) {
            messageUserInfo.setMessageStatus(MessageStatusEnum.SENDING.getStatus());
        } else {
            messageUserInfo.setMessageStatus((byte) messageUser.getMessageStatue());
        }

        // 错误log
        if (StringUtils.isNotBlank(messageUser.getErrorLog())) {
            messageUserInfo.setErrorLog(messageUser.getErrorLog());
        }

        messageUserInfo.setTitleCn(messageTemplateInfo.getTitleCn());
        messageUserInfo.setTitleEn(messageTemplateInfo.getTitleEn());
        messageUserInfo.setRetryCount(0);
        messageUserInfo.setMsgTmpId(messageTemplateInfo.getId());
        messageUserInfo.setMessageId(StringUtils.EMPTY);
        messageUserInfo.setErrorType(ErrorTypeEnum.NO_KNOW.getType());
        messageUserInfo.setReadStatus(ReadStatusEnum.NO.getCode());
        messageUserInfo.setIsSendSaas((byte) messageUser.getIsSendSaas());
        return messageUserInfo;
    }

    @Override
    public void getFinalContent(ExtraIdUsernameRequest request, StreamObserver<FinalContentResponse> responseObserver) {
        List<MessageUserInfo> messageUserInfoList = messageUserInfoMapper.selectList(
                Wrappers.<MessageUserInfo>lambdaQuery()
                        .eq(MessageUserInfo::getExtraId, request.getExtraId())
                        .eq(MessageUserInfo::getUsername, request.getUsername())
        );

        List<FinalContent> list = new ArrayList<>();
        for (MessageUserInfo messageUserInfo : messageUserInfoList) {
            FinalContent.Builder finalContentBuilder = FinalContent.newBuilder()
                    .setExtraId(messageUserInfo.getExtraId())
                    .setUsername(messageUserInfo.getUsername());

            if (Objects.nonNull(messageUserInfo.getFinalContent())) {
                finalContentBuilder.setFinalContent(messageUserInfo.getFinalContent());
            }

            list.add(finalContentBuilder.build());
        }

        FinalContentResponse.Builder responseBuilder = FinalContentResponse.newBuilder();
        responseBuilder.addAllFinalContent(list);

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    /**
     * 中断消息
     *
     * @param request 中断消息请求
     * @param responseObserver 中断消息返回观察者
     */
    @Override
    public void interruptMessageByExtraId(ExtraIdRequest request,
                                          StreamObserver<CommonResponse> responseObserver) {

        String extraId = request.getExtraId();

        interruptMessage(extraId);

        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        responseBuilder.setCode(ResultCode.OK.getCode());
        responseBuilder.setMessage(ResultCode.OK.getMessage());

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    private void interruptMessage(String extraId) {
        MessageFilterInfo messageFilterInfo = new MessageFilterInfo();
        messageFilterInfo.setCreateTime(System.currentTimeMillis());
        messageFilterInfo.setUpdateTime(System.currentTimeMillis());
        messageFilterInfo.setExtraId(extraId);

        messageFilterInfoMapper.insertOrUpdateSelective(messageFilterInfo);

        MessageUserInfo updateMessageUserInfo = MessageUserInfo.newUpdateTimeInstant();
        updateMessageUserInfo.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
        // 中断发送中和发送失败的消息
        messageUserInfoMapper.update(
                updateMessageUserInfo,
                Wrappers.<MessageUserInfo>lambdaUpdate()
                        .eq(MessageUserInfo::getExtraId, extraId)
                        .in(MessageUserInfo::getMessageStatus, MessageStatusEnum.SENDING.getStatus(),
                                MessageStatusEnum.SEND_FAIL.getStatus())
        );

        SingleEvent singleEvent = new SingleEvent(SingleActionConstants.SYNC_MSG_FILTER_ACTION, null);
        sendMqEventBus.postEvent(singleEvent);
    }

    @Override
    public void retractMessageByExtraId(ExtraIdRequest request,
                                        StreamObserver<CommonResponse> responseObserver) {
        String extraId = request.getExtraId();

        interruptMessage(extraId);

        messageService.asyncRetractMessage(extraId);

        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        responseBuilder.setCode(ResultCode.OK.getCode());
        responseBuilder.setMessage(ResultCode.OK.getMessage());

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void uploadLarkImage(ImageUrlRequest request, StreamObserver<ImageKeyResponse> responseObserver) {

        String url = request.getUrl();
        String appId = request.getAppId();

        ImageKeyResponse.Builder responseBuilder = ImageKeyResponse.newBuilder();
        try {
            String imageKey = miWorkRobotManager.uploadImageForLark(appId, url);
            responseBuilder.setCode(ResultCode.OK.getCode());
            responseBuilder.setMessage(ResultCode.OK.getMessage());
            responseBuilder.setKey(imageKey);
        } catch (Exception e) {
            responseBuilder.setCode(ResultCode.SERVER_INNER_ERROR.getCode());
            responseBuilder.setMessage(e.getMessage());
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();

    }



    private MessageTemplateInfo processTemplate(MessageTemplateRequest request)
            throws Exception {
        MessageTemplateInfo messageTemplateInfo = MessageTemplateInfo.newCreateAndUpdateTimeInstant();

        messageTemplateInfo.setChannel((byte) request.getChannel());
        messageTemplateInfo.setAppId(request.getAppId());

        messageTemplateInfo.setTitleCn(request.getTitleCn());
        messageTemplateInfo.setTitleEn(request.getTitleEn());

        messageTemplateInfo.setContentCn(MessageUtil.imageAddLine(request.getContentCn()));
        messageTemplateInfo.setContentEn(MessageUtil.imageAddLine(request.getContentEn()));
        messageTemplateInfo.setSystemId(request.getSystemId());
        messageTemplateInfo.setMsgFormatType((byte) request.getMsgFormatType());
        messageTemplateInfo.setImageMode(request.getImageMode());
        messageTemplateInfo.setTheme(request.getTheme());

        MessageChannelEnum messageChannelEnum = MessageChannelEnum.get(messageTemplateInfo.getChannel());
        if (messageChannelEnum == MessageChannelEnum.MI_WORK) {

            // 按钮序列化
            if (!request.getCardActionsList().isEmpty()) {
                messageTemplateInfo.setCardActions(serialCardAction(request.getCardActionsList()));
            }
            if (!request.getCardActionsCnList().isEmpty()) {
                messageTemplateInfo.setCardActionsCn(serialCardAction(request.getCardActionsCnList()));
            }
            if (!request.getCardActionsEnList().isEmpty()) {
                messageTemplateInfo.setCardActionsEn(serialCardAction(request.getCardActionsEnList()));
            }
        }

        Set<String> imageUrlList = new HashSet<>(MessageUtil.parseMdForImageUrl(request.getContentCn()));
        if (StringUtils.isNotBlank(request.getContentEn())) {
            // 英文
            imageUrlList.addAll(MessageUtil.parseMdForImageUrl(request.getContentEn()));
        }
        if (!imageUrlList.isEmpty()) {
            messageService.asyncUploadImage(imageUrlList, messageTemplateInfo, request.getAppId());
            if (request.getIsSendSaas() == SendSaasEnum.YES.getCode()) {
                messageService.asyncUploadSaasImage(imageUrlList, messageTemplateInfo, request.getAppId());
            }
        }

        messageTemplateInfoMapper.insert(messageTemplateInfo);

        redissonClient.getBucket(String.format("%s:%d", TEMPLATE_ID_FIELD, messageTemplateInfo.getId()))
                .set(JsonUtils.toJson(messageTemplateInfo), Duration.ofHours(1));

        return messageTemplateInfo;
    }

    private MessageUserInfo processMessageUserTemplateInfo(MessageUser messageUser,
                                                           MessageTemplateInfo messageTemplateInfo) {

        Assert.hasText(messageUser.getExtraId(), "扩展id不能为空");
        Assert.hasText(messageUser.getAppId(), "appId不能为空");
        if (messageTemplateInfo.getChannel() == MessageChannelEnum.MI_WORK.getType()) {
            Assert.isTrue(StringUtils.isNotBlank(messageUser.getUsername()) ||
                    StringUtils.isNotBlank(messageUser.getEmail()) ||
                    StringUtils.isNotBlank(messageUser.getChatId()), "小米办公用户id不能为空");
        } else if (messageTemplateInfo.getChannel() == MessageChannelEnum.EMAIL.getType()) {
            Assert.hasText(messageUser.getEmail(), "邮箱不能为空");
        } else if (messageTemplateInfo.getChannel() == MessageChannelEnum.SMS.getType()) {
            Assert.hasText(messageUser.getPhone(), "手机号不能为空");
        }

        MessageUserInfo messageUserInfo = MapperUtil.INSTANCE.mapToMessageUserInfo(messageUser);
        messageUserInfo.setCreateTime(System.currentTimeMillis());
        messageUserInfo.setUpdateTime(System.currentTimeMillis());
        messageUserInfo.setMsgTmpId(messageTemplateInfo.getId());
        messageUserInfo.setChannel(messageTemplateInfo.getChannel());

        // 手机号加密处理
        encryptPhone(messageUserInfo);

        // placeholderContent 添加固有变量
        Map<String, Object> sysParams = new HashMap<>();
        sysParams.put(UmsSystemParams.UMS_GROUP_ID_NAME, messageUser.getExtraId());
        addSystemParams(messageUserInfo, sysParams);

        return messageUserInfo;
    }

    private void encryptPhone(MessageUserInfo messageUserInfo) {
        if (StringUtils.isNotBlank(messageUserInfo.getPhone())) {
            try {
                messageUserInfo.setPhone(AesUtils.encrypt(messageUserInfo.getPhone()));
            } catch (Exception e) {
                log.error("手机号加密失败 username = [{}], phone = [{}]", messageUserInfo.getUsername(),
                        messageUserInfo.getPhone(), e);
                messageUserInfo.setPhone("");
            }
        }
    }

    /**
     * 添加系统内置变量
     *
     * @param messageUserInfo
     * @param sysParams
     */
    private void addSystemParams(MessageUserInfo messageUserInfo, Map<String, Object> sysParams) {
        Map<String, Object> params;
        if (StringUtils.isNotBlank(messageUserInfo.getPlaceholderContent()) &&
                !StringUtils.equals("null", messageUserInfo.getPlaceholderContent())) {
            params = JsonUtils.toMap(messageUserInfo.getPlaceholderContent());
            params.putAll(sysParams);
        } else {
            params = sysParams;
        }
        messageUserInfo.setPlaceholderContent(JsonUtils.toJson(params));
    }

    private MessageUserResult packMessageUserResult(MessageUserInfo messageUserInfo) {
        MessageUserResult.Builder builder = MessageUserResult.newBuilder();

        builder.setAppId(messageUserInfo.getAppId());
        builder.setOpenId(messageUserInfo.getOpenId());
        builder.setEmail(messageUserInfo.getEmail());
        builder.setUsername(messageUserInfo.getUsername());
        builder.setPhone(messageUserInfo.getPhone());
        builder.setExtraId(messageUserInfo.getExtraId());
        builder.setMsgId(messageUserInfo.getId());

        return builder.build();
    }

    @Override
    public void getMessageNumberAndTimeByExtraIdBatch(MessageExtraIdRequest request,
                                                      StreamObserver<MessageNumberAndTimeResponse> responseObserver) {
        MessageNumberAndTimeResponse.Builder messageNumberAndTimeResponse = MessageNumberAndTimeResponse.newBuilder();
        List<String> extraIdListList = request.getExtraIdListList();
        if (extraIdListList.isEmpty()) {
            responseObserver.onNext(messageNumberAndTimeResponse.build());
            responseObserver.onCompleted();
        }

        List<MessageNumberAndTime> messageNumberAndTimeList = new ArrayList<>();
        CompletableFuture<Map<String, ExtraIdCountBO>> futureExtraIdCountMap = CompletableFuture.supplyAsync(() ->
                messageUserInfoMapper.selectCountByExtraIdList(extraIdListList)
                        .stream()
                        .collect(Collectors.toMap(ExtraIdCountBO::getExtraId, Function.identity(), (v1, v2) -> v2))
        );
        List<Byte> typeList =
                Stream.of(ErrorTypeEnum.values()).map(ErrorTypeEnum::getType).collect(Collectors.toList());
        CompletableFuture<Map<String, ErrorTypeListBo>> futureExtraIdErrMap = CompletableFuture.supplyAsync(() ->
                messageUserInfoMapper.selectErrCountByExtraIdList(extraIdListList, typeList)
                        .stream()
                        .collect(Collectors.toMap(ErrorTypeListBo::getExtraId, Function.identity(), (v1, v2) -> v2))
        );
        CompletableFuture<Map<String, ExtraIdCostTimeBO>> futureExtraIdCostTimeMap = CompletableFuture.supplyAsync(() ->
                messageUserInfoMapper.selectCostTimeByExtraIdList(extraIdListList)
                        .stream()
                        .collect(Collectors.toMap(ExtraIdCostTimeBO::getExtraId, Function.identity(), (v1, v2) -> v2))
        );

        try {
            Map<String, ExtraIdCountBO> extraIdCountMap = futureExtraIdCountMap.get();
            Map<String, ErrorTypeListBo> extraIdErrMap = futureExtraIdErrMap.get();
            Map<String, ExtraIdCostTimeBO> extraIdCostTimeMap = futureExtraIdCostTimeMap.get();

            for (String extraId : extraIdListList) {
                MessageNumberAndTime.Builder messageNumberAndTime = MessageNumberAndTime.newBuilder();
                messageNumberAndTime.setExtraId(extraId);

                if (extraIdCountMap.containsKey(extraId)) {
                    ExtraIdCountBO extraIdCountBO = extraIdCountMap.get(extraId);
                    messageNumberAndTime.setAllCount(extraIdCountBO.getTotalCount());
                    messageNumberAndTime.setTodoCount(extraIdCountBO.getTodoCount());
                    messageNumberAndTime.setPushCount(extraIdCountBO.getPushCount());
                    messageNumberAndTime.setFailCount(extraIdCountBO.getFailCount());
                    messageNumberAndTime.setInterruptCount(extraIdCountBO.getInterruptCount());
                    messageNumberAndTime.setRetractCount(extraIdCountBO.getRetractCount());
                }

                if (extraIdCostTimeMap.containsKey(extraId)) {
                    ExtraIdCostTimeBO extraIdCostTimeBO = extraIdCostTimeMap.get(extraId);
                    messageNumberAndTime.setCostTime(extraIdCostTimeBO.getCostTime());
                    messageNumberAndTime.setReadCount(extraIdCostTimeBO.getReadCount());
                }

                if (extraIdErrMap.containsKey(extraId)) {
                    ErrorTypeListBo errorTypeListBo = extraIdErrMap.get(extraId);
                    List<ErrorTypeBo> errorTypeBos = errorTypeListBo.getList();
                    List<ErrorType> errorTypes =
                            errorTypeBos.stream().map(MapperUtil.INSTANCE::mapToErrorType).collect(Collectors.toList());
                    messageNumberAndTime.addAllErrorType(errorTypes);
                }

                messageNumberAndTimeList.add(messageNumberAndTime.build());
            }

            messageNumberAndTimeResponse.setCode(200);
            messageNumberAndTimeResponse.setMessage(SUCCESS_STR);
            messageNumberAndTimeResponse.addAllMessageNumberAndTimeList(messageNumberAndTimeList);
        } catch (Exception e) {
            messageNumberAndTimeResponse.setCode(500);
            messageNumberAndTimeResponse.setMessage(e.getMessage());
        }
        responseObserver.onNext(messageNumberAndTimeResponse.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMessagePageByExtraId(MessageExtraIdPageRequest request,
                                        StreamObserver<MessageResultSingleForCustomResponse> responseObserver) {
        String extraId = request.getExtraId();
        // page
        Page<MessageUserInfo> dataPage = new Page<>(request.getPage(), request.getSize());
        IPage<MessageUserInfo> messageUserInfoPage = messageUserInfoMapper.selectPage(
                dataPage,
                Wrappers.<MessageUserInfo>lambdaQuery()
                        .and(wrapper -> wrapper.in(MessageUserInfo::getExtraId, extraId))
        );

        MessageResultPage.Builder messageResultPageBuilder = MessageResultPage.newBuilder();
        messageResultPageBuilder.setCurrentPage(messageUserInfoPage.getCurrent());
        messageResultPageBuilder.setTotal(messageUserInfoPage.getTotal());
        messageResultPageBuilder.setPages(messageUserInfoPage.getPages());
        messageResultPageBuilder.setSize((int) messageUserInfoPage.getSize());
        messageResultPageBuilder.setCurrentPage(messageUserInfoPage.getCurrent());
        List<MessageUserInfo> records = messageUserInfoPage.getRecords();
        messageResultPageBuilder.addAllRecords(
                records.stream().map(MapperUtil.INSTANCE::mapToMessageRecord).collect(Collectors.toList()));

        MessageResultSingleForCustomResponse.Builder messageResultSingleForCustomResponseBuilder =
                MessageResultSingleForCustomResponse.newBuilder();

        messageResultSingleForCustomResponseBuilder.setCode(200);
        messageResultSingleForCustomResponseBuilder.setMessage(SUCCESS_STR);
        messageResultSingleForCustomResponseBuilder.setMessageResultPage(messageResultPageBuilder);
        responseObserver.onNext(messageResultSingleForCustomResponseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMessageGroupDeptByExtraId(MessageExtraIdPageRequest request,
                                             StreamObserver<MessageResultSingleForDeptResponse> responseObserver) {
        String extraId = request.getExtraId();
        List<String> deptLevel2IdsList = new ArrayList<>(request.getDeptLevel2IdsList());
        List<String> deptLevel3IdsList = new ArrayList<>(request.getDeptLevel3IdsList());
        List<String> deptLevel4IdsList = new ArrayList<>(request.getDeptLevel4IdsList());
        deptLevel2IdsList.removeIf(item -> item == null || "".equals(item));
        deptLevel3IdsList.removeIf(item -> item == null || "".equals(item));
        deptLevel4IdsList.removeIf(item -> item == null || "".equals(item));

        // page
        Page<MessageGroupDeptRecordBO> dataPage = new Page<>(request.getPage(), request.getSize());
        List<MessageGroupDeptRecordBO> messageGroupDeptRecordBOList =
                messageUserInfoMapper.selectByGroupPage(dataPage, extraId, deptLevel2IdsList, deptLevel3IdsList,
                        deptLevel4IdsList);

        dataPage.setRecords(messageGroupDeptRecordBOList);
        MessageGroupDeptResultPage.Builder messageGroupDeptResultPageBuilder = MessageGroupDeptResultPage.newBuilder();
        messageGroupDeptResultPageBuilder.addAllRecords(
                messageGroupDeptRecordBOList.stream().map(MapperUtil.INSTANCE::mapToMessageGroupDeptResultRecord)
                        .collect(Collectors.toList()));
        messageGroupDeptResultPageBuilder.setCurrentPage(dataPage.getCurrent());
        messageGroupDeptResultPageBuilder.setTotal(dataPage.getTotal());
        messageGroupDeptResultPageBuilder.setPages(dataPage.getPages());
        messageGroupDeptResultPageBuilder.setSize((int) dataPage.getSize());

        MessageResultSingleForDeptResponse.Builder messageResultSingleForDeptResponseBuilder =
                MessageResultSingleForDeptResponse.newBuilder();
        messageResultSingleForDeptResponseBuilder.setCode(200);
        messageResultSingleForDeptResponseBuilder.setMessage(SUCCESS_STR);
        messageResultSingleForDeptResponseBuilder.setMessageGroupDeptResultPage(messageGroupDeptResultPageBuilder);
        responseObserver.onNext(messageResultSingleForDeptResponseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAverageCostTimeByDateAndExtraIdBatch(CostTimeByDateAndExtraIdRequest request,
                                                        StreamObserver<CostTimeByDateAndExtraIdResponse> responseObserver) {
        List<AverageCostTimeByDate> averageCostTimeByDateList = request.getAverageCostTimeByDateList();
        Map<String, List<AverageCostTimeByDate>> dateAndAverageCostTimeByDateMap =
                averageCostTimeByDateList.stream().collect(Collectors.groupingBy(AverageCostTimeByDate::getDate));
        List<String> extraIdList = new ArrayList<>();
        averageCostTimeByDateList.forEach(p -> extraIdList.addAll(p.getExtraIdListList()));
        CostTimeByDateAndExtraIdResponse.Builder builder = CostTimeByDateAndExtraIdResponse.newBuilder();
        log.info("查询成功或者打断的任务开始时间：[{}]", System.currentTimeMillis());
        List<ExtraIdCostTimeBO> extraIdCostTimeBOS =
                messageUserInfoMapper.selectSuccessOrInterruptCostTimeByExtraIdList(extraIdList);
        log.info("查询成功或者打断的任务结束时间：[{}]", System.currentTimeMillis());
        Map<String, Long> extraIdAndCostTimeMap = extraIdCostTimeBOS.stream().distinct()
                .collect(Collectors.toMap(ExtraIdCostTimeBO::getExtraId, ExtraIdCostTimeBO::getCostTime));
        List<ChannelAndCostTimeByDate> channelAndCostTimeByDateList = new ArrayList<>();
        log.info("处理任务开始时间：[{}]", System.currentTimeMillis());
        for (Map.Entry<String, List<AverageCostTimeByDate>> entry : dateAndAverageCostTimeByDateMap.entrySet()) {
            String mapKey = entry.getKey();
            List<AverageCostTimeByDate> value = entry.getValue();
            Map<String, List<AverageCostTimeByDate>> channelAndAverageCostTimeByDateMap =
                    value.stream().collect(Collectors.groupingBy(AverageCostTimeByDate::getChannel));
            List<ChannelAndCostTime> channelAndCostTimeList = new ArrayList<>();
            for (Map.Entry<String, List<AverageCostTimeByDate>> entry1 : channelAndAverageCostTimeByDateMap.entrySet()) {
                List<String> extraIdListTmp = new ArrayList<>();
                entry1.getValue().forEach(p -> extraIdListTmp.addAll(p.getExtraIdListList()));
                long avg;
                long total = extraIdListTmp.stream().distinct().count();
                long totalTime = extraIdListTmp.stream().distinct()
                        .mapToLong(item -> extraIdAndCostTimeMap.getOrDefault(item, 0L)).sum();
                if (total == 0) {
                    avg = 0;
                } else {
                    avg = totalTime / total;
                }
                ChannelAndCostTime.Builder channelAndCostTimeBuilder = ChannelAndCostTime.newBuilder();
                channelAndCostTimeBuilder.setChannel(entry1.getKey());
                channelAndCostTimeBuilder.setAverageCostTime(avg);
                channelAndCostTimeList.add(channelAndCostTimeBuilder.build());
            }
            ChannelAndCostTimeByDate.Builder channelAndCostTimeByDateBuilder = ChannelAndCostTimeByDate.newBuilder();
            channelAndCostTimeByDateBuilder.addAllChannelAndCostTime(channelAndCostTimeList);
            channelAndCostTimeByDateBuilder.setDate(mapKey);
            channelAndCostTimeByDateList.add(channelAndCostTimeByDateBuilder.build());
        }
        log.info("处理任务结束时间：[{}]", System.currentTimeMillis());
        builder.addAllChannelAndCostTimeByDate(channelAndCostTimeByDateList);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMessageAverageCostTimeByExtraIdBatch(MessageAverageCostTimeRequest request,
                                                        StreamObserver<MessageAverageCostTimeResponse> responseObserver) {
        List<MessageAverageCostTime> messageAverageCostTimeList = request.getMessageAverageCostTimeList();
        MessageAverageCostTimeResponse.Builder messageAverageCostTimeResponseBuilder =
                MessageAverageCostTimeResponse.newBuilder();
        List<String> larkExtraIdList = new ArrayList<>();
        List<String> emailExtraIdList = new ArrayList<>();
        List<String> messageExtraIdList = new ArrayList<>();
        messageAverageCostTimeList.forEach(
                messageAverageCostTime -> {
                    if ("lark".equals(messageAverageCostTime.getScope())) {
                        larkExtraIdList.addAll(messageAverageCostTime.getExtraIdListList());

                    } else if ("email".equals(messageAverageCostTime.getScope())) {
                        emailExtraIdList.addAll(messageAverageCostTime.getExtraIdListList());

                    } else if ("message".equals(messageAverageCostTime.getScope())) {
                        messageExtraIdList.addAll(messageAverageCostTime.getExtraIdListList());
                    }
                }
        );

        CompletableFuture<Long> futureLarkAverageCostTime;
        if (larkExtraIdList.isEmpty()) {
            futureLarkAverageCostTime = CompletableFuture.supplyAsync(() -> 0L);
        } else {
            futureLarkAverageCostTime = CompletableFuture.supplyAsync(
                    () -> messageUserInfoMapper.selectAverageCostTimeByExtraIdList(larkExtraIdList)
            );
        }

        CompletableFuture<Long> futureEmailAverageCostTime;
        if (emailExtraIdList.isEmpty()) {
            futureEmailAverageCostTime = CompletableFuture.supplyAsync(() -> 0L);
        } else {
            futureEmailAverageCostTime = CompletableFuture.supplyAsync(
                    () -> messageUserInfoMapper.selectAverageCostTimeByExtraIdList(emailExtraIdList)
            );
        }

        CompletableFuture<Long> futureMessageAverageCostTime;
        if (messageExtraIdList.isEmpty()) {
            futureMessageAverageCostTime = CompletableFuture.supplyAsync(() -> 0L);
        } else {
            futureMessageAverageCostTime = CompletableFuture.supplyAsync(
                    () -> messageUserInfoMapper.selectAverageCostTimeByExtraIdList(messageExtraIdList)
            );
        }

        try {
            MessageScopeAndAverageCostTime.Builder messageAllScopeAndAverageCostTime =
                    MessageScopeAndAverageCostTime.newBuilder();

            List<MessageScopeAndAverageCostTime> messageScopeAndAverageCostTimeList = new ArrayList<>();
            buildMessageScopeAndAverageCostTimeList(futureLarkAverageCostTime, futureEmailAverageCostTime,
                    futureMessageAverageCostTime, messageAllScopeAndAverageCostTime,
                    messageScopeAndAverageCostTimeList);

            messageAverageCostTimeResponseBuilder.setCode(200);
            messageAverageCostTimeResponseBuilder.setMessage("OK");
            messageAverageCostTimeResponseBuilder.addAllMessageScopeAndAverageCostTimeList(
                    messageScopeAndAverageCostTimeList);

        } catch (Exception e) {
            messageAverageCostTimeResponseBuilder.setCode(500);
            messageAverageCostTimeResponseBuilder.setMessage(e.getMessage());
        }
        responseObserver.onNext(messageAverageCostTimeResponseBuilder.build());
        responseObserver.onCompleted();
    }

    private void buildMessageScopeAndAverageCostTimeList(CompletableFuture<Long> futureLarkAverageCostTime,
                                                         CompletableFuture<Long> futureEmailAverageCostTime,
                                                         CompletableFuture<Long> futureMessageAverageCostTime,
                                                         MessageScopeAndAverageCostTime.Builder messageAllScopeAndAverageCostTime,
                                                         List<MessageScopeAndAverageCostTime> messageScopeAndAverageCostTimeList)
            throws InterruptedException, java.util.concurrent.ExecutionException {
        messageAllScopeAndAverageCostTime.setScope("lark");
        messageAllScopeAndAverageCostTime.setAverageCostTime(futureLarkAverageCostTime.get());
        messageScopeAndAverageCostTimeList.add(messageAllScopeAndAverageCostTime.build());

        MessageScopeAndAverageCostTime.Builder messageDeptScopeAndAverageCostTime =
                MessageScopeAndAverageCostTime.newBuilder();
        messageDeptScopeAndAverageCostTime.setScope("email");
        messageDeptScopeAndAverageCostTime.setAverageCostTime(futureEmailAverageCostTime.get());
        messageScopeAndAverageCostTimeList.add(messageDeptScopeAndAverageCostTime.build());

        MessageScopeAndAverageCostTime.Builder messageCustomScopeAndAverageCostTime =
                MessageScopeAndAverageCostTime.newBuilder();
        messageCustomScopeAndAverageCostTime.setScope("message");
        messageCustomScopeAndAverageCostTime.setAverageCostTime(futureMessageAverageCostTime.get());
        messageScopeAndAverageCostTimeList.add(messageCustomScopeAndAverageCostTime.build());
    }

    @Override
    public void fetchGroupList(GroupListRequest request, StreamObserver<GroupListResponse> responseObserver) {
        GroupListResponse.Builder builder = GroupListResponse.newBuilder();
        String appId = request.getAppId();
        try {
            LarkClient.GroupListResult groupListResult = miWorkRobotManager.fetchGroupList(appId);
            List<Group> groups = groupListResult.getGroups();
            List<GroupRecord> groupRecordList = new ArrayList<>();
            if (!groups.isEmpty()) {
                groups.forEach(
                        group -> {
                            GroupRecord.Builder groupRecordBuilder = GroupRecord.newBuilder();
                            groupRecordBuilder.setChatId(group.getChatId());
                            groupRecordBuilder.setName(group.getName());
                            if (StringUtils.isBlank(group.getOwnerUserId())) {
                                groupRecordBuilder.setOwnerUserId(group.getOwnerOpenId());
                                groupRecordBuilder.setOwnerName("robot");
                            } else {
                                groupRecordBuilder.setOwnerUserId(group.getOwnerUserId());
                                groupRecordBuilder.setOwnerName("");
                            }
                            groupRecordList.add(groupRecordBuilder.build());
                        }
                );
            }
            builder.setCode(ResultCode.OK.getCode());
            builder.setMessage(ResultCode.OK.getMessage());
            builder.addAllGroup(groupRecordList);
        } catch (Exception e) {
            builder.setCode(ResultCode.SERVER_INNER_ERROR.getCode());
            builder.setMessage(e.getMessage());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void fetchGroupInfos(GroupInfosRequest request, StreamObserver<GroupInfosResponse> responseObserver) {
        GroupInfosResponse.Builder builder = GroupInfosResponse.newBuilder();
        String appId = request.getAppId();
        List<String> chatIdList = request.getChatIdList();
        try {
            GroupInfosBO groupInfosBO = miWorkRobotManager.fetchGroupInfos(appId, chatIdList);
            List<LarkClient.GroupInfoResult> groupInfoResults = groupInfosBO.getGroupInfoResults();
            List<GroupInfo> groupInfos = new ArrayList<>();
            groupInfoResults.forEach(
                    groupInfoResult -> {
                        GroupInfo.Builder groupInfoBuilder = GroupInfo.newBuilder();
                        groupInfoBuilder.setChatId(groupInfoResult.getChatId());
                        groupInfoBuilder.setName(groupInfoResult.getName());
                        groupInfoBuilder.setOwnerUserId(groupInfoResult.getOwnerUserId());
                        groupInfoBuilder.setOwnerOpenId(groupInfoResult.getOwnerOpenId());
                        groupInfos.add(groupInfoBuilder.build());
                    }
            );
            builder.setCode(ResultCode.OK.getCode());
            builder.setMessage(ResultCode.OK.getMessage());
            builder.addAllGroupInfo(groupInfos);

        } catch (Exception e) {
            builder.setCode(ResultCode.SERVER_INNER_ERROR.getCode());
            builder.setMessage(e.getMessage());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMessageAvgCostTimeByExtraIdList(MessageAvgCostTimeRequest request,
                                                   StreamObserver<MessageAvgCostTimeResponse> responseObserver) {
        List<String> extraIdList = request.getExtraIdListList();
        List<AvgCostTimeBO> avgCostTimeList = messageUserInfoMapper.getAvgCostTimeByExtraIdList(extraIdList);

        MessageAvgCostTimeResponse.Builder builder = MessageAvgCostTimeResponse.newBuilder();

        builder.setCode(ResultCode.OK.getCode());
        builder.setMessage(ResultCode.OK.getMessage());
        List<MessageAvgCostTime> messageAvgCostTimes = MapperUtil.INSTANCE.mapToMessageAvgCostTime(avgCostTimeList);
        builder.addAllMessageAvgCostTimeList(messageAvgCostTimes);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMessageRead(MessageReadRequest request, StreamObserver<MessageReadResponse> responseObserver) {
        String parentExtraId = request.getParentExtraId();
        List<String> subExtraIdListList = request.getSubExtraIdListList();

        CountAndCostTimeBO readCount = messageUserInfoMapper.selectReadCount1(parentExtraId,
                subExtraIdListList);

        CountAndCostTimeBO sendCount = messageUserInfoMapper.countByExtraIdListAndStatus(parentExtraId,
                MessageStatusEnum.SEND_SUCCESS.getStatus());

        CountAndCostTimeBO todoCount = messageUserInfoMapper.countToDo(parentExtraId);

        CountAndCostTimeBO totalCount = messageUserInfoMapper.countByExtraIdListAndStatus(parentExtraId,
                null);

        CountAndCostTimeBO retract_count = messageUserInfoMapper.countByExtraIdListAndStatus(parentExtraId,
                MessageStatusEnum.RETRACTED.getStatus());

        MessageRead.Builder messageReadBuilder = MessageRead.newBuilder();
        messageReadBuilder.setReadCount(readCount.getXCount());
        messageReadBuilder.setSendCount(sendCount.getXCount());
        messageReadBuilder.setCostTime(totalCount.getCostTime());
        messageReadBuilder.setTodoCount(todoCount.getXCount());
        messageReadBuilder.setTotalCount(totalCount.getXCount());
        messageReadBuilder.setRetractCount(retract_count.getXCount());

        MessageReadResponse.Builder builder = MessageReadResponse.newBuilder();
        builder.setCode(ResultCode.OK.getCode());
        builder.setMessage(ResultCode.OK.getMessage());
        builder.setMessageRead(messageReadBuilder.build());

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getPageMessageUnReadDetail(MessageUnreadDetailRequest request,
                                           StreamObserver<MessageUnReadDetailResponse> responseObserver) {
        //高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);

        long pageNo = request.getPageNo();
        long pageSize = request.getPageSize();
        pageNo = pageNo <= 0 ? 1 : pageNo;
        pageSize = pageSize > 100 ? 100 : pageSize;
        Page<String> page = new Page<>(pageNo, pageSize);

        HashSet<String> whiteSet = new HashSet<>(request.getExcludeUsernameList());
        if (CollectionUtils.isNotEmpty(executiveUsernames)) {
            whiteSet.addAll(executiveUsernames);
        }
        String parentExtraId = request.getParentExtraId();
        List<String> subExtraIdListList = request.getSubExtraIdList();

        IPage<String> pageResult =
                messageUserInfoMapper.selectPageUnReadDetail(page,
                        request.getSearchKey(),
                        whiteSet,
                        parentExtraId,
                        subExtraIdListList);

        MessageUnReadDetail.Builder messageUnReadDetailBuilder = MessageUnReadDetail.newBuilder();
        messageUnReadDetailBuilder.setTotal(pageResult.getTotal());
        messageUnReadDetailBuilder.setPageNo(pageResult.getCurrent());
        messageUnReadDetailBuilder.setPageSize(pageResult.getSize());
        messageUnReadDetailBuilder.addAllRecords(pageResult.getRecords());
        messageUnReadDetailBuilder.setTotalWithoutSearch(pageResult.getTotal());

        if (StringUtils.isNotBlank(request.getSearchKey())) {
            IPage<String> pageResultWithoutSearchKey =
                    messageUserInfoMapper.selectPageUnReadDetail(page,
                            StringUtils.EMPTY,
                            whiteSet,
                            parentExtraId,
                            subExtraIdListList);
            messageUnReadDetailBuilder.setTotalWithoutSearch(pageResultWithoutSearchKey.getTotal());
        }

        MessageUnReadDetailResponse.Builder builder = MessageUnReadDetailResponse.newBuilder();
        builder.setCode(ResultCode.OK.getCode());
        builder.setMessage(ResultCode.OK.getMessage());
        builder.setMessageUnreadDetail(messageUnReadDetailBuilder);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getMessageFailedDetail(ExtraIdRequest request,
                                       StreamObserver<UsernameResponse> responseStreamObserver) {
        String extraId = request.getExtraId();
        List<MessageUserInfo> messageUserInfoList =
                messageUserInfoMapper.selectList(
                        Wrappers.<MessageUserInfo>lambdaQuery().eq(MessageUserInfo::getExtraId, extraId)
                                .eq(MessageUserInfo::getChannel, MessageChannelEnum.MI_WORK.getType())
                                .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_INTERRUPT.getStatus()));

        Set<String> userNames =
                messageUserInfoList.stream().map(MessageUserInfo::getUsername).collect(Collectors.toSet());

        UsernameResponse.Builder builder = UsernameResponse.newBuilder();
        builder.setCode(ResultCode.OK.getCode());
        builder.setMessage(ResultCode.OK.getMessage());
        builder.addAllUsernames(userNames.isEmpty() ? Sets.newHashSet() : userNames);

        responseStreamObserver.onNext(builder.build());
        responseStreamObserver.onCompleted();
    }
}
