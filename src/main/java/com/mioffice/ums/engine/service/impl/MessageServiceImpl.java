package com.mioffice.ums.engine.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ExecutorBuilder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mioffice.ums.engine.config.MqProperties;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.enums.ReadStatusEnum;
import com.mioffice.ums.engine.enums.ResolveReadyEnum;
import com.mioffice.ums.engine.event.SendMessageEvent;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import com.mioffice.ums.engine.manager.EnhanceMiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageTemplateInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumer;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumerManager;
import com.mioffice.ums.engine.service.MessageService;
import com.mioffice.ums.engine.utils.FileUtil;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.xiaomi.infra.galaxy.talos.client.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.*;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mioffice.ums.engine.enums.constants.RedisConstants.LARK_MSG_READ_SCAN_START_ID;
import static com.mioffice.ums.engine.enums.constants.RedisConstants.LARK_MSG_UNREAD_APPID_LIST;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.11
 */
@Slf4j
@Service
public class MessageServiceImpl extends ServiceImpl<MessageUserInfoMapper, MessageUserInfo> implements MessageService {

    private final MqProperties mqProperties;
    private final MessageUserInfoMapper messageUserInfoMapper;
    private final MessageTemplateInfoMapper messageTemplateInfoMapper;
    private final SendMqEventBus sendMQEventBus;
    private final EnhanceMiWorkRobotManager enhanceMiWorkRobotManager;
    private final RedissonClient redissonClient;
    private final ProducerAndConsumerManager producerAndConsumerManager;
    private final ExecutorService RETRY_SENDING_POOL = ExecutorBuilder.create()
            .setCorePoolSize(20)
            .setMaxPoolSize(20 * 2)
            .setWorkQueue(new LinkedBlockingQueue<>(200 * 1024))
            .setThreadFactory(new NamedThreadFactory("retry-sending-task"))
            .buildFinalizable();

    private final ExecutorService RETRY_FAIL_POOL = ExecutorBuilder.create()
            .setCorePoolSize(20)
            .setMaxPoolSize(20 * 2)
            .setWorkQueue(new LinkedBlockingQueue<>(200 * 1024))
            .setThreadFactory(new NamedThreadFactory("retry-fail-task"))
            .buildFinalizable();

    public MessageServiceImpl(MqProperties mqProperties,
                              MessageUserInfoMapper messageUserInfoMapper,
                              MessageTemplateInfoMapper messageTemplateInfoMapper,
                              SendMqEventBus sendMQEventBus,
                              EnhanceMiWorkRobotManager enhanceMiWorkRobotManager,
                              RedissonClient redissonClient,
                              ProducerAndConsumerManager producerAndConsumerManager) {
        this.mqProperties = mqProperties;
        this.messageUserInfoMapper = messageUserInfoMapper;
        this.messageTemplateInfoMapper = messageTemplateInfoMapper;
        this.sendMQEventBus = sendMQEventBus;
        this.enhanceMiWorkRobotManager = enhanceMiWorkRobotManager;
        this.redissonClient = redissonClient;
        this.producerAndConsumerManager = producerAndConsumerManager;
    }

    @Override
    public void retrySendFailMsg() {

        long timeMillis = System.currentTimeMillis() - mqProperties.getRetry().getFailedAfter() * 60 * 1000;

        // 无专属队列的失败重试
        RETRY_FAIL_POOL.execute(() -> {

            long offset = 0L;
            int size = 500;

            while (true) {
                // 排除专属队列的机器人ID和统一Topic的机器人ID
                Set<String> excludeAppIds = new HashSet<>(producerAndConsumerManager.getEnableTopicRobotIdSet());
                Set<String> unifiedTopicRobotIds = producerAndConsumerManager.getUnifiedTopicRobotIds();
                // 将统一Topic的机器人ID也添加到排除列表中
                excludeAppIds.addAll(unifiedTopicRobotIds);
                
                LambdaQueryWrapper<MessageUserInfo> queryWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                        .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_FAIL.getStatus())
                        .lt(MessageUserInfo::getUpdateTime, timeMillis)
                        .lt(MessageUserInfo::getRetryCount, mqProperties.getRetry().getLimit())
                        .notIn(!excludeAppIds.isEmpty(), MessageUserInfo::getAppId, excludeAppIds)
                        .last(String.format("limit %d,%d", offset, size));
                
                List<MessageUserInfo> messageUserInfoList = list(queryWrapper);

                if (messageUserInfoList.isEmpty()) {
                    break;
                }

                offset = offset + size;

                // 增加重试次数
                List<Long> idList =
                        messageUserInfoList.stream().map(MessageUserInfo::getId).collect(Collectors.toList());
                messageUserInfoMapper.updateAddRetryCount(idList, 1, MessageStatusEnum.SENDING.getStatus(), System.currentTimeMillis());

                this.postEventByChannel(messageUserInfoList);
            }
        });
    }

    @Override
    public void retrySendFailMsgOfTopic() {
        long timeMillis = System.currentTimeMillis() - mqProperties.getRetry().getFailedAfter() * 60 * 1000;

        // 有专属队列的失败重试
        producerAndConsumerManager.getEnableTopicRobotIdSet()
                .forEach(robotId -> {
                            Optional<ProducerAndConsumer> producerAndConsumer =
                                    producerAndConsumerManager.getRobotProducerAndConsumer(robotId);

                            producerAndConsumer.ifPresent(pc -> RETRY_FAIL_POOL.execute(() -> {

                                long offset = 0L;
                                int size = 500;

                                while (true) {
                                    List<MessageUserInfo> messageUserInfoList = list(
                                            Wrappers.<MessageUserInfo>lambdaQuery()
                                                    .eq(MessageUserInfo::getMessageStatus,
                                                            MessageStatusEnum.SEND_FAIL.getStatus())
                                                    .eq(MessageUserInfo::getAppId, robotId)
                                                    .lt(MessageUserInfo::getUpdateTime, timeMillis)
                                                    .lt(MessageUserInfo::getRetryCount, mqProperties.getRetry().getLimit())
                                                    .last(String.format("limit %d,%d", offset, size))
                                    );
                                    if (messageUserInfoList.isEmpty()) {
                                        break;
                                    }
                                    offset = offset + size;

                                    // 增加重试次数
                                    List<Long> idList =
                                            messageUserInfoList.stream().map(MessageUserInfo::getId)
                                                    .collect(Collectors.toList());
                                    messageUserInfoMapper.updateAddRetryCount(idList, 1, MessageStatusEnum.SENDING.getStatus(), System.currentTimeMillis());

                                    pc.produce(messageUserInfoList);
                                }

                            }));

                        }
                );
    }

    @Override
    public void retrySendingMsg(Long timeMills) {
        if (timeMills == null) {
            timeMills = System.currentTimeMillis() - mqProperties.getRetry().getSendingAfter() * 60 * 1000;
        } else {
            timeMills = System.currentTimeMillis() - timeMills * 60 * 1000;
        }

        Long timeMillis = timeMills;

        RETRY_SENDING_POOL.execute(() -> {
            long offset = 0L;
            int size = 500;

            while (true) {
                // 排除专属队列的机器人ID和统一Topic的机器人ID
                Set<String> excludeAppIds = new HashSet<>(producerAndConsumerManager.getEnableTopicRobotIdSet());
                Set<String> unifiedTopicRobotIds = producerAndConsumerManager.getUnifiedTopicRobotIds();
                // 将统一Topic的机器人ID也添加到排除列表中
                excludeAppIds.addAll(unifiedTopicRobotIds);
                
                LambdaQueryWrapper<MessageUserInfo> queryWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                        .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SENDING.getStatus())
                        .lt(MessageUserInfo::getUpdateTime, timeMillis)
                        .notIn(!excludeAppIds.isEmpty(), MessageUserInfo::getAppId, excludeAppIds)
                        .last(String.format("limit %d,%d", offset, size));
                
                List<MessageUserInfo> messageUserInfoList = list(queryWrapper);

                if (messageUserInfoList.isEmpty()) {
                    break;
                }

                offset = offset + size;

                this.postEventByChannel(messageUserInfoList);
            }
        });
    }

    @Override
    public void retrySendingMsgOfTopic(Long timeMills) {

        if (timeMills == null) {
            timeMills = System.currentTimeMillis() - mqProperties.getRetry().getSendingAfter() * 60 * 1000;
        } else {
            timeMills = System.currentTimeMillis() - timeMills * 60 * 1000;
        }

        Long timeMillis = timeMills;

        producerAndConsumerManager.getEnableTopicRobotIdSet()
                .forEach(robotId -> {
                    Optional<ProducerAndConsumer> producerAndConsumer =
                            producerAndConsumerManager.getRobotProducerAndConsumer(robotId);

                    producerAndConsumer.ifPresent(pc -> RETRY_SENDING_POOL.execute(() -> {
                        long offset = 0L;
                        int size = 500;

                        while (true) {
                            List<MessageUserInfo> messageUserInfoList = list(
                                    Wrappers.<MessageUserInfo>lambdaQuery()
                                            .eq(MessageUserInfo::getAppId, robotId)
                                            .eq(MessageUserInfo::getMessageStatus,
                                                    MessageStatusEnum.SENDING.getStatus())
                                            .lt(MessageUserInfo::getUpdateTime, timeMillis)
                                            .last(String.format("limit %d,%d", offset, size))
                            );

                            if (messageUserInfoList.isEmpty()) {
                                break;
                            }

                            offset = offset + size;

                            pc.produce(messageUserInfoList);
                        }
                    }));
                });
    }

    @Override
    public void retryUnifiedTopicFailedMessages() {
        if (!producerAndConsumerManager.isUnifiedTopicEnabled()) {
            log.debug("统一Topic功能未启用，跳过重试失败消息任务");
            return;
        }

        long timeMillis = System.currentTimeMillis() - mqProperties.getRetry().getFailedAfter() * 60 * 1000;
        Set<String> unifiedTopicRobotIds = producerAndConsumerManager.getUnifiedTopicRobotIds();
        
        if (unifiedTopicRobotIds.isEmpty()) {
            log.debug("统一Topic机器人ID列表为空，跳过重试失败消息任务");
            return;
        }

        RETRY_FAIL_POOL.execute(() -> {
            int size = 500;
            int processedCount = 0;
            int batchCount = 0;
            final int MAX_BATCH_COUNT = 200; // 兜底策略：最多执行200次批处理，100000条

            while (batchCount < MAX_BATCH_COUNT) {
                // 获取专属队列的机器人ID，需要排除这些应用
                Set<String> excludeAppIds = producerAndConsumerManager.getEnableTopicRobotIdSet();
                
                LambdaQueryWrapper<MessageUserInfo> queryWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                        .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_FAIL.getStatus())
                        .in(MessageUserInfo::getAppId, unifiedTopicRobotIds)
                        .lt(MessageUserInfo::getUpdateTime, timeMillis)
                        .lt(MessageUserInfo::getRetryCount, mqProperties.getRetry().getLimit())
                        .orderByAsc(MessageUserInfo::getId)
                        .last(String.format("limit %d", size));
                
                // 排除专属队列的机器人ID，避免重复处理
                if (!excludeAppIds.isEmpty()) {
                    queryWrapper.notIn(MessageUserInfo::getAppId, excludeAppIds);
                }
                
                List<MessageUserInfo> messageUserInfoList = list(queryWrapper);
                
                if (messageUserInfoList.isEmpty()) {
                    break; // 没有更多数据，正常退出
                }
                
                batchCount++;
                processedCount += messageUserInfoList.size();

                // 增加重试次数
                List<Long> idList = messageUserInfoList.stream().map(MessageUserInfo::getId).collect(Collectors.toList());
                messageUserInfoMapper.updateAddRetryCount(idList, 1, MessageStatusEnum.SENDING.getStatus(), System.currentTimeMillis());

                // 尝试重新发送到统一Topic
                boolean success = producerAndConsumerManager.sendToUnifiedTopic(messageUserInfoList);
                
                if (!success) {
                    log.warn("统一Topic重试失败，回退到Talos公共队列, messageCount: {}", messageUserInfoList.size());
                    // 回退到Talos公共队列
                    this.postEventByChannel(messageUserInfoList);
                } else {
                    log.info("统一Topic重试成功, messageCount: {}", messageUserInfoList.size());
                }
            }
            
            if (batchCount >= MAX_BATCH_COUNT) {
                log.warn("Reached maximum batch count ({}) for failed message retry, may have messages left", MAX_BATCH_COUNT);
            }
            
            log.info("Failed message retry completed, total processed: {}, batches: {}", processedCount, batchCount);
        });
    }

    @Override
    public void retryUnifiedTopicSendingMessages(Long timeMills) {
        if (!producerAndConsumerManager.isUnifiedTopicEnabled()) {
            log.debug("统一Topic功能未启用，跳过重试发送中消息任务");
            return;
        }

        if (timeMills == null) {
            timeMills = System.currentTimeMillis() - mqProperties.getRetry().getSendingAfter() * 60 * 1000;
        } else {
            timeMills = System.currentTimeMillis() - timeMills * 60 * 1000;
        }

        Long timeMillis = timeMills;

        Set<String> unifiedTopicRobotIds = producerAndConsumerManager.getUnifiedTopicRobotIds();
        
        if (unifiedTopicRobotIds.isEmpty()) {
            log.debug("统一Topic机器人ID列表为空，跳过重试发送中消息任务");
            return;
        }

        RETRY_SENDING_POOL.execute(() -> {
            int size = 500;
            int processedCount = 0;
            int batchCount = 0;
            final int MAX_BATCH_COUNT = 1000; // 兜底策略：最多执行1000次批处理

            while (batchCount < MAX_BATCH_COUNT) {
                // 获取专属队列的机器人ID，需要排除这些应用
                Set<String> excludeAppIds = producerAndConsumerManager.getEnableTopicRobotIdSet();
                
                LambdaQueryWrapper<MessageUserInfo> queryWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                        .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SENDING.getStatus())
                        .in(MessageUserInfo::getAppId, unifiedTopicRobotIds)
                        .lt(MessageUserInfo::getUpdateTime, timeMillis)
                        .orderByAsc(MessageUserInfo::getId)
                        .last(String.format("limit %d", size));
                
                // 排除专属队列的机器人ID，避免重复处理
                if (!excludeAppIds.isEmpty()) {
                    queryWrapper.notIn(MessageUserInfo::getAppId, excludeAppIds);
                }
                
                List<MessageUserInfo> messageUserInfoList = list(queryWrapper);
                
                if (messageUserInfoList.isEmpty()) {
                    break; // 没有更多数据，正常退出
                }
                
                batchCount++;
                processedCount += messageUserInfoList.size();

                // 尝试重新发送到统一Topic
                boolean success = producerAndConsumerManager.sendToUnifiedTopic(messageUserInfoList);
                
                if (!success) {
                    log.warn("统一Topic发送中重试失败，回退到Talos公共队列, messageCount: {}", messageUserInfoList.size());
                    // 回退到Talos公共队列
                    this.postEventByChannel(messageUserInfoList);
                } else {
                    log.info("统一Topic发送中重试成功, messageCount: {}", messageUserInfoList.size());
                }
            }
            
            if (batchCount >= MAX_BATCH_COUNT) {
                log.warn("Reached maximum batch count ({}) for sending message retry, may have messages left", MAX_BATCH_COUNT);
            }
            
            log.info("Sending message retry completed, total processed: {}, batches: {}", processedCount, batchCount);
        });
    }

    @Override
    public void deleteExpiredMessage(Long timeMills) {
        LocalDate now;
        if (timeMills == 0) {
            now = LocalDate.now();
        } else {
            now = Instant.ofEpochMilli(timeMills).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        }
        LocalDate localDate = now.minusDays(7);
        LocalDateTime beginLocalDateTime = LocalDateTime.of(localDate, LocalTime.MIN);
        while (true) {
            LambdaQueryWrapper<MessageUserInfo> lambdaQueryWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
//                    .and(message -> message
//                            .and(m -> m
//                                    .eq(MessageUserInfo::getReadStatus, ReadStatusEnum.YES.getCode())
//                                    .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_SUCCESS.getStatus()))
//                            .or()
//                            .and(m -> m
//                                    .ne(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_SUCCESS.getStatus())
//                                    .ne(MessageUserInfo::getReadStatus, ReadStatusEnum.NO.getCode())))
                    .le(MessageUserInfo::getCreateTime,
                            beginLocalDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());

            lambdaQueryWrapper
                    .orderByAsc(MessageUserInfo::getId)
                    .last("limit 500");

            List<MessageUserInfo> messageUserInfoList = list(lambdaQueryWrapper);
            if (messageUserInfoList.isEmpty()) {
                break;
            }

            List<Long> messageIds =
                    messageUserInfoList.stream().map(MessageUserInfo::getId).collect(Collectors.toList());
            removeByIds(messageIds);

            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(100));
        }
    }

    @Override
    public void scanMessageRead(Integer shardIndex, Integer shardTotal) {

        RAtomicLong obj = redissonClient.getAtomicLong(LARK_MSG_READ_SCAN_START_ID);

        List<String> unReadAppIdList = redissonClient.getList(LARK_MSG_UNREAD_APPID_LIST);

        if (unReadAppIdList.isEmpty()) {
            return;
        }

        List<List<String>> splitLists = ListUtil.splitAvg(new ArrayList<>(unReadAppIdList), shardTotal);

        if (splitLists.size() < (shardIndex + 1)) {
            return;
        }

        List<String> myUnReadAppIdList = splitLists.get(shardIndex);

        if (myUnReadAppIdList.isEmpty()) {
            return;
        }

        log.info("总分片数:{},分片:{},app_id:{}", shardTotal, shardIndex, String.join(",", myUnReadAppIdList));

        long offset = 0L;
        int size = 200;
        while (true) {
            if (myUnReadAppIdList.isEmpty()) {
                return;
            }
            LambdaQueryWrapper<MessageUserInfo> lambdaQueryWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getReadStatus, ReadStatusEnum.NO.getCode())
                    .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_SUCCESS.getStatus())
                    .eq(MessageUserInfo::getChannel, MessageChannelEnum.MI_WORK.getType())
                    .eq(MessageUserInfo::getChatId, StringUtils.EMPTY)
                    .in(MessageUserInfo::getAppId, myUnReadAppIdList)
                    .ne(MessageUserInfo::getUsername, StringUtils.EMPTY)
                    .ne(MessageUserInfo::getMessageId, StringUtils.EMPTY);

            if (Objects.nonNull(obj)) {
                lambdaQueryWrapper.gt(MessageUserInfo::getId, obj.get());
            }

            lambdaQueryWrapper.orderByDesc(MessageUserInfo::getId).last(String.format("limit %d,%d", offset, size));

            List<MessageUserInfo> unReadMessageUserInfoList = list(lambdaQueryWrapper);
            if (unReadMessageUserInfoList.isEmpty()) {
                break;
            }

            offset = offset + size;

            RETRY_SENDING_POOL.execute(() -> {
                unReadMessageUserInfoList.forEach(messageUserInfo -> {
                    try {
                        boolean isRead = enhanceMiWorkRobotManager.checkUserRead(messageUserInfo.getAppId(),
                                messageUserInfo.getMessageId(),
                                messageUserInfo.getUsername());
                        messageUserInfo.setReadStatus(
                                isRead ? ReadStatusEnum.YES.getCode() : ReadStatusEnum.NO.getCode());
                        log.info("checkUserRead succ {},{},{},{}:",
                                messageUserInfo.getId(),
                                messageUserInfo.getAppId(),
                                messageUserInfo.getMessageId(),
                                messageUserInfo.getUsername());
                    } catch (Exception e) {
//                        String logMsg =
//                                String.format("checkUserRead error {%s},{%s},{%s},{%s}:",
//                                        messageUserInfo.getId(),
//                                        messageUserInfo.getAppId(),
//                                        messageUserInfo.getMessageId(),
//                                        messageUserInfo.getUsername());
//                        log.error(logMsg, e);
                    }
                });
                // 只需要更新已读的
                List<MessageUserInfo> readList = unReadMessageUserInfoList
                        .stream()
                        .filter(messageUserInfo -> ReadStatusEnum.YES.getCode() == messageUserInfo.getReadStatus())
                        .collect(Collectors.toList());
                updateBatchById(readList);
            });
        }

    }

    @Override
    public void scanUnReadAppId() {
        RAtomicLong obj = redissonClient.getAtomicLong(LARK_MSG_READ_SCAN_START_ID);
        RList<String> list = redissonClient.getList(LARK_MSG_UNREAD_APPID_LIST);
        list.clear();
        List<String> unReadAppIdList = messageUserInfoMapper.queryUnReadAppId(Objects.nonNull(obj) ?
                obj.get() : null);
        list.addAll(unReadAppIdList);
        log.info("unReadAppIdList:{}", JsonUtils.toJson(unReadAppIdList));
    }

    //    @Async("asyncUploadImageExecutor")
    @Override
    public void asyncUploadImage(Set<String> imageUrlList, MessageTemplateInfo messageTemplateInfo, String appId)
            throws Exception {
        // 抠出图片
        Map<String, String> imageKeyMap = new HashMap<>(10);
//        Set<String> failedImageUrl = new HashSet<>();
        for (String url : imageUrlList) {
//            try {
            String imageKey = enhanceMiWorkRobotManager.uploadImageForLark(appId, url);
            String fileId = FileUtil.parseFileId(url);
            imageKeyMap.put(fileId, imageKey);
//            } catch (Exception e) {
//                failedImageUrl.add(url);
//            }
        }
//        for (String url : failedImageUrl) {
//            try {
//                String imageKey = enhanceMiWorkRobotManager.uploadImageForLark(appId, url);
//                String fileId = FileUtil.parseFileId(url);
//                imageKeyMap.put(fileId, imageKey);
//            } catch (Exception e) {
//                log.error("asyncUploadImage fail,appId:{},url:{}",
//                        appId,
//                        url);
//            }
//        }
        messageTemplateInfo.setImages(JsonUtils.toJson(imageKeyMap));
        messageTemplateInfo.setResolveReady(ResolveReadyEnum.YES.getCode());
    }

    @Override
    public void asyncUploadSaasImage(Set<String> imageUrlList, MessageTemplateInfo messageTemplateInfo, String appId) throws Exception {
        Map<String, String> saasImageKeyMap = new HashMap<>(10);
        for (String url : imageUrlList) {
            String imageKey = enhanceMiWorkRobotManager.uploadSaasImageForLark(appId, url);
            String fileId = FileUtil.parseFileId(url);
            saasImageKeyMap.put(fileId, imageKey);
        }
        messageTemplateInfo.setSaasImages(JsonUtils.toJson(saasImageKeyMap));
    }

    @Async("async-retract-message")
    @Override
    public void asyncRetractMessage(String extraId) {
        long pageNo = 1L, pageSize = 100L;
        Page<MessageUserInfo> dataPage = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<MessageUserInfo> lambdaQueryWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                .eq(MessageUserInfo::getExtraId, extraId)
                .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_SUCCESS.getStatus())
                .eq(MessageUserInfo::getChannel, MessageChannelEnum.MI_WORK.getType())
                .ne(MessageUserInfo::getMessageId, StringUtils.EMPTY);
        IPage<MessageUserInfo> messageUserInfoPage = messageUserInfoMapper.selectPage(dataPage, lambdaQueryWrapper);

        if (CollectionUtils.isNotEmpty(messageUserInfoPage.getRecords())) {
            while (CollectionUtils.isNotEmpty(messageUserInfoPage.getRecords())) {
                messageUserInfoPage.getRecords().forEach(messageUserInfo -> {
                    try {
                        enhanceMiWorkRobotManager.retractMessage(messageUserInfo.getAppId(),
                                messageUserInfo.getMessageId());
                        messageUserInfo.setMessageStatus(MessageStatusEnum.RETRACTED.getStatus());
                    } catch (Exception e) {
                        log.error("撤回失败:{},{}", extraId, messageUserInfo.getId());
                    }
                });
                messageUserInfoMapper.updateBatch(messageUserInfoPage.getRecords());
                pageNo++;
                dataPage = new Page<>(pageNo, pageSize);
                messageUserInfoPage = messageUserInfoMapper.selectPage(dataPage, lambdaQueryWrapper);
            }
        }
    }

    /**
     * 消息用户列表， 同一批次下，类型一致
     *
     * @param userInfoList 用户列表
     */
    @Override
    @Async("message-to-eventbus")
    public void asyncSendMsg2EventBus(List<MessageUserInfo> userInfoList) {
        if (userInfoList.isEmpty()) {
            return;
        }
        MessageChannelEnum messageChannelEnum = MessageChannelEnum.get(userInfoList.get(0).getChannel());
        sendMQEventBus.postEvent(new SendMessageEvent(messageChannelEnum, userInfoList));
    }

    private void postEventByChannel(List<MessageUserInfo> messageUserInfoList) {

        Map<MessageChannelEnum, List<MessageUserInfo>> map = messageUserInfoList
                .parallelStream()
                .collect(
                        Collectors.groupingBy(
                                p -> MessageChannelEnum.get(p.getChannel()),
                                Collectors.mapping(Function.identity(), Collectors.toList())
                        )
                );

        map.forEach((channelEnum, list) -> sendMQEventBus.postEvent(new SendMessageEvent(channelEnum, list)));
    }

    public static void main(String[] args) {
        LocalDate now = Instant.ofEpochMilli(1632296745906L).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        System.out.println(now);
    }
}
