package com.mioffice.ums.engine.talos;

import com.mioffice.ums.engine.limiter.LimiterManager;
import com.xiaomi.infra.galaxy.rpc.thrift.Credential;
import com.xiaomi.infra.galaxy.rpc.thrift.UserType;
import com.xiaomi.infra.galaxy.talos.admin.TalosAdmin;
import com.xiaomi.infra.galaxy.talos.client.TalosClientConfig;
import com.xiaomi.infra.galaxy.talos.client.TalosClientConfigKeys;
import com.xiaomi.infra.galaxy.talos.client.TopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducer;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducerConfig;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.thrift.DescribeTopicRequest;
import com.xiaomi.infra.galaxy.talos.thrift.Message;
import com.xiaomi.infra.galaxy.talos.thrift.Topic;
import com.xiaomi.infra.galaxy.talos.thrift.TopicTalosResourceName;
import libthrift091.TException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/8/10 23:02
 */
@Slf4j
public class DefaultTalosHelper implements TalosHelper {

    private final Map<String, TopicTalosResourceName> TOPICS = new HashMap<>();
    private final Map<String, TalosProducer> PRODUCERS = new HashMap<>();
    private final Map<String, PollConsumer<?>> CONSUMERS = new HashMap<>();

    private final String accessKey;
    private final String accessSecret;

    private final Credential credential;
    private final TalosClientConfig clientConfig;
    private final TalosProducerConfig producerConfig;
    private final Properties defaultProperties;

    public DefaultTalosHelper(Properties properties) {
        this.defaultProperties = properties;
        this.accessKey = properties.getProperty(TalosClientConfigKeys.GALAXY_TALOS_ACCESS_KEY);
        this.accessSecret = properties.getProperty(TalosClientConfigKeys.GALAXY_TALOS_ACCESS_SECRET);
        credential = new Credential().setSecretKeyId(accessKey).setSecretKey(accessSecret).setType(UserType.DEV_XIAOMI);
        clientConfig = new TalosClientConfig(properties);
        producerConfig = new TalosProducerConfig(properties);
    }

    @Override
    public synchronized TopicTalosResourceName getTopicResName(String topicName) throws TException {
        TopicTalosResourceName topicResName = TOPICS.get(topicName);
        if (topicResName == null) {
            TalosAdmin admin = new TalosAdmin(clientConfig, credential);
            Topic topic = admin.describeTopic(new DescribeTopicRequest(topicName));
            topicResName = topic.getTopicInfo().getTopicTalosResourceName();
            TOPICS.put(topicName, topicResName);
        }
        return topicResName;
    }

    @Override
    public synchronized TalosProducer createProducer(final String topicName, TopicAbnormalCallback topicAbnormalCallback,
                                                     UserMessageCallback userMessageCallback) throws TException {
        TalosProducer producer = new TalosProducer(producerConfig, credential, topicName, topicAbnormalCallback,
                userMessageCallback);
        PRODUCERS.put(topicName, producer);
        return producer;
    }

    @Override
    public void shutdownAll() {
        PRODUCERS.forEach((topicName, talosProducer) -> talosProducer.shutdown());
        CONSUMERS.forEach((topicName, talosConsumer) -> talosConsumer.shutDown());
    }

    @Override
    public void produce(String topicName, byte[] bytes) throws TException {
        TalosProducer producer = getProducer(topicName);
        Message message = new Message(ByteBuffer.wrap(bytes));
        List<Message> messageList = new ArrayList<>(2);
        messageList.add(message);
        producer.addUserMessage(messageList);
    }

    @Override
    public void produce(String topicName, String msg, Charset charset) throws TException {
        byte[] bytes = msg.getBytes(charset);
        produce(topicName, bytes);
    }

    @Override
    public void produce(String topicName, String msg) throws TException {
        produce(topicName, msg, StandardCharsets.UTF_8);
    }

    @Override
    public void produce(String topicName, List<String> msgList) throws TException {
        TalosProducer producer = getProducer(topicName);
        List<Message> messageList = new ArrayList<>();
        for (String s : msgList) {
            messageList.add(new Message(ByteBuffer.wrap(s.getBytes(StandardCharsets.UTF_8))));
        }
        log.info("talos helper produce list = [{}]", msgList);
        producer.addUserMessage(messageList);
    }

    @Override
    public <T> PollConsumer<T> createConsume(MessageProcessor<T> processor, LimiterManager limiterManager) {

        PollConsumer<T> consumer = new PollConsumer<>(defaultProperties, processor, limiterManager);
        CONSUMERS.put(processor.getTopic(), consumer);
        return consumer;
    }

    protected synchronized TalosProducer getProducer(String topicName) {
        TalosProducer talosProducer = PRODUCERS.get(topicName);
        Assert.notNull(talosProducer, String.format("The talos producer of name is %s not be found.", topicName));
        return talosProducer;
    }

}
