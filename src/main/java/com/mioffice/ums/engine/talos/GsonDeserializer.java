package com.mioffice.ums.engine.talos;

import com.mioffice.ums.engine.exceptions.DeserializeException;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.Deserializer;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020/8/25
 */
@Slf4j
public class GsonDeserializer<T> implements Deserializer<T> {

    final Class<T> clazz;

    public GsonDeserializer(Class<T> clazz) {
        this.clazz = clazz;
    }

    @Override
    public void configure(Map<String, ?> map, boolean b) {
        // noting to do
    }

    @Override
    public T deserialize(String topic, byte[] bytes) {
        String msg = new String(bytes, StandardCharsets.UTF_8);
        if (String.class.equals(clazz)) {
            return (T) msg;
        }

        try {
            log.info("{} 消费到消息 message ={}", topic, msg);
            return JsonUtils.parse(msg, clazz);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new DeserializeException(String.format("source: %s, ex: %s", msg, e.getMessage()));
        }
    }

    @Override
    public void close() {
        // noting to do
    }
}
