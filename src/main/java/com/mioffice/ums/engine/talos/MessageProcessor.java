package com.mioffice.ums.engine.talos;

import com.mioffice.ums.engine.entity.bo.ChannelRateConfigBO;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * <AUTHOR>
 * @since 2020/8/25
 */
public interface MessageProcessor<T> {

    /**
     * 处理
     *
     * @param record
     */
    void process(ConsumerRecord<String, T> record);

    /**
     * msg deserializer
     *
     * @return
     */
    default GsonDeserializer<T> newDeserializer(Class<T> clazz) {
        return new GsonDeserializer<>(clazz);
    }

    /**
     * 获取限流器 key
     *
     * @param t
     * @return
     */
    ChannelRateConfigBO getRateLimitConfig(T t);

    /**
     * topic
     *
     * @return
     */
    String getTopic();

    /**
     * ConsumerGroup
     *
     * @return
     */
    String getConsumerGroup();

    /**
     * ClientPrefix
     *
     * @return
     */
    String getClientPrefix();

    void updatedFail(ConsumerRecord<String, T> record);
}
