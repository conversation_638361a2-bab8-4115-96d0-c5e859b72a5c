package com.mioffice.ums.engine.talos;

import cn.hutool.core.thread.ExecutorBuilder;
import com.mioffice.ums.engine.entity.bo.ChannelRateConfigBO;
import com.mioffice.ums.engine.exceptions.LimitException;
import com.mioffice.ums.engine.limiter.LimiterManager;
import com.xiaomi.infra.galaxy.talos.client.NamedThreadFactory;
import com.xiaomi.infra.galaxy.talos.client.TalosClientConfigKeys;
import com.xiaomi.infra.galaxy.talos.wrapper.consumer.ConsumerWrapper;
import com.xiaomi.infra.galaxy.talos.wrapper.consumer.ConsumerWrapperConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;

import java.lang.reflect.ParameterizedType;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.LockSupport;

/**
 * <AUTHOR>
 * @since 2020/8/25
 */
@Slf4j
public class PollConsumer<T> {

    private final ExecutorService singleExecutor;
    private final ExecutorService workExecutor;

    private final AtomicBoolean isRunning = new AtomicBoolean(true);
    private final ConsumerWrapper<String, T> consumer;
    private final MessageProcessor<T> processor;
    private final LimiterManager limiterManager;
    private static final int TIMEOUT = 1;

    protected PollConsumer(Properties defaultProps, MessageProcessor<T> processor, LimiterManager limiterManager) {
        Properties properties = new Properties(defaultProps);
        properties.setProperty(ConsumerWrapperConfig.GALAXY_TALOS_CONSUMER_GROUP_NAME, processor.getConsumerGroup());
        properties.setProperty(ConsumerWrapperConfig.GALAXY_TALOS_CONSUMER_ID_PREFIX, processor.getClientPrefix());
        properties.setProperty(TalosClientConfigKeys.GALAXY_TALOS_CONSUMER_MAX_FETCH_RECORDS, "1");

        // 后面删除
//        properties.setProperty("galaxy.talos.consumer.start.reset.offset.value", "-2");
//        properties.setProperty("galaxy.talos.consumer.start.whether.reset.offset", "true");

        Class<T> tClass = (Class<T>) ParameterizedType.class.cast(processor.getClass().getGenericInterfaces()[0])
                .getActualTypeArguments()[0];

        consumer = new ConsumerWrapper<>(properties, null, processor.newDeserializer(tClass));
        consumer.subscribe(Collections.singletonList(processor.getTopic()));

        this.processor = processor;
        this.limiterManager = limiterManager;

        // int corePoolSize = Runtime.getRuntime().availableProcessors() + 1;
        // 属于IO 线程，建议给大点
        int corePoolSize = 256;
        singleExecutor = ExecutorBuilder.create()
                .setCorePoolSize(1)
                .setMaxPoolSize(1)
                .setKeepAliveTime(0)
                .setThreadFactory(new NamedThreadFactory(processor.getTopic() + "-poll-from-talos"))
                .buildFinalizable();

        workExecutor = ExecutorBuilder.create()
                .setCorePoolSize(corePoolSize)
                .setMaxPoolSize(corePoolSize * 2)
                .setThreadFactory(new NamedThreadFactory(processor.getTopic() + "-send"))
                .buildFinalizable();

        singleExecutor.execute(this::loop);
    }

    public void shutDown() {
        isRunning.compareAndSet(true, false);
        singleExecutor.shutdown();
        workExecutor.shutdown();
    }

    private void loop() {
        while (isRunning.get()) {
            ConsumerRecords<String, T> records = consumer.poll(TIMEOUT);
            if (!records.isEmpty()) {
                for (ConsumerRecord<String, T> record : records) {
                    try {
                        ChannelRateConfigBO channelRateLimitConfig = processor.getRateLimitConfig(record.value());
                        limiterManager.getLimiter(channelRateLimitConfig.getRobotKey(),
                                channelRateLimitConfig.getRateMs(),
                                channelRateLimitConfig.getRate()).acquire(1100);
                        log.info("topic: {} got message: {}", processor.getTopic(), record.value());
                        workExecutor.execute(() -> processor.process(record));
                    } catch (LimitException limitException) {
                        log.error("消息限流，message：{}", record.value(), limitException);
                        processor.updatedFail(record);
                    } catch (Exception e) {
                        log.error("消息消费失败，message：{}", record.value(), e);
                    }
                }
            } else {
                // park 1000ms when empty
                LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(1000));
            }
        }
    }
}
