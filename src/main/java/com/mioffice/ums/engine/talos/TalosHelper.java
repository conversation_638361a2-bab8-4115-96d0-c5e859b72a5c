package com.mioffice.ums.engine.talos;

import com.mioffice.ums.engine.limiter.LimiterManager;
import com.xiaomi.infra.galaxy.talos.client.TopicAbnormalCallback;
import com.xiaomi.infra.galaxy.talos.producer.TalosProducer;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.thrift.TopicTalosResourceName;
import libthrift091.TException;

import java.nio.charset.Charset;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/10 22:53
 */
public interface TalosHelper {

    TopicTalosResourceName getTopicResName(String topicName) throws TException;

    TalosProducer createProducer(String topicName, TopicAbnormalCallback topicAbnormalCallback,
                                 UserMessageCallback userMessageCallback) throws TException;

    void shutdownAll();

    void produce(String topicName, byte[] bytes) throws TException;

    void produce(String topicName, String msg, Charset charset) throws TException;

    void produce(String topicName, String msg) throws TException;

    void produce(String topicName, List<String> msgList) throws TException;

    <T> PollConsumer<T> createConsume(MessageProcessor<T> processor, LimiterManager limiterManager);
}
