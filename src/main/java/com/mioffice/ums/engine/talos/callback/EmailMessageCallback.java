package com.mioffice.ums.engine.talos.callback;

import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageResult;
import org.springframework.stereotype.Component;

/**
 * 邮件生产者消息回调类
 *
 * <AUTHOR>
 * @date 2020/8/11 13:52
 */
@Component
public class EmailMessageCallback implements UserMessageCallback {

    @Override
    public void onSuccess(UserMessageResult userMessageResult) {

    }

    @Override
    public void onError(UserMessageResult userMessageResult) {

    }
}
