package com.mioffice.ums.engine.talos.callback;

import com.mioffice.ums.engine.utils.JsonUtils;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageCallback;
import com.xiaomi.infra.galaxy.talos.producer.UserMessageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 系统设置生产者消息回调类
 *
 * <AUTHOR>
 * @date 2020/8/11 13:57
 */
@Slf4j
@Component
public class SettingMessageCallback implements UserMessageCallback {

    @Override
    public void onSuccess(UserMessageResult userMessageResult) {
        log.info("Setting 发送消息成功 MessageCallback UserMessageResult = [{}]", JsonUtils.toJson(userMessageResult));
    }

    @Override
    public void onError(UserMessageResult userMessageResult) {
        log.info("Setting 发送消息失败 MessageCallback UserMessageResult = [{}]", JsonUtils.toJson(userMessageResult));
    }
}
