package com.mioffice.ums.engine.talos.processor;

/**
 * mipush消费者处理器
 *
 * <AUTHOR>
 * @date 2020/8/11 14:09
 */
//@Component
//public class MipushMessageProcessor implements MessageProcessor<String> {
//
//    @Override
//    public void process(ConsumerRecord<String, String> record) {
//
//    }
//
//    @Override
//    public String getTopic() {
//        return TalosConstant.TOPIC_NAME_MI_PUSH;
//    }
//
//    @Override
//    public String getConsumerGroup() {
//        return TalosConstant.TOPIC_NAME_MI_PUSH;
//    }
//
//    @Override
//    public String getClientPrefix() {
//        return TalosConstant.CLIENT_NAME_MI_PUSH;
//    }
//}
