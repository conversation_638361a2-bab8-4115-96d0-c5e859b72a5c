package com.mioffice.ums.engine.talos.processor;

import static com.mioffice.ums.engine.config.MapperConfig.CustomIdGenerator.ENV_POP_ID_NAME;
import com.mioffice.ums.engine.entity.bo.ChannelRateConfigBO;
import com.mioffice.ums.engine.event.SettingEventDispatcher;
import com.mioffice.ums.engine.event.SingleEvent;
import com.mioffice.ums.engine.talos.MessageProcessor;
import com.mioffice.ums.engine.talos.TalosConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

/**
 * 设置消费者处理器
 *
 * <AUTHOR>
 * @date 2020/8/11 14:10
 */
@Slf4j
@Component
public class SettingMessageProcessor implements MessageProcessor<SingleEvent> {

    private final SettingEventDispatcher settingEventDispatcher;

    public SettingMessageProcessor(SettingEventDispatcher settingEventDispatcher) {
        this.settingEventDispatcher = settingEventDispatcher;
    }

    /**
     * {"action": action, "idList": []}
     */
    @Override
    public void process(ConsumerRecord<String, SingleEvent> record) {
        settingEventDispatcher.dispatch(record.value());
    }

    @Override
    public String getTopic() {
        return TalosConstant.TOPIC_NAME_SETTING;
    }

    @Override
    public String getConsumerGroup() {
        String settingGroupName = System.getenv(ENV_POP_ID_NAME);
        if (StringUtils.isBlank(settingGroupName)) {
            settingGroupName = "wangchenhost";
        }
        return settingGroupName;
    }

    @Override
    public String getClientPrefix() {
        return TalosConstant.CLIENT_NAME_SETTING;
    }

    @Override
    public void updatedFail(ConsumerRecord<String, SingleEvent> record) {

    }

    @Override
    public ChannelRateConfigBO getRateLimitConfig(SingleEvent singleEvent) {
        ChannelRateConfigBO channelRateConfigBO = new ChannelRateConfigBO();
        channelRateConfigBO.setRateMs(1000L);
        channelRateConfigBO.setRate(100L);
        channelRateConfigBO.setRobotKey(System.getenv(ENV_POP_ID_NAME));
        return channelRateConfigBO;
    }
}
