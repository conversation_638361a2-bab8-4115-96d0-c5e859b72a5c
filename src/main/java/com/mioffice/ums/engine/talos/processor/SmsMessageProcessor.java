package com.mioffice.ums.engine.talos.processor;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.engine.config.MqProperties;
import com.mioffice.ums.engine.entity.bo.ChannelRateConfigBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.sender.SmsMessageSender;
import com.mioffice.ums.engine.talos.MessageProcessor;
import com.mioffice.ums.engine.talos.TalosConstant;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 短信消费者处理器
 *
 * <AUTHOR>
 * @date 2020/8/11 14:08
 */
@Component
public class SmsMessageProcessor implements MessageProcessor<MessageUserInfo> {

    @Autowired
    private MqProperties mqProperties;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    private final SmsMessageSender smsMessageSender;

    public SmsMessageProcessor(SmsMessageSender smsMessageSender) {
        this.smsMessageSender = smsMessageSender;
    }

    @Override
    public void process(ConsumerRecord<String, MessageUserInfo> record) {
        smsMessageSender.send(record.value());
    }

    @Override
    public String getTopic() {
        return TalosConstant.TOPIC_NAME_SMS;
    }

    @Override
    public String getConsumerGroup() {
        return TalosConstant.TOPIC_NAME_SMS;
    }

    @Override
    public String getClientPrefix() {
        return TalosConstant.CLIENT_NAME_SMS;
    }

    @Override
    public void updatedFail(ConsumerRecord<String, MessageUserInfo> record) {
        MessageUserInfo messageUserInfoInDb =
                messageUserInfoMapper.selectByIdFromMaster(
                        record.value().getId());
        if (Objects.nonNull(messageUserInfoInDb)
                && messageUserInfoInDb.getMessageStatus() != null
                && messageUserInfoInDb.getMessageStatus() == MessageStatusEnum.SENDING.getStatus()) {
            MessageUserInfo update = MessageUserInfo.newUpdateTimeInstant();
            update.setId(messageUserInfoInDb.getId());
            update.setMessageStatus(MessageStatusEnum.SEND_FAIL.getStatus());

            // 增加消息状态变更的判断，处理由于执行中断脚本，导致状态被时序覆盖的问题
            messageUserInfoMapper.update(update, Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getId, messageUserInfoInDb.getId())
                    .ne(MessageUserInfo::getMessageStatus, MessageStatusEnum.SEND_INTERRUPT.getStatus())
            );
        }
    }

    @Override
    public ChannelRateConfigBO getRateLimitConfig(MessageUserInfo messageUserInfo) {
        ChannelRateConfigBO channelRateConfigBO = new ChannelRateConfigBO();
        channelRateConfigBO.setRobotKey(messageUserInfo.getAppId());
        channelRateConfigBO.setRateMs(mqProperties.getRateLimit().getSms().getRateMs());
        channelRateConfigBO.setRate(mqProperties.getRateLimit().getSms().getRate());
        return channelRateConfigBO;
    }
}
