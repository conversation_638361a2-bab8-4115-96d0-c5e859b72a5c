package com.mioffice.ums.engine.task;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadUtil;
import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import com.xiaomi.infra.galaxy.talos.client.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @since 2021.09.22
 */
@Slf4j
@PlanTask(name = "DeleteExpiredMessageTask", quartzCron = "0 0 2 * * ?", description = "删除过期的消息")
public class DeleteExpiredMessageTask implements PlanExecutor {

    private final MessageService messageService;

    private final ExecutorService DEL_EXPIRED_MSG_POOL = ExecutorBuilder.create()
            .setCorePoolSize(1)
            .setMaxPoolSize(1)
            .setKeepAliveTime(0)
            .setThreadFactory(new NamedThreadFactory("del-expired-msg"))
            .buildFinalizable();

    public DeleteExpiredMessageTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {
        String taskData = PlanThreadLocal.getRequestData();
        long timeMills;
        if (StringUtils.isNotBlank(taskData)) {
            timeMills = Long.parseLong(taskData);
        } else {
            timeMills = 0;
        }
        DEL_EXPIRED_MSG_POOL.execute(
                () -> {
                    log.info("删除过期消息任务开始前");
                    messageService.deleteExpiredMessage(timeMills);
                    log.info("删除过期消息任务结束");
                }
        );
    }
}
