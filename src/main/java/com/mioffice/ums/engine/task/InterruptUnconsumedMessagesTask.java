package com.mioffice.ums.engine.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

/**
 * 将对应应用的消息全部从发送中设置为中断。
 */
@Component
@Slf4j
@PlanTask(name = "InterruptUnconsumedMessagesTask", quartzCron = "0 0 2 * * ?", description = "【手动执行】中断没有提交的消息")
public class InterruptUnconsumedMessagesTask implements PlanExecutor {
    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    private static final int BATCH_SIZE = 500;

    @Override
    public void execute() {
        // 1. 获取 appId 并校验
        String sysId = PlanThreadLocal.getRequestData();
        if (!validateAppId(sysId)) {
            throw new IllegalArgumentException("sysId is empty");
        }

        // 2. 批量终止该应用未消费的消息
        interruptUnconsumedMessages(sysId);
    }

    /**
     * 校验 appId 是否有效
     */
    private boolean validateAppId(String sysId) {
        if (sysId == null || sysId.isEmpty()) {
            log.warn("sysId is empty, skip reset task");
            return false;
        }
        return true;
    }

    /**
     * 批量将该应用未消费的消息状态设置为 INTERRUPT
     */
    private void interruptUnconsumedMessages(String sysId) {
        int totalProcessed = 0;
        int batchCount = 0;
        final int MAX_BATCH_COUNT = 1000; // 兜底策略：最多执行1000次批处理
        
        while (batchCount < MAX_BATCH_COUNT) {
            // 每次查询固定数量的未消费消息，不需要offset或游标
            // 因为处理后的数据状态会改变，不再符合查询条件
            LambdaQueryWrapper<MessageUserInfo> query = Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getSysId, sysId)
                    .in(MessageUserInfo::getMessageStatus, MessageStatusEnum.SENDING.getStatus(), MessageStatusEnum.SEND_FAIL.getStatus())
                    .orderByAsc(MessageUserInfo::getId)
                    .last("limit " + BATCH_SIZE);
                    
            List<MessageUserInfo> list = messageUserInfoMapper.selectList(query);
            if (list == null || list.isEmpty()) {
                break; // 没有更多数据，正常退出
            }
            
            // 构造批量更新对象
            List<MessageUserInfo> updateList = list.stream().map(msg -> {
                MessageUserInfo update = new MessageUserInfo();
                update.setId(msg.getId());
                update.setMessageStatus(MessageStatusEnum.SEND_INTERRUPT.getStatus());
                update.setUpdateTime(System.currentTimeMillis());
                return update;
            }).collect(Collectors.toList());
            
            // 执行批量更新
            messageUserInfoMapper.updateBatchSelective(updateList);
            
            batchCount++;
            totalProcessed += updateList.size();
            
            log.info("Batch {}: Set {} messages to INTERRUPT for sysId {}, totalProcessed: {}", 
                    batchCount, updateList.size(), sysId, totalProcessed);
            
            // 睡一会，防止频繁更新db
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(100));
        }
        
        if (batchCount >= MAX_BATCH_COUNT) {
            log.warn("Reached maximum batch count ({}) for sysId {}, may have unconsumed messages left", 
                    MAX_BATCH_COUNT, sysId);
        }
        
        log.info("All unconsumed messages for sysId {} set to INTERRUPT, total processed: {}, batches: {}", 
                sysId, totalProcessed, batchCount);
    }

}
