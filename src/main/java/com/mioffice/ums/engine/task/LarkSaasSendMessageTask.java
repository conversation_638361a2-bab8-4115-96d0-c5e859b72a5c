package com.mioffice.ums.engine.task;

import cn.hutool.core.thread.ExecutorBuilder;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserSaasInfo;
import com.mioffice.ums.engine.enums.ContentFlagEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.LarkSaasRobotManager;
import com.mioffice.ums.engine.manager.LarkSaasV1RobotManager;
import com.mioffice.ums.engine.mapper.MessageUserSaasInfoMapper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.sender.DefaultMessageSender;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import com.xiaomi.infra.galaxy.talos.client.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

@Slf4j
@PlanTask(name = "LarkSaasSendMessageTask", quartzCron = "0 0/1 * * * ?", description = "飞书saas渠道消息发送")
public class LarkSaasSendMessageTask implements PlanExecutor {
    // appid调度线程池设置16， 当前生产环境主渠道待发送应用统计大约20个
    private final ExecutorService appExecutor = ExecutorBuilder.create()
            .setCorePoolSize(16)
            .setMaxPoolSize(16 * 2)
            .setThreadFactory(new NamedThreadFactory("lark-saas-app-executor"))
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .buildFinalizable();

    private static final Integer CORE_POOL_SIZE = 128;

    private final ExecutorService larkSaasExecutor = ExecutorBuilder.create()
            .setCorePoolSize(CORE_POOL_SIZE)
            .setMaxPoolSize(CORE_POOL_SIZE * 2)
            .setWorkQueue(new LinkedBlockingQueue<>(50000))
            .setThreadFactory(new NamedThreadFactory("lark-saas-send"))
            .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
            .buildFinalizable();

    @Autowired
    private MessageUserSaasInfoMapper messageUserSaasInfoMapper;

    @NacosValue(value = "${ums.saas.rate:50}", autoRefreshed = true)
    private Integer rate;

    @NacosValue(value = "${ums.saas.retry.after:1}", autoRefreshed = true)
    private Integer retryAfter;

    @NacosValue(value = "${ums.saas.retry.limit:10}", autoRefreshed = true)
    private Integer retryLimit;

    @NacosValue(value = "${ums.saas.batch.size:1000}", autoRefreshed = true)
    private Integer batchSize;

    @Autowired
    private LarkSaasRobotManager larkSaasRobotManager;

    @Autowired
    private LarkSaasV1RobotManager larkSaasV1RobotManager;

    @Autowired
    private IdDeduplicateManager idDeduplicateManager;

    @Override
    public void execute() {
        long timeMillis = System.currentTimeMillis();
        ArrayList<Byte> sendingStatusList = Lists.newArrayList(MessageStatusEnum.SENDING.getStatus(), MessageStatusEnum.SEND_FAIL.getStatus());

        List<MessageUserSaasInfo> messageUserSaasInfoList = messageUserSaasInfoMapper.selectList(
                Wrappers.<MessageUserSaasInfo>lambdaQuery()
                        .in(MessageUserSaasInfo::getMessageStatus, sendingStatusList)
                        .lt(MessageUserSaasInfo::getUpdateTime, timeMillis)
                        .lt(MessageUserSaasInfo::getRetryCount, retryLimit)
                        .orderByAsc(MessageUserSaasInfo::getRetryCount)
                        .orderByDesc(MessageUserSaasInfo::getCreateTime)
                        .last("limit 50000")
        );

        if (CollectionUtils.isEmpty(messageUserSaasInfoList)) {
            log.warn("messageUserSaasInfoList is empty");
            return;
        }

        log.info("messageUserSaasInfoList size: {}", messageUserSaasInfoList.size());

        List<Callable<Boolean>> tasks = new ArrayList<>();
        messageUserSaasInfoList.stream()
                .collect(Collectors.groupingBy(MessageUserSaasInfo::getAppId, Collectors.toList()))
                .forEach((appid, list) -> {
                    log.info("appid: {} message count : {}", appid, list.size());
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }
                    // 每个应用下结果最多取1000条，尽早让本次定时任务
                    List<MessageUserSaasInfo> sendList = list.subList(0, Math.min(batchSize, list.size()));
                    tasks.add(() -> batchSend(appid, sendList));
                });

        if (CollectionUtils.isEmpty(tasks)) {
            log.warn("appid tasks is empty");
            return;
        }

        try {
            appExecutor.invokeAll(tasks);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private boolean batchSend(String appid, List<MessageUserSaasInfo> list) {
        long start = System.currentTimeMillis();
        // 1. 将投递任务分批次，每批次50条
        Lists.partition(list, 50).forEach(partition -> {
            List<Callable<Boolean>> tasks = new ArrayList<>();
            partition.forEach(message -> tasks.add(() -> sendMessage(message)));
            long startTime = System.currentTimeMillis();
            try {
                // 2. 本批次50条记录全部投递线程池并发执行，并等待全部执行结束
                larkSaasExecutor.invokeAll(tasks);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            long elapsedTime = System.currentTimeMillis() - startTime;
            // 间隔时间
            long parkTime = 1000 - elapsedTime;
            // 3. 计算本批次投递执行时间，如果没超过1s，则等待剩余时间，以免下一批次请求飞书限流
            if (parkTime > 0) {
                LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(parkTime));
            }
        });
        log.info("batch send app = [{}] cost time:{}", appid, System.currentTimeMillis() - start);

        return true;
    }

    private boolean sendMessage(MessageUserSaasInfo messageUserSaasInfo) {
        long start = System.currentTimeMillis();
        // 发送前检查，避免重复发送
        if (!check(messageUserSaasInfo)) {
            return true;
        }

        MiWorkRobot.Session session = new MiWorkRobot.Session();
        session.setUserId(messageUserSaasInfo.getUsername());
        session.setEmail(messageUserSaasInfo.getEmail());
        session.setOpenChatId(messageUserSaasInfo.getChatId());

        boolean result = true;
        try {
            MiWorkResponseBO miWorkResponseBO = messageUserSaasInfo.getIsV1Channel() == 1
                    ? larkSaasV1RobotManager.sendMsg(messageUserSaasInfo.getAppId(), messageUserSaasInfo, session)
                    : larkSaasRobotManager.sendMsg(messageUserSaasInfo.getAppId(), messageUserSaasInfo, session);

            messageUserSaasInfo.setUpdateTime(System.currentTimeMillis());
            messageUserSaasInfo.setMessageId(miWorkResponseBO.getMsgId());
            messageUserSaasInfo.setMessageStatus(MessageStatusEnum.SEND_SUCCESS.getStatus());
            if (messageUserSaasInfo.getContentFlag() == ContentFlagEnum.LARK_CONTENT.getFlag()) {
                log.info("FinalContent = [{}]", miWorkResponseBO.getMessageJson());
                messageUserSaasInfo.setFinalContent(miWorkResponseBO.getMessageJson());
            }
            messageUserSaasInfoMapper.updateById(messageUserSaasInfo);
            idDeduplicateManager.add(MessageChannelEnum.MI_WORK_SAAS, messageUserSaasInfo.getId());
            log.info("saas消息发送成功 messageId = [{}], extraId = [{}]", messageUserSaasInfo.getId(),
                    messageUserSaasInfo.getExtraId());
        } catch (Throwable e) {
            log.error("saas消息发送失败 messageId = [{}], extraId = [{}]",
                    messageUserSaasInfo.getId(),
                    messageUserSaasInfo.getExtraId(), e);
            result = false;
            updateSendFail(messageUserSaasInfo, e);
        }

        log.info("send message = [{}] cost time:{}", messageUserSaasInfo.getId(), System.currentTimeMillis() - start);
        return result;
    }

    protected void updateSendFail(MessageUserSaasInfo messageUserSaasInfo, Throwable e) {
        messageUserSaasInfo.setUpdateTime(System.currentTimeMillis());
        messageUserSaasInfo.setMessageStatus(MessageStatusEnum.SEND_FAIL.getStatus());
        messageUserSaasInfo.setErrorLog(Optional.of(e.getMessage()).orElse(StringUtils.EMPTY));
        messageUserSaasInfo.setRetryCount(messageUserSaasInfo.getRetryCount() + 1);
        // 解析错误码
        DefaultMessageSender.errType(e, MessageChannelEnum.get(messageUserSaasInfo.getChannel()), messageUserSaasInfo);
        messageUserSaasInfoMapper.updateById(messageUserSaasInfo);
    }

    protected boolean check(MessageUserSaasInfo messageUserSaasInfo) {
        // 去重判断
        if (idDeduplicateManager.contains(MessageChannelEnum.MI_WORK_SAAS, messageUserSaasInfo.getId())) {
            log.info("saas消息重复，丢弃发送 id = {},extraId = {}", messageUserSaasInfo.getId(), messageUserSaasInfo.getExtraId());
            return false;
        }
        return true;
    }
}
