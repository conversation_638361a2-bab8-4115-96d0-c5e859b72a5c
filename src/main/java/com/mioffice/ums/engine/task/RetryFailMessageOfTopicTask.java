package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName RetryFailMessageOfTopicTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/1 16:14
 **/

@Slf4j
@PlanTask(name = "RetryFailMessageOfTopicTask", quartzCron = "0 0/1 * * * ?", description = "重试专属队列发送失败的消息")
public class RetryFailMessageOfTopicTask implements PlanExecutor {
    private final MessageService messageService;

    public RetryFailMessageOfTopicTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {
        messageService.retrySendFailMsgOfTopic();
    }
}
