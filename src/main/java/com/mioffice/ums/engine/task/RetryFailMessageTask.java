package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.11
 */
@Slf4j
@PlanTask(name = "RetryFailMessageTask", quartzCron = "0 0/5 * * * ?", description = "重试发送失败的消息(无专属队列)")
public class RetryFailMessageTask implements PlanExecutor {

    private final MessageService messageService;

    public RetryFailMessageTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {
        messageService.retrySendFailMsg();
    }
}
