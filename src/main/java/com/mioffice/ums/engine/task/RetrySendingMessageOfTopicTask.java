package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName RetrySendingMessageOfTopicTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/1 16:45
 **/

@Slf4j
@PlanTask(name = "RetrySendingMessageOfTopicTask", quartzCron = "0 0/1 * * * ?", description = "重试专属队列发送中的消息")
public class RetrySendingMessageOfTopicTask implements PlanExecutor {

    private final MessageService messageService;

    public RetrySendingMessageOfTopicTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {
        messageService.retrySendingMsgOfTopic();
    }
}
