package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName RetrySendingMessageOfTopicTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/1 16:45
 **/

@Slf4j
@PlanTask(name = "RetrySendingMessageOfTopicTask", quartzCron = "0 0/1 * * * ?", description = "【手动执行】重试专属队列发送中的消息")
public class RetrySendingMessageOfTopicTask implements PlanExecutor {

    private final MessageService messageService;

    public RetrySendingMessageOfTopicTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {

        // 设置要重试什么时间的间隔
        String retryTime = PlanThreadLocal.getRequestData();
        Long time = null;
        try {
            time = Long.parseLong(retryTime);
        } catch (Exception e) {
            log.warn("重试时间格式错误, 请检查配置, 默认使用Nacos配置");
        }

        if (time == null) {
            throw new IllegalArgumentException("重试时间不能为空");
        }

        messageService.retrySendingMsgOfTopic(time);
    }
}
