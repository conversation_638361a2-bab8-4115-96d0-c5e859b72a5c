package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.11
 */
@Slf4j
@PlanTask(name = "RetrySendingMessageTask", quartzCron = "0 0/3 * * * ?", description = "【手动执行】重试发送中的消息(无专属队列)")
public class RetrySendingMessageTask implements PlanExecutor {

    private final MessageService messageService;

    public RetrySendingMessageTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {

        // 设置要重试什么时间的间隔
        String retryTime = PlanThreadLocal.getRequestData();
        Long time = null;
        try {
            time = Long.parseLong(retryTime);
        } catch (Exception e) {
            log.warn("重试时间格式错误, 请检查配置, 默认使用Nacos配置");
        }

        if (time == null) {
            throw new IllegalArgumentException("重试时间不能为空");
        }

        messageService.retrySendingMsg(time);
    }
}
