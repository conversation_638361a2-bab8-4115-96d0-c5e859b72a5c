package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 重试统一Topic失败消息定时任务
 * </p>
 *
 * <AUTHOR>
 * @date 2024.12.19
 */
@Slf4j
@PlanTask(name = "RetryUnifiedTopicFailMessageTask", quartzCron = "0 0/5 * * * ?", description = "重试统一Topic发送失败的消息")
public class RetryUnifiedTopicFailMessageTask implements PlanExecutor {

    private final MessageService messageService;

    public RetryUnifiedTopicFailMessageTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {
        messageService.retryUnifiedTopicFailedMessages();
    }
} 