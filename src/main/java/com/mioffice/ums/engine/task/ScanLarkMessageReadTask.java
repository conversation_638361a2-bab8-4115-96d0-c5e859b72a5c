package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.plan.PlanThreadLocal;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName ScanLarkMessageReadTask
 * @Description 扫描消息已读
 * <AUTHOR>
 * @Date 2023/6/5 17:22
 **/

@Slf4j
@PlanTask(name = "ScanLarkMessageReadTask", quartzCron = "0 0/8 * * * ?", description = "扫描&统计消息已读情况", shardStrategy
        = 1)
public class ScanLarkMessageReadTask implements PlanExecutor {
    private final MessageService messageService;

    public ScanLarkMessageReadTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {
        Integer shardIndex = PlanThreadLocal.getShardIndex();
        Integer shardTotal = PlanThreadLocal.getShardTotal();
        messageService.scanMessageRead(shardIndex, shardTotal);
    }
}
