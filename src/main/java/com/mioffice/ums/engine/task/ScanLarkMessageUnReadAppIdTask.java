package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.cloud.plan.client.plan.PlanExecutor;
import com.xiaomi.cloud.plan.client.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName ScanLarkMessageUnReadAppIdTask
 * @Description 扫描未读消息的app_id
 * <AUTHOR>
 * @Date 2023/8/16 19:26
 **/

@Slf4j
@PlanTask(name = "ScanLarkMessageUnReadAppIdTask", quartzCron = "0 0/5 * * * ?", description = "扫描未读消息的app_id")
public class ScanLarkMessageUnReadAppIdTask implements PlanExecutor {

    private final MessageService messageService;

    public ScanLarkMessageUnReadAppIdTask(MessageService messageService) {
        this.messageService = messageService;
    }

    @Override
    public void execute() {
        messageService.scanUnReadAppId();
    }
}
