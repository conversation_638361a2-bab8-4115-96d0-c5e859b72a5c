package com.mioffice.ums.engine.template;

import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.enums.I18nEnum;
import freemarker.cache.TemplateLoader;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.util.Objects;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.08
 */
public class CacheTemplateLoader implements TemplateLoader {

    private static final int DEFAULT_MAXIMUM_SIZE = 100;

    private TemplateCache templateCache;

    public CacheTemplateLoader(TemplateCache templateCache) {
        this.templateCache = templateCache;
    }


    public MessageTemplateInfo get(Long tmpId) {
        return templateCache.get(tmpId);
    }


    @Override
    public Object findTemplateSource(String tmpId) throws IOException {

        String[] strings = tmpId.split("-");
        String type = strings[0];
        String id = strings[1];
        String i18n = null;
        if (strings.length > 2) {
            i18n = strings[2];
        }

        MessageTemplateInfo messageTemplateInfo = get(Long.valueOf(id));
        if (Objects.isNull(messageTemplateInfo)) {
            return null;
        }

        if (TemplateType.CONTENT.getType().equals(type)) {
            return StringUtils.isNotBlank(i18n) ? messageTemplateInfo.getI18nContent(I18nEnum.valueOf(i18n)) : messageTemplateInfo.getContentCn();
        } else if (TemplateType.ACTION.getType().equals(type)) {
            String cardAction = StringUtils.isNotBlank(i18n) ? messageTemplateInfo.getCardActions(I18nEnum.valueOf(i18n)) : messageTemplateInfo.getCardActions();
            if (StringUtils.isBlank(cardAction)) {
                // 兼容老字段
                return messageTemplateInfo.getCardActions();
            }
            return cardAction;
        } else if (TemplateType.TITLE.getType().equals(type)) {
            return StringUtils.isNotBlank(i18n) ? messageTemplateInfo.getI18nTitle(I18nEnum.valueOf(i18n)) : messageTemplateInfo.getTitleCn();
        }

        return null;
    }

    @Override
    public long getLastModified(Object templateSource) {
        return System.currentTimeMillis();
    }

    @Override
    public Reader getReader(Object templateSource, String encoding) throws IOException {
        return new StringReader((String) templateSource);
    }

    @Override
    public void closeTemplateSource(Object templateSource) throws IOException {

    }
}
