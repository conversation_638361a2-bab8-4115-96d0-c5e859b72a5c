package com.mioffice.ums.engine.template;

import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.config.LarkSaasMapConfig;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.08
 */
@Slf4j
@Component
public class LarkSaasTemplateParse extends TemplateParse {
    @Autowired
    private LarkSaasMapConfig larkSaasMapConfig;

    public LarkSaasTemplateParse(TemplateCache templateCache) {
        super(templateCache);
    }

    protected String process(Template template, Map<String, Object> params) throws IOException, TemplateException {
        String process = super.process(template, params);
        if (larkSaasMapConfig == null) {
            return process;
        }

        if (larkSaasMapConfig.getAppIdMap() != null) {
            Pattern appIdPattern = Pattern.compile("cli_[a-z0-9]{16}");
            process = replace(process, larkSaasMapConfig.getAppIdMap(), appIdPattern);
        }
        if (larkSaasMapConfig.getLinkMap() != null) {
            for (String link : larkSaasMapConfig.getLinkMap().keySet()) {
                process = process.replace(link, larkSaasMapConfig.getLinkMap().get(link));
            }
        }
        if (larkSaasMapConfig.getImgKeyMap() != null) {
            Pattern imgKeyPattern = Pattern.compile("img_(?:v\\d+_)?(?:\\d+[a-zA-Z0-9-]+_)?[a-zA-Z0-9-]+");
            process = replace(process, larkSaasMapConfig.getImgKeyMap(), imgKeyPattern);
        }
        return process;
    }

    private String replace(String original, Map<String, String> mapConfig, Pattern pattern) {
        Matcher matcher = pattern.matcher(original);
        Set<String> matchSet = new HashSet<>();
        while (matcher.find()) {
            if (matchSet.contains(matcher.group())) {
                continue;
            }
            String mapVal = mapConfig.get(matcher.group());
            if (mapVal != null) {
                original = original.replace(matcher.group(), mapVal);
            }
            matchSet.add(matcher.group());
        }

        return original;
    }
}
