package com.mioffice.ums.engine.template;

import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.enums.I18nEnum;
import com.mioffice.ums.engine.mapper.MessageTemplateInfoMapper;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import freemarker.template.Configuration;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.08
 */
@Slf4j
@Component
public class TemplateParse {

    private Configuration configuration;

    private final CacheTemplateLoader cacheTemplateLoader;

    public TemplateParse(TemplateCache templateCache) {
        this.cacheTemplateLoader = new CacheTemplateLoader(templateCache);
    }

    @PostConstruct
    public void init() {
        configuration = new Configuration(Configuration.VERSION_2_3_28);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setLocalizedLookup(false);
        configuration.setTemplateLoader(cacheTemplateLoader);
    }

    public String parseTitle(Long tmpId, Map<String, Object> params) throws Exception {
        Template template = configuration.getTemplate(TemplateType.TITLE.getType().concat("-").concat(String.valueOf(tmpId)));
        return this.process(template, params);
    }

    public String parseContent(Long tmpId, Map<String, Object> params) throws Exception {
        Template template = configuration.getTemplate(TemplateType.CONTENT.getType().concat("-").concat(String.valueOf(tmpId)));
        return this.process(template, params);
    }

    public String parseAction(Long tmpId, Map<String, Object> params) throws Exception {
        Template template = configuration.getTemplate(TemplateType.ACTION.getType().concat("-").concat(String.valueOf(tmpId)));
        return this.process(template, params);
    }

    public String parseTitle(Long tmpId, Map<String, Object> params, I18nEnum i18nEnum) throws Exception {
        Template template = configuration.getTemplate(TemplateType.TITLE.getType().concat("-").concat(String.valueOf(tmpId)).concat("-").concat(i18nEnum.name()));
        return this.process(template, params);
    }

    public String parseContent(Long tmpId, Map<String, Object> params, I18nEnum i18nEnum) throws Exception {
        Template template = configuration.getTemplate(TemplateType.CONTENT.getType().concat("-").concat(String.valueOf(tmpId)).concat("-").concat(i18nEnum.name()));
        return this.process(template, params);
    }

    public String parseAction(Long tmpId, Map<String, Object> params, I18nEnum i18nEnum) throws Exception {
        Template template = configuration.getTemplate(TemplateType.ACTION.getType().concat("-").concat(String.valueOf(tmpId)).concat("-").concat(i18nEnum.name()));
        return this.process(template, params);
    }

    protected String process(Template template, Map<String, Object> params) throws IOException, TemplateException {
        StringWriter stringWriter = new StringWriter();
        template.process(params, stringWriter);
        return stringWriter.toString();
    }


    public MessageTemplateInfo get(Long tmpId) {
        return cacheTemplateLoader.get(tmpId);
    }

    @Data
    @AllArgsConstructor
    public static class TmpContent {
        private String title;
        private String content;
    }

}
