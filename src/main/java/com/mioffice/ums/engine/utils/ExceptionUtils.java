package com.mioffice.ums.engine.utils;

import com.mioffice.ums.engine.common.CheckedFunction;

import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.10.19
 */
public class ExceptionUtils {

    public static <T, R> Function<T, R> uncheck(CheckedFunction<T, R> function) {


        return param -> {
            try {
                return function.apply(param);
            } catch (Throwable e) {
                throw new RuntimeException(e);
            }
        };
    }
}
