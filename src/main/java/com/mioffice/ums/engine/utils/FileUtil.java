package com.mioffice.ums.engine.utils;

import java.util.UUID;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.07.01
 */
public class FileUtil {

    public static String getRandomName(String fileName) {
        return UUID.randomUUID().toString() + getFileExt(fileName);
    }

    public static String getFileExt(String fileName) {
        return fileName.substring(fileName.lastIndexOf("."));
    }

    public static String parseFileId(String url) {
        return url.substring(url.lastIndexOf("/") + 1, url.lastIndexOf("."));
    }

    private FileUtil() {
    }
}
