package com.mioffice.ums.engine.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.List;

/**
 * IpUtil
 *
 * <AUTHOR>
 * @Date 2019/6/2 15:20
 */
@Slf4j
public class IpUtil {

    private IpUtil() {
    }

    private static final String LOCAL_IP = "127.0.0.1";


    /**
     * 获得本地的ip地址
     *
     * @return
     */
    public static List<String> getLocalIPList() {
        List<String> ipList = Lists.newArrayList();
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            NetworkInterface networkInterface;
            Enumeration<InetAddress> inetAddresses;
            InetAddress inetAddress;
            String ip;
            while (networkInterfaces.hasMoreElements()) {
                networkInterface = networkInterfaces.nextElement();
                inetAddresses = networkInterface.getInetAddresses();
                while (inetAddresses.hasMoreElements()) {
                    inetAddress = inetAddresses.nextElement();
                    // IPV4
                    if (inetAddress != null && inetAddress instanceof Inet4Address) {
                        ip = inetAddress.getHostAddress();
                        ipList.add(ip);
                    }
                }
            }
        } catch (SocketException e) {
            log.error("getLocalIPList Exception", e);
        }
        ipList.remove(LOCAL_IP);
        return ipList;
    }
}
