package com.mioffice.ums.engine.utils;

import com.fasterxml.jackson.databind.type.SimpleType;
import com.google.gson.*;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import lombok.Data;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.12
 */
@SuppressWarnings("all")
public class JsonUtils {

    private static final Gson GSON;

    private static JsonSerializer<Number> numberJsonSerializer = new JsonSerializer<Number>() {
        @Override
        public JsonElement serialize(Number src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(String.valueOf(src));
        }
    };

    static {
        GsonBuilder gsonBuilder = new GsonBuilder();
        GSON = gsonBuilder.create();
    }

    public static String toJson(Object object) {
        return GSON.toJson(object);
    }

    public static <T> T parse(String json, Class<T> cls) {
        return GSON.fromJson(json, cls);
    }

    public static <T> T parse(String json, Type cls) {
        return GSON.fromJson(json, cls);
    }

    public static List<Map> toListMap(String json) {
        return GSON.fromJson(json, new TypeToken<List<Map>>() {
        }.getType());
    }

    public static <T> Map<String, T> toMap(String gsonString) {
        return GSON.fromJson(gsonString, new TypeToken<Map<String, T>>() {
        }.getType());
    }


//    public static class MapDeserializerDoubleAsIntFix implements JsonDeserializer<Map<String, Object>> {
//        @Override
//        public Map<String, Object> deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
//            return (Map<String, Object>) read(jsonElement);
//        }
//    }
//
//    public static class ListDeserializerDoubleAsIntFix implements JsonDeserializer<List<Object>> {
//        @Override
//        public List deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
//            return (List<Object>) read(jsonElement);
//        }
//    }

    private static Object read(JsonElement in) {
        if (in.isJsonArray()) {
            List<Object> list = new ArrayList<>();
            JsonArray arr = in.getAsJsonArray();
            for (JsonElement anArr : arr) {
                list.add(read(anArr));
            }
            return list;
        } else if (in.isJsonObject()) {
            Map<String, Object> map = new LinkedTreeMap<>();
            JsonObject obj = in.getAsJsonObject();
            Set<Map.Entry<String, JsonElement>> entitySet = obj.entrySet();
            for (Map.Entry<String, JsonElement> entry : entitySet) {
                map.put(entry.getKey(), read(entry.getValue()));
            }
            return map;
        } else if (in.isJsonPrimitive()) {
            JsonPrimitive prim = in.getAsJsonPrimitive();
            if (prim.isBoolean()) {
                return prim.getAsBoolean();
            } else if (prim.isString()) {
                return prim.getAsString();
            } else if (prim.isNumber()) {
                Number num = prim.getAsNumber();
                if (Math.ceil(num.doubleValue()) == num.longValue()) {
                    return num.longValue();
                } else {
                    return num.doubleValue();
                }
            }
        }
        return null;
    }

    public static void main(String[] args) {
        String ss = "{\"result\":\"OK\",\"description\":\"成功\",\"code\":0}";
        Map<String, Object> toMap = JsonUtils.toMap(ss);
        System.out.println(toMap);
    }

}
