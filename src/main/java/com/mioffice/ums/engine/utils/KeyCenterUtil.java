package com.mioffice.ums.engine.utils;

import com.xiaomi.keycenter.KeycenterHelper;
import com.xiaomi.keycenter.agent.client.DataProtectionProvider;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import shade.org.apache.commons.codec.binary.Base64;

import java.nio.charset.StandardCharsets;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * description:
 * date: 2020/8/5 5:21 下午
 * version: 1.0.0
 */
public class KeyCenterUtil {

    static {
        KeycenterHelper.config("keycenter-ums-engine");
    }

    private KeyCenterUtil() {

    }

    private static final DataProtectionProvider provider = DataProtectionProvider.getProvider("keycenter-ums-engine");

    public static String encrypt(String encryptString) throws DataProtectionException {
        byte[] encrypted = provider.encrypt(encryptString.getBytes(StandardCharsets.UTF_8), null, false);
        return Base64.encodeBase64String(encrypted);
    }

    public static String decrypt(String decryptString) throws DataProtectionException {
        byte[] decrypted = provider.decrypt(Base64.decodeBase64(decryptString), null, false);
        return new String(decrypted, StandardCharsets.UTF_8);
    }
}
