package com.mioffice.ums.engine.utils;

import com.mioffice.ums.engine.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.engine.entity.bo.AvgCostTimeBO;
import com.mioffice.ums.engine.entity.bo.ErrorTypeBo;
import com.mioffice.ums.engine.entity.bo.MessageGroupDeptRecordBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.ErrorType;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageAvgCostTime;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageGroupDeptResultRecord;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageRecord;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import com.xiaomi.info.pb.app.mioffice.ums.open.v1.AppTopicInfo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * proto 实体类转换工具
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.26
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MapperUtil {

    MapperUtil INSTANCE = Mappers.getMapper(MapperUtil.class);

    /**
     * MessageUser -> MessageUserInfo
     *
     * @param user
     * @return
     */
    MessageUserInfo mapToMessageUserInfo(MessageUser user);

    /**
     * MessageUserInfo -> MessageUser
     *
     * @param userInfo
     * @return
     */
    MessageUser mapToMessageUser(MessageUserInfo userInfo);

    /**
     * MessageUserInfo -> MessageRecord
     *
     * @param messageUserInfo
     * @return
     */
    MessageRecord mapToMessageRecord(MessageUserInfo messageUserInfo);

    /**
     * MessageGroupDeptRecordBO -> MessageGroupDeptResultRecord
     *
     * @param messageGroupDeptRecordBO
     * @return
     */
    MessageGroupDeptResultRecord mapToMessageGroupDeptResultRecord(MessageGroupDeptRecordBO messageGroupDeptRecordBO);

    /**
     * ErrorTypeBo -> ErrorType
     *
     * @param errorTypeBo
     * @return
     */
    ErrorType mapToErrorType(ErrorTypeBo errorTypeBo);

    /**
     * AvgCostTimeBO mapTo MessageAvgCostTime
     *
     * @param avgCostTimeBO
     * @return MessageAvgCostTime
     */
    List<MessageAvgCostTime> mapToMessageAvgCostTime(List<AvgCostTimeBO> avgCostTimeBO);

    List<AppTopicDetailBO> map2AppTopicDetailBOList(List<AppTopicInfo> appTopicInfoList);

    AppTopicDetailBO map2AppTopicDetailBO(AppTopicInfo appTopicInfo);
}
