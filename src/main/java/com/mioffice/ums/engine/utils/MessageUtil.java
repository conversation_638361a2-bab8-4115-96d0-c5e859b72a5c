package com.mioffice.ums.engine.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.larksuite.appframework.sdk.utils.JsonUtil;
import static com.mioffice.ums.engine.utils.MessageUtil.MessageTextRegex.FEISHU_400_ERROR_PREFIX;
import static com.mioffice.ums.engine.utils.MessageUtil.MessageTextRegex.FEISHU_500_ERROR_PREFIX;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 * 消息工具类
 * </p>
 *
 * <AUTHOR>
 * @date 2020.07.02
 */
public class MessageUtil {

    public static class MessageTextRegex {
        /**
         * 图片消息提取
         */
        public static final String IMAGE_MD_REGEX = "!\\[.*?\\]\\(.*?\\)";
        /**
         * 小米办公消息落地页地址
         */
        public static final String LANDING_PAGE_URL_KEY = "landingPageUrl";

        public static final String FEISHU_400_ERROR_PREFIX = "code :9001, response http code 400 , ";

        public static final String FEISHU_500_ERROR_PREFIX = "code :9001, response http code 500 , ";

        private MessageTextRegex() {
        }
    }

    private MessageUtil() {
    }

    /**
     * 图片的 前后单独加 \n\r
     *
     * @param content
     * @return
     */
    public static String imageAddLine(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }

        content = content.trim();
        Pattern pattern = Pattern.compile(MessageUtil.MessageTextRegex.IMAGE_MD_REGEX);
        Matcher matcher = pattern.matcher(content);

        while (matcher.find()) {
            String group = matcher.group();
            content = content.replace(group, "\n\r" + group + "\n\r");
        }

        return content;
    }

    public static List<String> parseMdForImageUrl(String line) {
        List<String> strings = RegexUtil.find(MessageTextRegex.IMAGE_MD_REGEX, line);
        List<String> results = new ArrayList<>();
        for (String string : strings) {
            results.add(string.substring(string.lastIndexOf("(") + 1, string.lastIndexOf(")")).trim());
        }
        return results;
    }

    /**
     * 将  ${name} 变量替换成对应的 值
     *
     * @param content
     * @param data
     * @return
     */
    public static String placeholderText(String content, Map<String, Object> data) {
        if (Objects.isNull(data)) {
            return content;
        }
        StringSubstitutor sub = new StringSubstitutor(data);
        return sub.replace(content);
    }

    /**
     * 解析错误码
     * <p>
     * code :11105, user_id not exist
     *
     * @param errMsg
     * @return
     */
    public static String parseErrorCode(String errMsg) {
        List<String> codes = RegexUtil.find("code :\\d+", errMsg);
        if (codes.isEmpty()) {
            return null;
        }

        return RegexUtil.find("\\d+", codes.get(0)).get(0).trim();
    }

    public static boolean templateError(String errMsg) {
        return errMsg.startsWith("Unexpected character") || errMsg.startsWith("Unrecognized character escape");
    }

    public static ObjectNode feishu400ErrorResponseJson(String errMsg) {
        if (errMsg.startsWith(FEISHU_400_ERROR_PREFIX)) {
            String jsonErrMsgStr = errMsg.replace(FEISHU_400_ERROR_PREFIX, StringUtils.EMPTY);
            try {
                return JsonUtil.larkFormatToObject(jsonErrMsgStr, new TypeReference<ObjectNode>() {
                });
            } catch (Throwable ex) {

            }
        }
        return null;
    }

    public static ObjectNode feishu500ErrorResponseJson(String errMsg){
        if (errMsg.startsWith(FEISHU_500_ERROR_PREFIX)) {
            String jsonErrMsgStr = errMsg.replace(FEISHU_500_ERROR_PREFIX, StringUtils.EMPTY);
            try {
                return JsonUtil.larkFormatToObject(jsonErrMsgStr, new TypeReference<ObjectNode>() {
                });
            } catch (Throwable ex) {

            }
        }
        return null;
    }
}


