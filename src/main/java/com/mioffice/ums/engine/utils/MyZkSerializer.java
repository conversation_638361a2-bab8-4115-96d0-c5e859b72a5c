package com.mioffice.ums.engine.utils;

import org.I0Itec.zkclient.exception.ZkMarshallingError;
import org.I0Itec.zkclient.serialize.ZkSerializer;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.serializer.support.DeserializingConverter;
import org.springframework.core.serializer.support.SerializingConverter;

/**
 * MyZkSerializer
 *
 * <AUTHOR>
 * @Date 2019/10/1 18:06
 */
public class MyZkSerializer implements ZkSerializer {

    private Converter<Object, byte[]> serializingConverter = new SerializingConverter();
    private Converter<byte[], Object> deserializingConverter = new DeserializingConverter();

    @Override
    public byte[] serialize(Object data) throws ZkMarshallingError {
        return serializingConverter.convert(data);
    }

    @Override
    public Object deserialize(byte[] bytes) throws ZkMarshallingError {
        return deserializingConverter.convert(bytes);
    }
}
