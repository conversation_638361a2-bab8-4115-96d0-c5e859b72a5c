package com.mioffice.ums.engine.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.07.02
 */
public class RegexUtil {

    private RegexUtil() {
    }

    public static List<String> find(String regex, String content) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content.trim());

        List<String> list = new ArrayList<>();
        while (matcher.find()) {
            list.add(matcher.group());
        }

        return list;
    }

    /**
     * 该字符串是否完成匹配改正则
     *
     * @param regex
     * @param content
     * @return
     */
    public static boolean hasFullText(String regex, String content) {
        return Pattern.matches(regex, content);
    }
}
