server:
  port: 8080

management:
  server:
    port: 8080
  endpoints:
    web:
      exposure:
        include: "health"
      base-path: /debug
  health:
    redis:
      enabled: false

spring:
  application:
    name: ums-engine
  datasource:
    master:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.jdbc.Driver
      jdbcUrl: *********************************************************************************************
      username: ums_engine_wn
      password@kc-sid: oa-infra.g
      password: GDDg5MPLquS51BeXna87eI0jo+jylirJL34bQgJyfpHSwyGguz1tYjrKE4hqV5PWec0YEnK8lFJnuk6zsLCshFBBdz2J/xgQgaNqzgfiT1aqMcw5BUL/nxgUDEVhX2VWOahjMe7fIY5Jk4ieUfYA
      # hikari 的相关配置
      continue-on-error: true
      maximum-pool-size: 500
      minimum-idle: 50
      auto-commit: true
      connection-test-query: SELECT 1
      connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci
    slave:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.jdbc.Driver
      jdbcUrl: *********************************************************************************************
      username: ums_engine_wn
      password@kc-sid: oa-infra.g
      password: GDDg5MPLquS51BeXna87eI0jo+jylirJL34bQgJyfpHSwyGguz1tYjrKE4hqV5PWec0YEnK8lFJnuk6zsLCshFBBdz2J/xgQgaNqzgfiT1aqMcw5BUL/nxgUDEVhX2VWOahjMe7fIY5Jk4ieUfYA
      # hikari 的相关配置
      continue-on-error: true
      maximum-pool-size: 500
      minimum-idle: 50
      auto-commit: true
      connection-test-query: SELECT 1
      connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci
  redis:
    redisson:
      password@kc-sid: oa-infra.g
      password: GGBkO3/xei1dRWYI12FjVF0KHzd2EzSSoREU0kGJdTZJE8871QPN7CFONJHx9nX8gZoUmNhfANlSgeIGCHGSW0Ol9ERkC/l1rB9nZI7LYeejFxeD4sQiDCrjvFjewvrk68kYElmJ5pa9uEzWiP0a2I/x6zWC/xgQcWWnaaQOQC2+/TiuE12xNBgUAIPjljFqhIu6h1yibOvWCWzOrssA
      config: |
        singleServerConfig:
          database: 0
          address: redis://wcc.cache01.test.b2c.srv:22122
          password: ${spring.redis.redisson.password}
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 10000
          retryAttempts: 3
          retryInterval: 1500
          connectionMinimumIdleSize: 24
          connectionPoolSize: 64
          dnsMonitoringInterval: 5000
          pingConnectionInterval: 30000
  mi:
    plan:
      env: test   #TEST/PROD
      project-uid: 4564c618841b4f4fa7cd2f8f72d19349   #就是上面生成的uid
      auto-startup: true    #默认为false:表示不启动
      cuntry-domain: cn

logging.level.com.xiaomi.infra.galaxy.talos.consumer: OFF
logging.level.com.xiaomi.infra.galaxy.talos.producer: OFF
logging.level.com.xiaomi.xms.plans.client: OFF

mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  global-config:
    db-config:
      id-type: auto   # 可以拿到返回的主键id


# grpc
mrpc:
  server:
    # 此处打开了 auto-register 可以被服务发现，如果是本地服务可以将其关闭
    auto-register: true
    server-group: info-application # MIS 上的服务组
    server-name: ums_engine # MIS 上注册的服务名
    registries: http://etcd.test.mi.com
  client:
    server-name: ums_engine
    server-group: info-application
    category:
      ums-open-app-list-server:
        server-name: ums_open_api
        use-soa: true


# talos
galaxy:
  talos:
    client:
      access-key: AKYPGIP74Z2XEXAZC4
      acess-secret@kc-sid: oa-infra.g
      acess-secret: GDAkQVjXDLZoLpzbE2Lfu2B5WvC5SPTfGC+RpsUWCesu9gkMUcArJqYJ6NTcQFbtDWkYEnYK1bT9qUaniV0OXSAkgo61/xgQQpkDnPdfRfmBKapsW27wDRgUWYdeKjcqq8igdA+uH+LNK2F/ldEA
    service:
      endpoint: http://staging-cnbj2-talos.api.xiaomi.net

# zk
zk:
  server: tjwqstaging.zk.hadoop.srv:2181
  connection-timeout-ms: 30000
  session-timeout-ms: 30000

ums:
  engine:
    sms:
      env: dev
    # 重试配置
    retry:
      # 重试次数限制
      limit: 50
      # 发送中消息重试间隔（分钟）
      sending-after: 5
      # 失败消息重试间隔（分钟）
      failed-after: 1

    # 统一Topic配置
    unified-topic:
      # 是否启用统一Topic功能
      enabled: true
      # 统一Topic名称
      name: "ums-unified-topic"
      # 使用统一Topic的系统ID列表
      system-ids:
        - "system1"
        - "system2"
        - "system3"
      # Topic延迟最小秒数
      delay-min-seconds: 1
      # Topic延迟最大秒数
      delay-max-seconds: 10
    rate-limit:
      # 邮件限流配置
      email:
        # 邮件限流时间窗口（毫秒）
        rate-ms: 1000
        # 邮件限流速率（每个时间窗口内的最大请求数）
        rate: 10
      
      # 短信限流配置
      sms:
        # 短信限流时间窗口（毫秒）
        rate-ms: 1000
        # 短信限流速率（每个时间窗口内的最大请求数）
        rate: 10
      
      # 飞书限流配置
      miwork:
        # 飞书限流时间窗口（毫秒）
        rate-ms: 1000
        # 飞书限流速率（每个时间窗口内的最大请求数）
        rate: 50
        # 飞书秒级限流（每秒最大请求数）
        second-limit: 50
        # 飞书分钟级限流（每分钟最大请求数）
        minute-limit: 1000
        # 飞书API升级配置
    lark-api-upgrade:
      # 是否启用白名单
      enable-white-list: false
      # 白名单列表
      white-list:
        - "app-id-1"
        - "app-id-2"
        - "app-id-3"
        # 专属队列配置
    exclusive-queue:
      # 不走专属队列的机器人列表
      exclude-bots:
        - "bot-1"
        - "bot-2"
        - "bot-3"
        # 高管配置
    executives:
      # 高管用户名列表
      usernames:
        - "executive1"
        - "executive2"
        - "executive3"

email:
  guard:
    out-url: http://mail.d.xiaomi.net/mail/send
    out-multiple-file-url: http://mail.d.xiaomi.net/mail/sendWithMultipleFile
    inner-host: mail.b2c.srv

nacos:
  config:
    bootstrap:
      enable: true
    remote-first: true
    # server端地址，填写Nacos对应环境的服务端地址
    server-addr: http://staging-nacos.api.xiaomi.net:80
    # 命名空间设置，注意此处需要填：namespaceId的值，public命名空间对应的id为空
    namespace: info_oa_test
    #填写Nacos提供的登陆用户名和密码
    username: info_oa_test
    password@kc-sid: oa-infra.g
    password: GDB0ZjH8bvUzH/MsrWKhkQ3lU8Fw43Wl63sY91Zzk+rJPwcYN/qpLTauwabqqAdwzkAYEiJisoiiuUVcme9hd2Wid0EZ/xgQ91w/NyyHQV2/j+EZrS3ViRgUZRDDx4b3M9Dq7TxiT3WrbP0MDUgA
    #使用nacos配置集的dataId，也就是在上一步的nacos console新建的配置文件
    data-ids: common_config,lark_saas
    #开启自动刷新,默认为false
    auto-refresh: true
    #配置中心的组配置,默认为DEFAULT_GROUP
    group: infra.ums
    #配置中心配置文件文件类型
    type: yaml
    #拉取配置的重试次数，默认3次
    max-retry: 3
    #长轮询任务重试时间，单位为毫秒
    config-retry-time: 2000
    #拉取配置超时时间 单位毫秒 默认30000
    config-long-poll-timeout: 30000
    #监听器首次添加时拉取远端配置 默认false
    enable-remote-sync-config: true
rocketmq:
  name-server: staging-cnbj2-rocketmq.namesrv.api.xiaomi.net:9876
  ak: AKYPGIP74Z2XEXAZC4
  sk@kc-sid: oa-infra.g
  sk: GDDsNUhTGLnFG0Q5e+26/MqOjfkkH8/0t24LaR1irOhFC2oLYQKBg06PulaenVrr5J0YEnK8lFJnuk6zsLCshFBBdz2J/xgQkoD0vrZxR1GtPExrZCBvQRgUtyu2h6JaybkrAihINQjYFyMFBVAA
