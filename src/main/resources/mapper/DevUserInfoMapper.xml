<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.engine.mapper.DevUserInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.engine.entity.info.DevUserInfo">
    <!--@mbg.generated-->
    <!--@Table dev_user_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="update_username" jdbcType="VARCHAR" property="updateUsername" />
    <result column="create_username" jdbcType="VARCHAR" property="createUsername" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, email, username, update_username, create_username, create_time, update_time, 
    phone
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update dev_user_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="phone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update dev_user_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.email != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.username != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateUsername != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_username = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createUsername != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="phone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.phone != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dev_user_info
    (email, username, update_username, create_username, create_time, update_time, phone
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.email,jdbcType=VARCHAR}, #{item.username,jdbcType=VARCHAR}, #{item.updateUsername,jdbcType=VARCHAR}, 
        #{item.createUsername,jdbcType=VARCHAR}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, 
        #{item.phone,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.DevUserInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dev_user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      email,
      username,
      update_username,
      create_username,
      create_time,
      update_time,
      phone,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{email,jdbcType=VARCHAR},
      #{username,jdbcType=VARCHAR},
      #{updateUsername,jdbcType=VARCHAR},
      #{createUsername,jdbcType=VARCHAR},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
      #{phone,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      email = #{email,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      update_username = #{updateUsername,jdbcType=VARCHAR},
      create_username = #{createUsername,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      phone = #{phone,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.DevUserInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into dev_user_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="updateUsername != null">
        update_username,
      </if>
      <if test="createUsername != null">
        create_username,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="phone != null">
        phone,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="updateUsername != null">
        #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUsername != null">
        #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="updateUsername != null">
        update_username = #{updateUsername,jdbcType=VARCHAR},
      </if>
      <if test="createUsername != null">
        create_username = #{createUsername,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>