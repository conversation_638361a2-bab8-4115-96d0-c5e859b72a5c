<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.engine.mapper.EmailRobotInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.engine.entity.info.EmailRobotInfo">
    <!--@mbg.generated-->
    <!--@Table email_robot_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="sender" jdbcType="VARCHAR" property="sender" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="sender_name" jdbcType="VARCHAR" property="senderName" />
    <result column="mail_host" jdbcType="VARCHAR" property="mailHost" />
    <result column="stop_flag" jdbcType="TINYINT" property="stopFlag" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `type`, sender, token, sender_name, mail_host, stop_flag, create_time, update_time
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update email_robot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.type,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="sender = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sender,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="token = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.token,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sender_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.senderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mail_host = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mailHost,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="stop_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stopFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update email_robot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.type,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="sender = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sender != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sender,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="token = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.token != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.token,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sender_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.senderName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.senderName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="mail_host = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mailHost != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.mailHost,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="stop_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stopFlag != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.stopFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into email_robot_info
    (`type`, sender, token, sender_name, mail_host, stop_flag, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.type,jdbcType=TINYINT}, #{item.sender,jdbcType=VARCHAR}, #{item.token,jdbcType=VARCHAR}, 
        #{item.senderName,jdbcType=VARCHAR}, #{item.mailHost,jdbcType=VARCHAR}, #{item.stopFlag,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.EmailRobotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into email_robot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      `type`,
      sender,
      token,
      sender_name,
      mail_host,
      stop_flag,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{type,jdbcType=TINYINT},
      #{sender,jdbcType=VARCHAR},
      #{token,jdbcType=VARCHAR},
      #{senderName,jdbcType=VARCHAR},
      #{mailHost,jdbcType=VARCHAR},
      #{stopFlag,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      `type` = #{type,jdbcType=TINYINT},
      sender = #{sender,jdbcType=VARCHAR},
      token = #{token,jdbcType=VARCHAR},
      sender_name = #{senderName,jdbcType=VARCHAR},
      mail_host = #{mailHost,jdbcType=VARCHAR},
      stop_flag = #{stopFlag,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.EmailRobotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into email_robot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="sender != null">
        sender,
      </if>
      <if test="token != null">
        token,
      </if>
      <if test="senderName != null">
        sender_name,
      </if>
      <if test="mailHost != null">
        mail_host,
      </if>
      <if test="stopFlag != null">
        stop_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="sender != null">
        #{sender,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="senderName != null">
        #{senderName,jdbcType=VARCHAR},
      </if>
      <if test="mailHost != null">
        #{mailHost,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        #{stopFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="sender != null">
        sender = #{sender,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        token = #{token,jdbcType=VARCHAR},
      </if>
      <if test="senderName != null">
        sender_name = #{senderName,jdbcType=VARCHAR},
      </if>
      <if test="mailHost != null">
        mail_host = #{mailHost,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        stop_flag = #{stopFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>