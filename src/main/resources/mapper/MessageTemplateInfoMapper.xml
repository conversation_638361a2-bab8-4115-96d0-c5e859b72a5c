<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.engine.mapper.MessageTemplateInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.engine.entity.info.MessageTemplateInfo">
        <!--@mbg.generated-->
        <!--@Table message_template_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="title_cn" jdbcType="LONGVARCHAR" property="titleCn"/>
        <result column="title_en" jdbcType="LONGVARCHAR" property="titleEn"/>
        <result column="content_cn" jdbcType="LONGVARCHAR" property="contentCn"/>
        <result column="content_en" jdbcType="LONGVARCHAR" property="contentEn"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="card_actions" jdbcType="LONGVARCHAR" property="cardActions"/>
        <result column="images" jdbcType="LONGVARCHAR" property="images"/>
        <result column="image_mode" jdbcType="VARCHAR" property="imageMode"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="system_id" jdbcType="VARCHAR" property="systemId"/>
        <result column="msg_format_type" jdbcType="TINYINT" property="msgFormatType"/>
        <result column="card_actions_cn" jdbcType="VARCHAR" property="cardActionsCn"/>
        <result column="card_actions_en" jdbcType="VARCHAR" property="cardActionsEn"/>
        <result column="theme" jdbcType="VARCHAR" property="theme"/>
        <result column="resolve_ready" jdbcType="VARCHAR" property="resolveReady"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, title_cn, title_en, content_cn, content_en, channel, app_id, card_actions, images, image_mode,
        create_time, update_time, system_id, msg_format_type, card_actions_cn, card_actions_en,theme,resolve_ready
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update message_template_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="title_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="title_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="content_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.contentCn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="content_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.contentEn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="card_actions = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cardActions,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="images = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.images,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="image_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.imageMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="system_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.systemId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="msg_format_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.msgFormatType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="card_actions_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cardActionsCn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="card_actions_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.cardActionsEn,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="theme = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.theme,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resolve_ready = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.resolveReady,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update message_template_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="title_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.titleCn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="title_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.titleEn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contentCn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.contentCn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contentEn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.contentEn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.channel != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="card_actions = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cardActions != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.cardActions,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="images = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.images != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.images,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="image_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.imageMode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.imageMode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="system_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.systemId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.systemId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="msg_format_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.msgFormatType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.msgFormatType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="card_actions_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cardActionsCn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.cardActionsCn,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="card_actions_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cardActionsEn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.cardActionsEn,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="theme = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.theme != null">
                        when id = #{item.theme,jdbcType=BIGINT} then #{item.theme,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="resolve_ready = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.resolveReady != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.resolveReady,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into message_template_info
        (title_cn, title_en, content_cn, content_en, channel, app_id, card_actions, images, image_mode,
        create_time, update_time, system_id, msg_format_type, card_actions_cn, card_actions_en,theme,resolve_ready
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.titleCn,jdbcType=LONGVARCHAR}, #{item.titleEn,jdbcType=LONGVARCHAR},
            #{item.contentCn,jdbcType=LONGVARCHAR},
            #{item.contentEn,jdbcType=LONGVARCHAR}, #{item.channel,jdbcType=TINYINT}, #{item.appId,jdbcType=VARCHAR},
            #{item.cardActions,jdbcType=LONGVARCHAR}, #{item.images,jdbcType=LONGVARCHAR}, #{item.imageMode,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}, #{item.systemId,jdbcType=VARCHAR},
            #{item.msgFormatType,jdbcType=TINYINT}, #{item.cardActionsCn,jdbcType=VARCHAR},
            #{item.cardActionsEn,jdbcType=VARCHAR}, #{item.theme,jdbcType=VARCHAR},
            #{item.resolveReady,jdbcType=TINYINT})
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.engine.entity.info.MessageTemplateInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into message_template_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            title_cn,
            title_en,
            content_cn,
            content_en,
            channel,
            app_id,
            card_actions,
            images,
            image_mode,
            create_time,
            update_time,
            system_id,
            msg_format_type,
            card_actions_cn,
            card_actions_en,
            theme,
            resolve_reday,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{titleCn,jdbcType=LONGVARCHAR},
            #{titleEn,jdbcType=LONGVARCHAR},
            #{contentCn,jdbcType=LONGVARCHAR},
            #{contentEn,jdbcType=LONGVARCHAR},
            #{channel,jdbcType=TINYINT},
            #{appId,jdbcType=VARCHAR},
            #{cardActions,jdbcType=LONGVARCHAR},
            #{images,jdbcType=LONGVARCHAR},
            #{imageMode,jdbcType=VARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{systemId,jdbcType=VARCHAR},
            #{msgFormatType,jdbcType=TINYINT},
            #{cardActionsCn,jdbcType=VARCHAR},
            #{cardActionsEn,jdbcType=VARCHAR},
            #{theme,jdbcType=VARCHAR},
            #{resolveReady,jdbcType=TINYINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            title_cn = #{titleCn,jdbcType=LONGVARCHAR},
            title_en = #{titleEn,jdbcType=LONGVARCHAR},
            content_cn = #{contentCn,jdbcType=LONGVARCHAR},
            content_en = #{contentEn,jdbcType=LONGVARCHAR},
            channel = #{channel,jdbcType=TINYINT},
            app_id = #{appId,jdbcType=VARCHAR},
            card_actions = #{cardActions,jdbcType=LONGVARCHAR},
            images = #{images,jdbcType=LONGVARCHAR},
            image_mode = #{images,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            system_id = #{systemId,jdbcType=VARCHAR},
            msg_format_type = #{msgFormatType,jdbcType=TINYINT},
            card_actions_cn = #{cardActionsCn,jdbcType=VARCHAR},
            card_actions_en = #{cardActionsEn,jdbcType=VARCHAR},
            theme = #{theme,jdbcType=VARCHAR},
            resolve_ready = #{resolveReady,jdbcType=TINYINT},
        </trim>
    </insert>
    <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.engine.entity.info.MessageTemplateInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into message_template_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="titleCn != null">
                title_cn,
            </if>
            <if test="titleEn != null">
                title_en,
            </if>
            <if test="contentCn != null">
                content_cn,
            </if>
            <if test="contentEn != null">
                content_en,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="appId != null">
                app_id,
            </if>
            <if test="cardActions != null">
                card_actions,
            </if>
            <if test="images != null">
                images,
            </if>
            <if test="imageMode != null">
                image_mode,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="systemId != null">
                system_id,
            </if>
            <if test="msgFormatType != null">
                msg_format_type,
            </if>
            <if test="cardActionsCn != null">
                card_actions_cn,
            </if>
            <if test="cardActionsEn != null">
                card_actions_en,
            </if>
            <if test="theme != null">
                theme,
            </if>
            <if test="resolveReady != null">
                resolve_ready,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="titleCn != null">
                #{titleCn,jdbcType=LONGVARCHAR},
            </if>
            <if test="titleEn != null">
                #{titleEn,jdbcType=LONGVARCHAR},
            </if>
            <if test="contentCn != null">
                #{contentCn,jdbcType=LONGVARCHAR},
            </if>
            <if test="contentEn != null">
                #{contentEn,jdbcType=LONGVARCHAR},
            </if>
            <if test="channel != null">
                #{channel,jdbcType=TINYINT},
            </if>
            <if test="appId != null">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="cardActions != null">
                #{cardActions,jdbcType=LONGVARCHAR},
            </if>
            <if test="images != null">
                #{images,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="systemId != null">
                #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="msgFormatType != null">
                #{msgFormatType,jdbcType=TINYINT},
            </if>
            <if test="cardActionsCn != null">
                #{cardActionsCn,jdbcType=VARCHAR},
            </if>
            <if test="cardActionsEn != null">
                #{cardActionsEn,jdbcType=VARCHAR},
            </if>
            <if test="theme != null">
                #{theme,jdbcType=VARCHAR},
            </if>
            <if test="resolveReady != null">
                #{resolveReady,jdbcType=TINYINT},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="titleCn != null">
                title_cn = #{titleCn,jdbcType=LONGVARCHAR},
            </if>
            <if test="titleEn != null">
                title_en = #{titleEn,jdbcType=LONGVARCHAR},
            </if>
            <if test="contentCn != null">
                content_cn = #{contentCn,jdbcType=LONGVARCHAR},
            </if>
            <if test="contentEn != null">
                content_en = #{contentEn,jdbcType=LONGVARCHAR},
            </if>
            <if test="channel != null">
                channel = #{channel,jdbcType=TINYINT},
            </if>
            <if test="appId != null">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="cardActions != null">
                card_actions = #{cardActions,jdbcType=LONGVARCHAR},
            </if>
            <if test="images != null">
                images = #{images,jdbcType=LONGVARCHAR},
            </if>
            <if test="imageMode != null">
                image_mode = #{imageMode,jdbcType=LONGVARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="systemId != null">
                system_id = #{systemId,jdbcType=VARCHAR},
            </if>
            <if test="msgFormatType != null">
                msg_format_type = #{msgFormatType,jdbcType=TINYINT},
            </if>
            <if test="cardActionsCn != null">
                card_actions_cn = #{cardActionsCn,jdbcType=VARCHAR},
            </if>
            <if test="cardActionsEn != null">
                card_actions_en = #{cardActionsEn,jdbcType=VARCHAR},
            </if>
            <if test="theme != null">
                theme = #{theme,jdbcType=VARCHAR},
            </if>
            <if test="resolveReady != null">
                resolve_ready = #{resolveReady,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
</mapper>
