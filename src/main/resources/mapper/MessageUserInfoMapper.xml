<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.engine.mapper.MessageUserInfoMapper">
    <resultMap id="BaseResultMap" type="com.mioffice.ums.engine.entity.info.MessageUserInfo">
        <!--@mbg.generated-->
        <!--@Table message_user_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="msg_tmp_id" jdbcType="BIGINT" property="msgTmpId"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="mi_dept_level2" jdbcType="VARCHAR" property="miDeptLevel2"/>
        <result column="mi_dept_level2_desc" jdbcType="VARCHAR" property="miDeptLevel2Desc"/>
        <result column="mi_dept_level3" jdbcType="VARCHAR" property="miDeptLevel3"/>
        <result column="mi_dept_level3_desc" jdbcType="VARCHAR" property="miDeptLevel3Desc"/>
        <result column="channel" jdbcType="TINYINT" property="channel"/>
        <result column="message_status" jdbcType="TINYINT" property="messageStatus"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="error_log" jdbcType="LONGVARCHAR" property="errorLog"/>
        <result column="retry_count" jdbcType="INTEGER" property="retryCount"/>
        <result column="title_cn" jdbcType="LONGVARCHAR" property="titleCn"/>
        <result column="title_en" jdbcType="LONGVARCHAR" property="titleEn"/>
        <result column="placeholder_content" jdbcType="LONGVARCHAR" property="placeholderContent"/>
        <result column="extra_id" jdbcType="VARCHAR" property="extraId"/>
        <result column="extra_content" jdbcType="LONGVARCHAR" property="extraContent"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="content_flag" jdbcType="TINYINT" property="contentFlag"/>
        <result column="final_content" jdbcType="LONGVARCHAR" property="finalContent"/>
        <result column="error_type" jdbcType="TINYINT" property="errorType"/>
        <result column="cc_email" jdbcType="LONGVARCHAR" property="ccEmail"/>
        <result column="attach_email_url" jdbcType="LONGVARCHAR" property="attachEmailUrl"/>
        <result column="chat_id" jdbcType="VARCHAR" property="chatId"/>
        <result column="mi_dept_level4" jdbcType="VARCHAR" property="miDeptLevel4"/>
        <result column="mi_dept_level4_desc" jdbcType="VARCHAR" property="miDeptLevel4Desc"/>
        <result column="read_status" jdbcType="VARBINARY" property="readStatus"/>
        <result column="sys_id" jdbcType="VARCHAR" property="sysId"/>
        <result column="is_send_saas" jdbcType="TINYINT" property="isSendSaas"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, app_id, msg_tmp_id, open_id, email, username, phone, mi_dept_level2, mi_dept_level2_desc,
        mi_dept_level3, mi_dept_level3_desc, channel, message_status, message_id, error_log,
        retry_count, title_cn, title_en, placeholder_content, extra_id, extra_content, create_time,
        update_time, content_flag, final_content, error_type, cc_email, attach_email_url,
        chat_id, mi_dept_level4, mi_dept_level4_desc, read_status,sys_id,is_send_saas
    </sql>

    <select id="selectByIdFromMaster" resultMap="BaseResultMap">
        select /*master*/
        <include refid="Base_Column_List"/>
        from message_user_info
        where id=#{id}
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update message_user_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="msg_tmp_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.msgTmpId,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="open_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.openId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="message_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.messageStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="message_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.messageId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="error_log = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.errorLog,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="retry_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.retryCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="title_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="title_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="placeholder_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.placeholderContent,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="extra_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.extraContent,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                </foreach>
            </trim>
            <trim prefix="content_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.contentFlag,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="final_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.finalContent,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="error_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.errorType,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="cc_email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.ccEmail,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="attach_email_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.attachEmailUrl,jdbcType=LONGVARCHAR}
                </foreach>
            </trim>
            <trim prefix="chat_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.chatId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4Desc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="read_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.readStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="sys_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.sysId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_send_saas = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.isSendSaas,jdbcType=TINYINT}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update message_user_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="msg_tmp_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.msgTmpId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.msgTmpId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="open_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.openId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.openId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.email != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="username = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.username != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.username,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.phone != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel2 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level2_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel2Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel2Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel3 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level3_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel3Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel3Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.channel != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.channel,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="message_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.messageStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.messageStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="message_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.messageId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.messageId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="error_log = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.errorLog != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.errorLog,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="retry_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.retryCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.retryCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="title_cn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.titleCn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.titleCn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="title_en = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.titleEn != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.titleEn,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="placeholder_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.placeholderContent != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.placeholderContent,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="extra_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.extraId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.extraId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="extra_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.extraContent != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.extraContent,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.contentFlag != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.contentFlag,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="final_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.finalContent != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.finalContent,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="error_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.errorType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.errorType,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="cc_email = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ccEmail != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.ccEmail,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attach_email_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.attachEmailUrl != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.attachEmailUrl,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="chat_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.chatId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.chatId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel4 != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="mi_dept_level4_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.miDeptLevel4Desc != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.miDeptLevel4Desc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="read_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.readStatus != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.readStatus,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sys_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sysId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.sysId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_send_saas = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isSendSaas != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.isSendSaas,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into message_user_info
        (app_id, msg_tmp_id, open_id, email, username, phone, mi_dept_level2, mi_dept_level2_desc,
        mi_dept_level3, mi_dept_level3_desc, channel, message_status, message_id, error_log,
        retry_count, title_cn, title_en, placeholder_content, extra_id, extra_content,
        create_time, update_time, content_flag, final_content, error_type, cc_email, attach_email_url,
        chat_id, mi_dept_level4, mi_dept_level4_desc,read_status,sys_id,is_send_saas)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.appId,jdbcType=VARCHAR}, #{item.msgTmpId,jdbcType=BIGINT}, #{item.openId,jdbcType=VARCHAR},
            #{item.email,jdbcType=VARCHAR}, #{item.username,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR},
            #{item.miDeptLevel2,jdbcType=VARCHAR}, #{item.miDeptLevel2Desc,jdbcType=VARCHAR},
            #{item.miDeptLevel3,jdbcType=VARCHAR}, #{item.miDeptLevel3Desc,jdbcType=VARCHAR},
            #{item.channel,jdbcType=TINYINT}, #{item.messageStatus,jdbcType=TINYINT},
            #{item.messageId,jdbcType=VARCHAR},
            #{item.errorLog,jdbcType=LONGVARCHAR}, #{item.retryCount,jdbcType=INTEGER},
            #{item.titleCn,jdbcType=LONGVARCHAR},
            #{item.titleEn,jdbcType=LONGVARCHAR}, #{item.placeholderContent,jdbcType=LONGVARCHAR},
            #{item.extraId,jdbcType=VARCHAR}, #{item.extraContent,jdbcType=LONGVARCHAR},
            #{item.createTime,jdbcType=BIGINT},
            #{item.updateTime,jdbcType=BIGINT}, #{item.contentFlag,jdbcType=TINYINT},
            #{item.finalContent,jdbcType=LONGVARCHAR},
            #{item.errorType,jdbcType=TINYINT}, #{item.ccEmail,jdbcType=LONGVARCHAR},
            #{item.attachEmailUrl,jdbcType=LONGVARCHAR},
            #{item.chatId,jdbcType=VARCHAR}, #{item.miDeptLevel4,jdbcType=VARCHAR},
            #{item.miDeptLevel4Desc,jdbcType=VARCHAR},
            #{item.readStatus,jdbcType=VARCHAR},
            #{item.sysId,jdbcType=VARCHAR},
            #{item.isSendSaas,jdbcType=TINYINT}
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
            parameterType="com.mioffice.ums.engine.entity.info.MessageUserInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into message_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            app_id,
            msg_tmp_id,
            open_id,
            email,
            username,
            phone,
            mi_dept_level2,
            mi_dept_level2_desc,
            mi_dept_level3,
            mi_dept_level3_desc,
            channel,
            message_status,
            message_id,
            error_log,
            retry_count,
            title_cn,
            title_en,
            placeholder_content,
            extra_id,
            extra_content,
            create_time,
            update_time,
            content_flag,
            final_content,
            error_type,
            cc_email,
            attach_email_url,
            chat_id,
            mi_dept_level4,
            mi_dept_level4_desc,
            read_status,
            sys_id,
            is_send_saas,
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            #{appId,jdbcType=VARCHAR},
            #{msgTmpId,jdbcType=BIGINT},
            #{openId,jdbcType=VARCHAR},
            #{email,jdbcType=VARCHAR},
            #{username,jdbcType=VARCHAR},
            #{phone,jdbcType=VARCHAR},
            #{miDeptLevel2,jdbcType=VARCHAR},
            #{miDeptLevel2Desc,jdbcType=VARCHAR},
            #{miDeptLevel3,jdbcType=VARCHAR},
            #{miDeptLevel3Desc,jdbcType=VARCHAR},
            #{channel,jdbcType=TINYINT},
            #{messageStatus,jdbcType=TINYINT},
            #{messageId,jdbcType=VARCHAR},
            #{errorLog,jdbcType=LONGVARCHAR},
            #{retryCount,jdbcType=INTEGER},
            #{titleCn,jdbcType=LONGVARCHAR},
            #{titleEn,jdbcType=LONGVARCHAR},
            #{placeholderContent,jdbcType=LONGVARCHAR},
            #{extraId,jdbcType=VARCHAR},
            #{extraContent,jdbcType=LONGVARCHAR},
            #{createTime,jdbcType=BIGINT},
            #{updateTime,jdbcType=BIGINT},
            #{contentFlag,jdbcType=TINYINT},
            #{finalContent,jdbcType=LONGVARCHAR},
            #{errorType,jdbcType=TINYINT},
            #{ccEmail,jdbcType=LONGVARCHAR},
            #{attachEmailUrl,jdbcType=LONGVARCHAR},
            #{chatId,jdbcType=VARCHAR},
            #{miDeptLevel4,jdbcType=VARCHAR},
            #{miDeptLevel4Desc,jdbcType=VARCHAR},
            #{readStatus,javaType=TINYINT},
            #{sysId,javaType=VARCHAR},
            #{isSendSaas,jdbcType=TINYINT},
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            app_id = #{appId,jdbcType=VARCHAR},
            msg_tmp_id = #{msgTmpId,jdbcType=BIGINT},
            open_id = #{openId,jdbcType=VARCHAR},
            email = #{email,jdbcType=VARCHAR},
            username = #{username,jdbcType=VARCHAR},
            phone = #{phone,jdbcType=VARCHAR},
            mi_dept_level2 = #{miDeptLevel2,jdbcType=VARCHAR},
            mi_dept_level2_desc = #{miDeptLevel2Desc,jdbcType=VARCHAR},
            mi_dept_level3 = #{miDeptLevel3,jdbcType=VARCHAR},
            mi_dept_level3_desc = #{miDeptLevel3Desc,jdbcType=VARCHAR},
            channel = #{channel,jdbcType=TINYINT},
            message_status = #{messageStatus,jdbcType=TINYINT},
            message_id = #{messageId,jdbcType=VARCHAR},
            error_log = #{errorLog,jdbcType=LONGVARCHAR},
            retry_count = #{retryCount,jdbcType=INTEGER},
            title_cn = #{titleCn,jdbcType=LONGVARCHAR},
            title_en = #{titleEn,jdbcType=LONGVARCHAR},
            placeholder_content = #{placeholderContent,jdbcType=LONGVARCHAR},
            extra_id = #{extraId,jdbcType=VARCHAR},
            extra_content = #{extraContent,jdbcType=LONGVARCHAR},
            create_time = #{createTime,jdbcType=BIGINT},
            update_time = #{updateTime,jdbcType=BIGINT},
            content_flag = #{contentFlag,jdbcType=TINYINT},
            final_content = #{finalContent,jdbcType=LONGVARCHAR},
            error_type = #{errorType,jdbcType=TINYINT},
            cc_email = #{ccEmail,jdbcType=LONGVARCHAR},
            attach_email_url = #{attachEmailUrl,jdbcType=LONGVARCHAR},
            chat_id = #{chatId,jdbcType=VARCHAR},
            mi_dept_level4 = #{miDeptLevel4,jdbcType=VARCHAR},
            mi_dept_level4_desc = #{miDeptLevel4Desc,jdbcType=VARCHAR},
            read_status = #{readStatus,jdbcType=VARCHAR},
            sys_id = #{sysId,jdbcType=VARCHAR},
            is_send_saas = #{isSendSaas,jdbcType=TINYINT},
        </trim>
    </insert>

    <update id="updateAddRetryCount">
        update message_user_info
        set retry_count = retry_count + #{count},update_time=#{currentMs,jdbcType=BIGINT},message_status = #{messageStatus,jdbcType=TINYINT}
        where id in
        <foreach close=")" collection="idList" item="id" open="(" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="addRetryCount">
        update message_user_info
        set retry_count=retry_count + #{count,jdbcType=BIGINT},
            update_time=#{currentMs,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByGroupPage" resultType="com.mioffice.ums.engine.entity.bo.MessageGroupDeptRecordBO">
        <if test="deptLevel2IdList != null and deptLevel2IdList.size() != 0">
            select mi_dept_level2 as miDeptLevel2,
            mi_dept_level2_desc as miDeptLevel2Desc,
            '' as miDeptLevel3,
            '' as miDeptLevel3Desc,
            '' as miDeptLevel4,
            '' as miDeptLevel4Desc,
            count(*) as allCount,
            count(message_status = 1 or message_status = 3 or null) as todoCount,
            count(message_status = 2 or null) as pushCount,
            count(message_status = 4 or null) as failCount,
            count(message_status = 4 or null) as interruptCount,
            count(message_status = 5 or null) as retractCount
            from message_user_info
            where extra_id = #{extraId}
            and mi_dept_level2 in
            <foreach close=")" collection="deptLevel2IdList" item="dept2Id" open="(" separator=",">
                #{dept2Id}
            </foreach>
            group by mi_dept_level2
        </if>
        <if test="deptLevel2IdList != null and deptLevel2IdList.size() != 0 and deptLevel3IdList != null and deptLevel3IdList.size() != 0">
            union
        </if>
        <if test="deptLevel3IdList != null and deptLevel3IdList.size() != 0">
            select mi_dept_level2 as miDeptLevel2,
            mi_dept_level2_desc as miDeptLevel2Desc,
            mi_dept_level3 as miDeptLevel3,
            mi_dept_level3_desc as miDeptLevel3Desc,
            '' as miDeptLevel4,
            '' as miDeptLevel4Desc,
            count(*) as allCount,
            count(message_status = 1 or message_status = 3 or null) as todoCount,
            count(message_status = 2 or null) as pushCount,
            count(message_status = 4 or null) as failCount,
            count(message_status = 4 or null) as interruptCount,
            count(message_status = 5 or null) as retractCount
            from message_user_info
            where extra_id = #{extraId}
            and mi_dept_level3 in
            <foreach close=")" collection="deptLevel3IdList" item="dept3Id" open="(" separator=",">
                #{dept3Id}
            </foreach>
            group by mi_dept_level3
        </if>
        <if test="(deptLevel2IdList != null and deptLevel2IdList.size() != 0 and deptLevel4IdList != null and deptLevel4IdList.size() != 0) or (deptLevel3IdList != null and deptLevel3IdList.size() != 0  and deptLevel4IdList != null and deptLevel4IdList.size() != 0)">
            union
        </if>
        <if test="deptLevel4IdList != null and deptLevel4IdList.size() != 0">
            select mi_dept_level2 as miDeptLevel2,
            mi_dept_level2_desc as miDeptLevel2Desc,
            mi_dept_level3 as miDeptLevel3,
            mi_dept_level3_desc as miDeptLevel3Desc,
            mi_dept_level4 as miDeptLevel4,
            mi_dept_level4_desc as miDeptLevel4Desc,
            count(*) as allCount,
            count(message_status = 1 or message_status = 3 or null) as todoCount,
            count(message_status = 2 or null) as pushCount,
            count(message_status = 4 or null) as failCount,
            count(message_status = 4 or null) as interruptCount,
            count(message_status = 5 or null) as retractCount
            from message_user_info
            where extra_id = #{extraId}
            and mi_dept_level4 in
            <foreach close=")" collection="deptLevel4IdList" item="dept4Id" open="(" separator=",">
                #{dept4Id}
            </foreach>
            group by mi_dept_level4
        </if>
    </select>

    <select id="selectCountByExtraIdList" resultType="com.mioffice.ums.engine.entity.bo.ExtraIdCountBO">
        select extra_id as extraId,
        count(*) as totalCount,
        count(message_status = 1 or message_status = 3 or null) as todoCount,
        count(message_status = 2 or null) as pushCount,
        count(message_status = 4 or null) as failCount,
        count(message_status = 4 or null) as interruptCount,
        count(message_status = 5 or null) as retractCount
        from message_user_info
        where extra_id in
        <foreach close=")" collection="extraIdList" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        group by extra_id;
    </select>
    <select id="selectCostTimeByExtraIdList" resultType="com.mioffice.ums.engine.entity.bo.ExtraIdCostTimeBO">
        select extra_id as extraId,
        max(update_time) - min(create_time) as costTime,
        count(read_status = 1 or null) as readCount
        from message_user_info
        where extra_id in
        <foreach close=")" collection="extraIdList" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and message_status = 2
        group by extra_id;
    </select>

    <resultMap id="errTypeList" type="com.mioffice.ums.engine.entity.bo.ErrorTypeListBo">
        <result column="extra_id" jdbcType="VARCHAR" property="extraId"/>
        <collection ofType="com.mioffice.ums.engine.entity.bo.ErrorTypeBo" property="list">
            <result column="type" jdbcType="TINYINT" property="type"/>
            <result column="count" jdbcType="BIGINT" property="count"/>
        </collection>
    </resultMap>
    <select id="selectErrCountByExtraIdList" resultMap="errTypeList">
        select extra_id,
        count(*) as count,
        error_type as type
        from message_user_info
        where extra_id in
        <foreach close=")" collection="extraIdList" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and error_type in
        <foreach close=")" collection="typeList" item="item" open="(" separator=",">
            #{item,jdbcType=TINYINT}
        </foreach>
        group by extra_id, error_type;
    </select>

    <select id="selectAverageCostTimeByExtraIdList" resultType="java.lang.Long">
        select
        ifnull(avg(costTime), 0)
        from (
        select
        (max(update_time) - min(create_time))/count(*) as costTime
        from
        message_user_info
        where
        extra_id in
        <foreach close=")" collection="extraIdList" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and (message_status = 2 or message_status = 4)
        group by
        extra_id) tmp
    </select>

    <select id="getAvgCostTimeByExtraIdList" resultType="com.mioffice.ums.engine.entity.bo.AvgCostTimeBO">
        select extra_id as extraId,
        max(update_time) - min(create_time) as costTime,
        count(*) as totalCount,
        count(message_status = 1 or message_status = 3 or null) as todoCount,
        count(message_status = 2 or null) as pushCount,
        count(message_status = 4 or null) as failCount,
        count(message_status = 4 or null) as interruptCount,
        count(message_status = 5 or null) as retractCount
        from message_user_info
        where extra_id in
        <foreach close=")" collection="extraIdList" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and message_status = 2
        group by extra_id;
    </select>

    <select id="selectSuccessOrInterruptCostTimeByExtraIdList"
            resultType="com.mioffice.ums.engine.entity.bo.ExtraIdCostTimeBO">
        select
        extraId,
        costTime
        from (
        select
        extra_id as extraId,
        (max(update_time) - min(create_time))/count(*) as costTime
        from
        message_user_info
        where
        extra_id in
        <foreach close=")" collection="extraIdList" item="item" open="(" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and (message_status = 2 or message_status = 4)
        group by
        extra_id) tmp
    </select>

    <select id="queryUnReadDetail" resultType="java.lang.String">
        select parent.username
        from message_user_info parent
        where parent.extra_id = #{parentExtraId}
        and parent.message_status = 2
        and parent.read_status = 0
        <if test="subExtraIdList != null and subExtraIdList.size() != 0">
            and not exists(select sub.username
            from message_user_info sub
            where sub.extra_id in
            <foreach close=")" collection="subExtraIdList" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            and sub.message_status = 2
            and sub.read_status = 1
            and sub.username = parent.username)
        </if>
    </select>

    <select id="selectPageUnReadDetail" resultType="java.lang.String">
        select distinct parent.username
        from message_user_info parent
        where parent.extra_id = #{parentExtraId}
        and parent.message_status = 2
        and parent.read_status = 0
        <if test="searchKey != null and searchKey!='' ">
            and parent.username like CONCAT('%',#{searchKey},'%')
        </if>
        <if test="whiteList != null and whiteList.size()!='' ">
            and parent.username not in
            <foreach close=")" collection="whiteList" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="subExtraIdList != null and subExtraIdList.size() != 0">
            and not exists(select sub.username
            from message_user_info sub
            where sub.extra_id in
            <foreach close=")" collection="subExtraIdList" item="item" open="(" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
            and sub.message_status = 2
            and sub.read_status = 1
            and sub.username = parent.username)
        </if>
    </select>

    <select id="countByExtraIdListAndStatus" resultType="com.mioffice.ums.engine.entity.bo.CountAndCostTimeBO">
        select count(distinct parent.username) as xCount,
        IFNULL(max(parent.update_time) - min(parent.create_time),0) as costTime
        from message_user_info parent
        where parent.extra_id = #{parentExtraId}
        <if test="messageStatus != null">
            and parent.message_status = #{messageStatus}
        </if>
    </select>

    <select id="countToDo" resultType="com.mioffice.ums.engine.entity.bo.CountAndCostTimeBO">
        select count(distinct parent.username)                              as xCount,
               IFNULL(max(parent.update_time) - min(parent.create_time), 0) as costTime
        from message_user_info parent
        where parent.extra_id = #{parentExtraId}
          and parent.message_status in (1, 3)
    </select>

    <select id="selectReadCount" resultType="com.mioffice.ums.engine.entity.bo.CountAndCostTimeBO">
        select count(distinct a.username) as xCount,
        IFNULL(max(a.update_time) - min(a.create_time),0) as costTime
        from (select parent.username,parent.update_time,parent.create_time
        from message_user_info parent
        where parent.extra_id = #{parentExtraId}
        and parent.message_status = 2
        and parent.read_status = 1
        <if test="subExtraIdList != null and subExtraIdList.size() != 0">
            union
            select sub.username,sub.update_time,sub.create_time
            from message_user_info sub
            where sub.message_status = 2
            and sub.read_status = 1
            and sub.username in (select t.username
            from message_user_info t
            where t.message_status = 2
            and t.extra_id = #{parentExtraId})
            and sub.extra_id in
            <foreach collection="subExtraIdList" index="index" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        ) a
    </select>

    <select id="selectReadCount1" resultType="com.mioffice.ums.engine.entity.bo.CountAndCostTimeBO">
        select count(distinct t.username) as xCount,
        IFNULL(max(t.update_time) - min(t.create_time), 0) as costTime
        from (select parent.username,
        parent.read_status as parent_read,
        <choose>
            <when test="subExtraIdList != null and subExtraIdList.size() != 0">
                case
                when isnull(sub
                .update_time)
                then 0
                else 1 end sub_read,
            </when>
            <otherwise>
                0 as sub_read,
            </otherwise>
        </choose>
        parent.update_time,
        parent.create_time
        from message_user_info parent
        <if test="subExtraIdList != null and subExtraIdList.size() != 0">
            left join (select username, update_time, create_time
            from message_user_info
            where message_status = 2
            and read_status = 1
            and extra_id in
            <foreach collection="subExtraIdList" index="index" open="(" close=")" item="item" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>) sub
            on parent.username = sub.username
        </if>
        where parent.message_status = 2
        and parent.extra_id = #{parentExtraId}) as t
        where t.parent_read = 1
        or t.sub_read = 1
    </select>

    <select id="queryReadDetail" resultType="java.lang.String">
        select distinct username from message_user_info where message_status=2 and read_status=1 and extra_id in
        <foreach collection="subExtraIdList" index="index" open="(" close=")" item="item" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryUnReadAppId" resultType="java.lang.String">
        select distinct app_id
        from message_user_info
        where read_status = 0
        and message_status = 2
        and channel = 1
        and chat_id = ''
        and username <![CDATA[<>]]> ''
        and message_id <![CDATA[<>]]> ''
        and app_id <![CDATA[<>]]> ''
        <if test=" startId != null and startId > 0">
            and id <![CDATA[>]]> #{startId}
        </if>
    </select>

    <select id="selectPageSendingExcludeWhiteList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from message_user_info
        where app_id != #{excludeAppId}
          and message_status = 1
          <if test="whiteList != null and whiteList.size() > 0">
            and app_id not in
            <foreach collection="whiteList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
          </if>
        order by id
    </select>

    <select id="selectPageSendingExcludeAppIds" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from message_user_info
        where message_status = 1
          <if test="excludeAppIds != null and excludeAppIds.size() > 0">
            and app_id not in
            <foreach collection="excludeAppIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
          </if>
        order by id
        limit #{limit} offset #{offset}
    </select>

</mapper>
