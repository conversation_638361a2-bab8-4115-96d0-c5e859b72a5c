<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.engine.mapper.RobotInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.engine.entity.info.RobotInfo">
    <!--@mbg.generated-->
    <!--@Table robot_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
    <result column="app_short_name" jdbcType="VARCHAR" property="appShortName" />
    <result column="encrypt_key" jdbcType="VARCHAR" property="encryptKey" />
    <result column="verification_token" jdbcType="VARCHAR" property="verificationToken" />
    <result column="stop_flag" jdbcType="TINYINT" property="stopFlag" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_id, app_secret, app_short_name, encrypt_key, verification_token, stop_flag, 
    create_time, update_time
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update robot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="app_secret = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appSecret,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="app_short_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appShortName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="encrypt_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.encryptKey,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="verification_token = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.verificationToken,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="stop_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stopFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update robot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="app_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_secret = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appSecret != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appSecret,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_short_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appShortName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appShortName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="encrypt_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.encryptKey != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.encryptKey,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="verification_token = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.verificationToken != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.verificationToken,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="stop_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stopFlag != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.stopFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into robot_info
    (app_id, app_secret, app_short_name, encrypt_key, verification_token, stop_flag, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.appId,jdbcType=VARCHAR}, #{item.appSecret,jdbcType=VARCHAR}, #{item.appShortName,jdbcType=VARCHAR}, 
        #{item.encryptKey,jdbcType=VARCHAR}, #{item.verificationToken,jdbcType=VARCHAR}, 
        #{item.stopFlag,jdbcType=TINYINT}, #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.RobotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into robot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      app_id,
      app_secret,
      app_short_name,
      encrypt_key,
      verification_token,
      stop_flag,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{appId,jdbcType=VARCHAR},
      #{appSecret,jdbcType=VARCHAR},
      #{appShortName,jdbcType=VARCHAR},
      #{encryptKey,jdbcType=VARCHAR},
      #{verificationToken,jdbcType=VARCHAR},
      #{stopFlag,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      app_id = #{appId,jdbcType=VARCHAR},
      app_secret = #{appSecret,jdbcType=VARCHAR},
      app_short_name = #{appShortName,jdbcType=VARCHAR},
      encrypt_key = #{encryptKey,jdbcType=VARCHAR},
      verification_token = #{verificationToken,jdbcType=VARCHAR},
      stop_flag = #{stopFlag,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.RobotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into robot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="appSecret != null">
        app_secret,
      </if>
      <if test="appShortName != null">
        app_short_name,
      </if>
      <if test="encryptKey != null">
        encrypt_key,
      </if>
      <if test="verificationToken != null">
        verification_token,
      </if>
      <if test="stopFlag != null">
        stop_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="appShortName != null">
        #{appShortName,jdbcType=VARCHAR},
      </if>
      <if test="encryptKey != null">
        #{encryptKey,jdbcType=VARCHAR},
      </if>
      <if test="verificationToken != null">
        #{verificationToken,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        #{stopFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null">
        app_secret = #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="appShortName != null">
        app_short_name = #{appShortName,jdbcType=VARCHAR},
      </if>
      <if test="encryptKey != null">
        encrypt_key = #{encryptKey,jdbcType=VARCHAR},
      </if>
      <if test="verificationToken != null">
        verification_token = #{verificationToken,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        stop_flag = #{stopFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>