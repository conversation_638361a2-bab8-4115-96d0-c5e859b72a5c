<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mioffice.ums.engine.mapper.SmsRobotInfoMapper">
  <resultMap id="BaseResultMap" type="com.mioffice.ums.engine.entity.info.SmsRobotInfo">
    <!--@mbg.generated-->
    <!--@Table sms_robot_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content_type_key" jdbcType="VARCHAR" property="contentTypeKey" />
    <result column="sign" jdbcType="VARCHAR" property="sign" />
    <result column="stop_flag" jdbcType="TINYINT" property="stopFlag" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, content_type_key, sign, stop_flag, create_time, update_time
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sms_robot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="content_type_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.contentTypeKey,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sign = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sign,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="stop_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.stopFlag,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sms_robot_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="content_type_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contentTypeKey != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.contentTypeKey,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sign = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sign != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.sign,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="stop_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.stopFlag != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.stopFlag,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sms_robot_info
    (content_type_key, sign, stop_flag, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.contentTypeKey,jdbcType=VARCHAR}, #{item.sign,jdbcType=VARCHAR}, #{item.stopFlag,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=BIGINT}, #{item.updateTime,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.SmsRobotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sms_robot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      content_type_key,
      sign,
      stop_flag,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{contentTypeKey,jdbcType=VARCHAR},
      #{sign,jdbcType=VARCHAR},
      #{stopFlag,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      content_type_key = #{contentTypeKey,jdbcType=VARCHAR},
      sign = #{sign,jdbcType=VARCHAR},
      stop_flag = #{stopFlag,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.mioffice.ums.engine.entity.info.SmsRobotInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sms_robot_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="contentTypeKey != null">
        content_type_key,
      </if>
      <if test="sign != null">
        sign,
      </if>
      <if test="stopFlag != null">
        stop_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="contentTypeKey != null">
        #{contentTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="sign != null">
        #{sign,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        #{stopFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="contentTypeKey != null">
        content_type_key = #{contentTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="sign != null">
        sign = #{sign,jdbcType=VARCHAR},
      </if>
      <if test="stopFlag != null">
        stop_flag = #{stopFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>