local limit_root = KEYS[1];
local limit_key = KEYS[2];
local limit_conf = KEYS[3];
local limit_ms_conf = KEYS[4];
local rate = redis.call('HGET', limit_root, limit_conf);
local rate_ms = redis.call('HGET', limit_root, limit_ms_conf);

local interval_ms = 20;
if (rate_ms)
then
    if (tonumber(rate_ms) > 0) then
        rate_ms = tonumber(rate_ms)
    end
else
    rate_ms = 1000
end
if (rate)
then
    if (tonumber(rate) > 0)
    then
        interval_ms = rate_ms / tonumber(rate)
    end
end

local wait_ms = 0;
local timestamp_ms = ARGV[1];
local val = redis.call('HGET', limit_root, limit_key);
if (val)
then
    val = tonumber(val)
    local diff = tonumber(timestamp_ms) - tonumber(interval_ms);
    if (diff < val) then
        wait_ms = val - diff
    else
        redis.call('HSET', limit_root, limit_key, timestamp_ms);
    end
else
    redis.call('HSET', limit_root, limit_key, timestamp_ms);
end

return wait_ms;
