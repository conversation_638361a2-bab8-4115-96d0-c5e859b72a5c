-- 时间窗口限流脚本
-- KEYS[1]: appId
-- ARGV[1]: 当前时间戳(毫秒)
-- ARGV[2]: 1秒窗口限制 (默认50)
-- ARGV[3]: 1分钟窗口限制 (默认1000)

local app_id = KEYS[1]
local current_time = tonumber(ARGV[1])
local second_limit = tonumber(ARGV[2]) or 50
local minute_limit = tonumber(ARGV[3]) or 1000

-- Redis keys for different time windows
local second_key = app_id .. ":second:" .. math.floor(current_time / 1000)
local minute_key = app_id .. ":minute:" .. math.floor(current_time / 60000)

-- 检查1秒窗口限制
local second_count = redis.call('GET', second_key)
if second_count == false then
    second_count = 0
else
    second_count = tonumber(second_count)
end

-- 检查1分钟窗口限制
local minute_count = redis.call('GET', minute_key)
if minute_count == false then
    minute_count = 0
else
    minute_count = tonumber(minute_count)
end

-- 检查是否超过限制
if second_count >= second_limit then
    return {0, "second_limit_exceeded", second_count, second_limit}
end

if minute_count >= minute_limit then
    return {0, "minute_limit_exceeded", minute_count, minute_limit}
end

-- 增加计数器
redis.call('INCR', second_key)
redis.call('INCR', minute_key)

-- 设置过期时间
redis.call('EXPIRE', second_key, 2)  -- 2秒过期，防止时钟偏移
redis.call('EXPIRE', minute_key, 120)  -- 2分钟过期，防止时钟偏移

-- 返回成功，以及当前计数
return {1, "success", second_count + 1, minute_count + 1} 