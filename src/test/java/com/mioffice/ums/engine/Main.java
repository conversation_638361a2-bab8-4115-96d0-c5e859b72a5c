package com.mioffice.ums.engine;

import com.mioffice.ums.engine.template.CacheTemplateLoader;
import com.mioffice.ums.engine.utils.JsonUtils;
import com.mioffice.ums.engine.utils.KeyCenterUtil;
import com.xiaomi.keycenter.common.iface.DataProtectionException;
import freemarker.cache.TemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

import java.io.IOException;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.10
 */
public class Main {

//    private static final String tmp = "<tbody>\n" +
//            "    <#list domains as domain>\n" +
//            "    <tr>\n" +
//            "        <td class=\"tbody-td\">${domain.teamName4!}</td>\n" +
//            "        <td class=\"tbody-td\">${domain.name!}</td>\n" +
//            "        <td class=\"tbody-td\">${domain.host!}</td>\n" +
//            "        <td class=\"tbody-td\">\n" +
//            "            <#if domain.isNotStand==0 >\n" +
//            "                ${domain.rate!}%\n" +
//            "            </#if>\n" +
//            "            <#if domain.isNotStand==1 >\n" +
//            "             <font color=\"red\"><b>${domain.rate!}%</b></font>\n" +
//            "            </#if>\n" +
//            "        </td>\n" +
//            "        <td>\n" +
//            "             <#escape x as x?html>\n" +
//            "                <a href=${domain.monUrl} target=\"_blank\">查看详情</a>\n" +
//            "             </#escape>\n" +
//            "        </td>\n" +
//            "    </tr>\n" +
//            "    </#list>\n" +
//            "</tbody>";


    private static final String tmp = "{\n" +
            "    \"chat_id\": \"${chatId!}\",\n" +
            "    \"msg_type\": \"interactive\",\n" +
            "    \"card\": {\n" +
            "        \"header\": {\n" +
            "            \"title\": {\n" +
            "                \"tag\": \"plain_text\",\n" +
            "                \"content\": \"周日报\"\n" +
            "            }\n" +
            "        },\n" +
            "        \"elements\": [\n" +
            "\n" +
            "            <#list contentList as content>\n" +
            "            {\n" +
            "                \"tag\": \"hr\"\n" +
            "            },\n" +
            "            {\n" +
            "                \"tag\": \"div\",\n" +
            "                \"fields\": [\n" +
            "                    {\n" +
            "                        \"is_short\": false,\n" +
            "                        \"text\": {\n" +
            "                            \"tag\": \"lark_md\",\n" +
            "                            \"content\": \"${content!}\"\n" +
            "                        }\n" +
            "                    }\n" +
            "                ]\n" +
            "            } <#if !content?is_last>,</#if> \n"  +
            "            </#list>\n" +
            "        ]\n" +
            "    }\n" +
            "}";

    public static void main(String[] args) throws DataProtectionException, IOException, TemplateException {
//        Map<String, Object> placeholderContent = new HashMap<>();
//        placeholderContent.put("title", "测试消息");
//        placeholderContent.put("content", "hello world");
//
//        System.out.println(JsonUtils.toJson(placeholderContent));

//        System.out.println(KeyCenterUtil.decrypt("GBAyxKZcnQxb+xA4R2fiwqEYGBLDzAk3aeJG+at+AfoaAZIiKP8YEMvg3OffpElTh+iRDiKb0iUYFMWuePA6VIeGbuK88AH0QdeyA1qlAA=="));
//        System.out.println(System.currentTimeMillis());
        Configuration configuration;
        configuration = new Configuration(Configuration.VERSION_2_3_28);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setLocalizedLookup(false);
        configuration.setTemplateLoader(new TemplateLoader() {

            @Override
            public Object findTemplateSource(String name) throws IOException {
//                return "{ \"chat_id\": \"${chatId!}\", \"msg_type\": \"interactive\", \"card\": { \"header\": { \"title\": { \"tag\": \"plain_text\", \"content\": \"项目周报${title!}\" } }, \"elements\": [ { \"tag\": \"hr\" }, { \"tag\": \"div\", \"fields\": [ { \"is_short\": false, \"text\": { \"tag\": \"lark_md\", \"content\": \"项目状态： ${projectStatus!}\\n研发进度： ${projectProgress!0}% ${extStatus!}\\n预估起止时间：${startTime!} ~ ${endTime!}\\n本周工作回顾：\\n${workReview!}\\n下周工作计划：\\n${nextWorkPlan!}\\n\" } } ] } ] } }";
//                return "{\"content\": \"${workReview}\"}";

                return tmp;
            }

            @Override
            public long getLastModified(Object templateSource) {
                return System.currentTimeMillis();
            }

            @Override
            public Reader getReader(Object templateSource, String encoding) throws IOException {
                return new StringReader((String) templateSource);
            }

            @Override
            public void closeTemplateSource(Object templateSource) throws IOException {

            }
        });

        Template template = configuration.getTemplate("");

//        Map<String, Object> params = JsonUtils.toMap("{\"monUrl\":\"https://mi.com\",\"extStatus\":\"进行中\",\"projectProgress\":31.0,\"nextWorkPlan\":\"1. 工时统计\\n2. 项目验收\",\"startTime\":\"2020-11-12\",\"endTime\":\"2020-11-30\",\"title\":\"(2020-11-30~2020-12-04)\",\"workReview\":\"mi-robot-vacuum-mop-p（IN）\\nmi-computer-monitor-light-bar 「新发布系统」\\nj19c 「后续迁站」\\n\"}");

        Map<String, Object> params = new HashMap<>();

        params.put("contentList", Arrays.asList("content1", "content2", "content3"));
        params.put("size", 3);

//        HashMap<String, Object> obj = new HashMap<>();
//        obj.put("monUrl", "https://mi.com");
//        obj.put("isNotStand", 1);
//        params.put("domains", Arrays.asList(obj));
        StringWriter stringWriter = new StringWriter();
        template.process(params, stringWriter);
        System.out.println(stringWriter.toString());
    }
}
