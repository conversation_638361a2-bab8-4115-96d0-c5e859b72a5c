package com.mioffice.ums.engine.base;

import com.larksuite.appframework.sdk.client.message.Message;
import com.larksuite.appframework.sdk.exception.LarkClientException;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.I18nEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.exceptions.NotFoundRobotException;
import com.mioffice.ums.engine.exceptions.RobotStopException;
import com.mioffice.ums.engine.manager.EnhanceMiWorkRobotManager;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.MessageV1MiWorkRobotManager;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.message.MiWorkMessageHelper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.sender.MiWorkMessageSender;
import com.mioffice.ums.engine.sender.MiWorkMessageSenderV1;
import com.mioffice.ums.engine.template.TemplateParse;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.04
 */
@Slf4j
public class MessageManagerTest extends BaseTest {

    @Autowired
    private MiWorkRobotManager miWorkRobotManager;

    @Autowired
    private IdDeduplicateManager idDeduplicateManager;

    @Autowired
    private MiWorkMessageSender miWorkMessageSender;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @Autowired
    private EnhanceMiWorkRobotManager enhanceMiWorkRobotManager;

    @Autowired
    private MessageV1MiWorkRobotManager miWorkMessageSenderV1;

//    @Autowired
//    private MiWorkMessageHelper miWorkMessageHelper;

    @Test
    public void testSend() throws RobotStopException, LarkClientException, NotFoundRobotException {
       /* MiWorkRobot.Session session = new MiWorkRobot.Session();
        session.setEmail("<EMAIL>");
        String msgId = miWorkRobotManager.sendMsg("cli_9e6998059bb59062", new TextMessage("测试消息，请忽略"), session);
        log.info("msgId = [{}]", msgId);*/
    }

    @Test
    public void testMessageDeduplicate() {

        long t1, t2, t3;
        for (int i = 0; i < 100; i++) {
            t1 = System.currentTimeMillis();
            boolean contains = idDeduplicateManager.contains(MessageChannelEnum.MI_WORK, (long) i);
            log.info("i = [{}], contains = [{}]", i, contains);

            t2 = System.currentTimeMillis();
            log.info("contains t = [{}]", (t2 - t1));
            idDeduplicateManager.add(MessageChannelEnum.MI_WORK, (long) i);
            t3 = System.currentTimeMillis();
            log.info("add t = [{}]", (t3 - t2));
        }
    }

    @Test
    public void testAssert() {
        int a = 1;
        int b = 1;
        Assert.assertTrue("显示", a == b);
    }

    @Test
    public void testSendMsgByTmpId() throws Exception {

        MessageUserInfo messageUserInfo = messageUserInfoMapper.selectById(289285L);
//        messageUserInfo.setMsgTmpId(1960L);

//        MiWorkRobot.Session session = new MiWorkRobot.Session();
//        session.setUserId("yangguanlin");

        miWorkMessageSender.send(messageUserInfo);
    }

    @Autowired
    private TemplateParse templateParse;

    @Test
    public void testMessageParse() throws Exception {

        MessageUserInfo messageUserInfo = messageUserInfoMapper.selectById(290411000);

        MiWorkMessageHelper miWorkMessageHelper = new MiWorkMessageHelper(templateParse);

        Message message = miWorkMessageHelper.parseIi8nSdk(messageUserInfo);

        System.out.println(JsonUtils.toJson(message));
    }

    @Test
    public void testParse01() throws Exception {
        templateParse.parseContent(12000L, null, I18nEnum.zh_cn);
    }

    @Test
    public void testSendCertainMessageUserInfo() throws Exception {
        MessageUserInfo messageUserInfo = messageUserInfoMapper.selectById(43134181);
        MiWorkRobot.Session session = new MiWorkRobot.Session();
        session.setUserId(messageUserInfo.getUsername());
        session.setEmail(messageUserInfo.getEmail());
        session.setOpenChatId(messageUserInfo.getChatId());
        MiWorkResponseBO
                miWorkResponseBO = enhanceMiWorkRobotManager.sendMsg("cli_a31c151044f89062", messageUserInfo, session);
    }

    @Test
    public void testSendV1Message() throws Exception {
        MessageUserInfo messageUserInfo = messageUserInfoMapper.selectById(43134181);
        MiWorkRobot.Session session = new MiWorkRobot.Session();
        session.setUserId(messageUserInfo.getUsername());
        session.setEmail(messageUserInfo.getEmail());
        session.setOpenChatId(messageUserInfo.getChatId());
        MiWorkResponseBO
                miWorkResponseBO = miWorkMessageSenderV1.sendMsg("cli_a31c151044f89062", messageUserInfo, session);
    }
}
