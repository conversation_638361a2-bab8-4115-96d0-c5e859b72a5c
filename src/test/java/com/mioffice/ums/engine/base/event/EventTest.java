package com.mioffice.ums.engine.base.event;

import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.enums.constants.SingleActionConstants;
import com.mioffice.ums.engine.event.SingleEvent;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.08.13
 */
public class EventTest extends BaseTest {

    @Autowired
    private SendMqEventBus sendMQEventBus;

    @Test
    public void testSend() throws InterruptedException {

        SingleEvent singleEvent = new SingleEvent(SingleActionConstants.SYNC_ROBOT_ACTION, Collections.singletonList("cli_9e6998059bb59062"));
        sendMQEventBus.postEvent(singleEvent);

        Thread.sleep(5000);
    }
}
