package com.mioffice.ums.engine.base.mapper;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.entity.bo.CountAndCostTimeBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

/**
 * 自定义ID生成器测试类
 *
 * <AUTHOR>
 * @date 2020/8/10 11:47
 */
@Slf4j
public class CustomIdGenerator extends BaseTest {

    @NacosValue(value = "${ums.admin.executives:}", autoRefreshed = true)
    private String executives;

    @Autowired
    private RobotInfoMapper robotInfoMapper;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @Test
    public void generateIdTest() {
        for (int i = 0; i < 10; i++) {
            MessageUserInfo robotInfo = new MessageUserInfo();
            robotInfo.setAppId("appId-" + i);
            robotInfo.setUsername("appSecret-" + i);
            robotInfo.setTitleCn("appshortname-" + i);
            robotInfo.setCreateTime(System.currentTimeMillis());
            messageUserInfoMapper.insert(robotInfo);
        }
    }

    @Test
    public void addMessageUserList() {

        MessageUserInfo messageUserInfo = MessageUserInfo.newCreateAndUpdateTimeInstant();
        messageUserInfo.setId(IdWorker.getId());
        messageUserInfo.setMsgTmpId(1L);
        messageUserInfo.setTitleCn("测试");

        messageUserInfoMapper.insert(messageUserInfo);
    }

    @Test
    public void testSelectUnReadDetailUserNameByExtraIdList() {
        List<String> unReadUserNameList =
                messageUserInfoMapper.queryUnReadDetail(
                        "e98950845d214066ba764bfb0a58821c", Lists.newArrayList());
        log.info("{}", unReadUserNameList);
    }

    @Test
    public void testPageUnread() {
        //高管名单
        List<String> executiveUsernames = Splitter.on(",").splitToList(executives);
        List<String> excludeUsernames = new ArrayList<>(executiveUsernames);
        excludeUsernames.add("wangwei77");
        String parentExtraId = "2dffee2941b641d994aca3be054a4b3e";
        List<String> subExtraIdList = Lists.newArrayList("19e23eff4c614655af3b4518c0c18161",
                "3a4d6e8ed0bc4f3489ea527a765ab002"
        );
//        subExtraIdList = new ArrayList<>();
        Page<String> page = new Page<>(1, 1);
        IPage<String> pageResult =
                messageUserInfoMapper.selectPageUnReadDetail(page,
                        null,
                        new HashSet<>(excludeUsernames),
                        parentExtraId,
                        subExtraIdList);
        log.info("{}", pageResult.getRecords().size());
    }

    @Test
    public void testCountByExtraIdListAndStatus() {
        CountAndCostTimeBO readCount = messageUserInfoMapper.countByExtraIdListAndStatus("",
                MessageStatusEnum.SEND_SUCCESS.getStatus());

        CountAndCostTimeBO sendCount = messageUserInfoMapper.countByExtraIdListAndStatus("",
                MessageStatusEnum.SEND_SUCCESS.getStatus());
        CountAndCostTimeBO todoCount = messageUserInfoMapper.countByExtraIdListAndStatus("",
                MessageStatusEnum.SENDING.getStatus());

        log.info("send:{},todo:{},read:{},send cost time:{}", sendCount.getXCount(), todoCount.getXCount(),
                readCount.getXCount(), sendCount.getCostTime());
    }
}
