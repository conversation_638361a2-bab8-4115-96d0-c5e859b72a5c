package com.mioffice.ums.engine.base.mapstruct;

import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.utils.MapperUtil;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2020/8/26
 */
@Slf4j
public class MapTest {


    @Test
    public void testMap() {
        MessageUser messageUser = MessageUser.newBuilder()
                .setAppId("test")
                .setEmail("email")
                .setExtraId("1234")
                .setContentFlag(1)
                .setOpenId("12345")
                .setPhone("1234567")
                .setMiDeptLevel2("MiDeptLevel2")
                .build();

//        MessageUserInfo userInfo = UserMapper.INSTANCE.map(messageUser);
        MessageUserInfo messageUserInfo = MapperUtil.INSTANCE.mapToMessageUserInfo(messageUser);
        Assert.assertEquals("test", messageUserInfo.getAppId());
        log.info("{}", messageUserInfo);
    }

    @Test
    public void testMap01() {
//        MessageUser messageUser = MessageUser.newBuilder()
//                .setAppId("test")
//                .setEmail("email")
//                .setExtraId("1234")
//                .setContentFlag(1)
//                .setOpenId("12345")
//                .setPhone("1234567")
//                .setMiDeptLevel2("MiDeptLevel2")
//                .build();

        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setAppId("test");
        messageUserInfo.setEmail("email");
        messageUserInfo.setExtraId("1234");
        messageUserInfo.setContentFlag((byte)1);
        messageUserInfo.setOpenId("12345");
        messageUserInfo.setPhone("1234567");
        messageUserInfo.setMiDeptLevel2("MiDeptLevel2");

//        MessageUserInfo userInfo = UserMapper.INSTANCE.map(messageUser);
        MessageUser messageUser = MapperUtil.INSTANCE.mapToMessageUser(messageUserInfo);
        Assert.assertEquals("test", messageUserInfo.getAppId());
        log.info("{}", messageUserInfo);
    }
}
