package com.mioffice.ums.engine.base.mapstruct;

import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUser;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2020/8/26
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UserMapper {

    UserMapper INSTANCE = Mappers.getMapper(UserMapper.class);

    MessageUserInfo map(MessageUser user);
}
