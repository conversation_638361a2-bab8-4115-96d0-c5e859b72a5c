package com.mioffice.ums.engine.base.talos;

import com.xiaomi.infra.galaxy.talos.client.TalosClientConfigKeys;
import com.xiaomi.infra.galaxy.talos.wrapper.consumer.ConsumerWrapper;
import com.xiaomi.infra.galaxy.talos.wrapper.consumer.ConsumerWrapperConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Properties;

import static com.mioffice.ums.engine.talos.TalosConstant.TOPIC_NAME_MI_WORK;

/**
 * <AUTHOR>
 * @since 2020/8/24
 */
@Slf4j
public class ConsumerTest {

    public static class Consumer {
        private final ConsumerWrapper<String, String> consumer;
        private final String topic;

        public Consumer(String topic) {
            Properties props = new Properties();
            props.setProperty(TalosClientConfigKeys.GALAXY_TALOS_SERVICE_ENDPOINT, "http://staging-cnbj2-talos.api.xiaomi.net");
            props.setProperty(TalosClientConfigKeys.GALAXY_TALOS_ACCESS_KEY, Arrays.toString(Base64.getDecoder().decode("QUtZUEdJUDc0WjJYRVhBWkM0")));
            props.setProperty(TalosClientConfigKeys.GALAXY_TALOS_ACCESS_SECRET, Arrays.toString(Base64.getDecoder().decode("SGtkMjJ0M2ZXMmxDaGZjdUZ0bG8vSURiQ1Z0Rll3ZkgzUGZxRHMzcQ==")));
            props.setProperty(ConsumerWrapperConfig.GALAXY_TALOS_CONSUMER_GROUP_NAME, "test1");
            props.setProperty(ConsumerWrapperConfig.GALAXY_TALOS_CONSUMER_WRAPPER_AUTO_COMMIT, "true");
            props.setProperty(ConsumerWrapperConfig.GALAXY_TALOS_CONSUMER_WRAPPER_AUTO_COMMIT_INTERVAL, "1000");
            props.setProperty("galaxy.talos.consumer.start.reset.offset.value", "-2");
            props.setProperty("galaxy.talos.consumer.max.fetch.records", "1");

            props.setProperty(ConsumerWrapperConfig.GALAXY_TALOS_KEY_DESERIALIZER_CLASS,
                    "com.xiaomi.infra.galaxy.talos.wrapper.common.serialization.StringDeserializer");
            props.setProperty(ConsumerWrapperConfig.GALAXY_TALOS_VALUE_DESERIALIZER_CLASS,
                    "com.xiaomi.infra.galaxy.talos.wrapper.common.serialization.StringDeserializer");

            consumer = new ConsumerWrapper<>(props);
            this.topic = topic;
        }

        public void poll() throws InterruptedException {
            consumer.subscribe(Collections.singletonList(this.topic));
            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(1000);
                for (ConsumerRecord<String, String> record : records) {
                    log.info("topic: {}, partition: {}, k {} message: {}, offset: {}",
                            record.topic(), record.partition(), record.key(), record.value(), record.offset());
                }
            }
        }
    }

    @Test
    public void testConsume() throws InterruptedException {
        Consumer consumer = new Consumer(TOPIC_NAME_MI_WORK);
        consumer.poll();
        Assert.assertTrue(true);
    }
}
