package com.mioffice.ums.engine.base.talos;

import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.talos.TalosConstant;
import com.mioffice.ums.engine.talos.TalosHelper;
import libthrift091.TException;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/11 14:44
 */
public class MiWorkProducer extends BaseTest {

    @Autowired
    private TalosHelper talosHelper;

    @Test
    public void produce() {
        List<String> messageList = new ArrayList<>();
        while (true) {
            for (int i = 0; i < 7; ++i) {
                String messageStr = "message id: " + i + ": this message is a text string.";
                messageList.add(messageStr);
            }

            try {
                talosHelper.produce(TalosConstant.TOPIC_NAME_MI_WORK, messageList);
                Thread.sleep(10000);
            } catch (InterruptedException | TException e) {
                e.printStackTrace();
            }
        }
    }
}
