package com.mioffice.ums.engine.base.talos;

import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.talos.TalosConstant;
import com.mioffice.ums.engine.talos.TalosHelper;
import libthrift091.TException;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2020/8/11 21:22
 */
public class SettingProducer extends BaseTest {

    @Autowired
    private TalosHelper talosHelper;

    @Test
    public void produce() {
        AtomicInteger atomicInteger = new AtomicInteger();
        while (true) {
            List<String> messageList = new ArrayList<>();
            String messageStr = "message id: " + atomicInteger.addAndGet(1) + ": this message is a text string.";
            messageList.add(messageStr);

            try {
                talosHelper.produce(TalosConstant.TOPIC_NAME_SETTING, messageList);
                Thread.sleep(10000);
            } catch (InterruptedException | TException e) {
                e.printStackTrace();
            }
        }
    }
}
