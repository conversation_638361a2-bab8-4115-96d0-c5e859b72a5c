package com.mioffice.ums.engine.config;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * LarkSaasMapConfig 单元测试类 - 100%行覆盖率
 */
@RunWith(MockitoJUnitRunner.class)
public class LarkSaasMapConfigTest {

    private LarkSaasMapConfig larkSaasMapConfig;

    @Before
    public void setUp() {
        larkSaasMapConfig = new LarkSaasMapConfig();
    }

    @Test
    public void testConstructor() {
        // Given & When
        LarkSaasMapConfig config = new LarkSaasMapConfig();

        // Then
        assertNotNull("配置实例应该被正确创建", config);
        assertNull("linkMap初始值应该为null", config.getLinkMap());
        assertNull("appIdMap初始值应该为null", config.getAppIdMap());
        assertNull("saasIdSecretMap初始值应该为null", config.getSaasIdSecretMap());
        assertNull("imgKeyMap初始值应该为null", config.getImgKeyMap());
    }

    @Test
    public void testLinkMapGetterAndSetter() {
        // Given
        Map<String, String> linkMap = new HashMap<>();
        linkMap.put("https://old.example.com", "https://new.example.com");
        linkMap.put("http://test.com", "http://saas.test.com");

        // When
        larkSaasMapConfig.setLinkMap(linkMap);
        Map<String, String> retrievedLinkMap = larkSaasMapConfig.getLinkMap();

        // Then
        assertNotNull("linkMap不应该为null", retrievedLinkMap);
        assertEquals("linkMap应该相等", linkMap, retrievedLinkMap);
        assertEquals("linkMap大小应该正确", 2, retrievedLinkMap.size());
        assertTrue("应该包含第一个链接映射", retrievedLinkMap.containsKey("https://old.example.com"));
        assertTrue("应该包含第二个链接映射", retrievedLinkMap.containsKey("http://test.com"));
        assertEquals("第一个链接映射值应该正确", "https://new.example.com", retrievedLinkMap.get("https://old.example.com"));
        assertEquals("第二个链接映射值应该正确", "http://saas.test.com", retrievedLinkMap.get("http://test.com"));
    }

    @Test
    public void testAppIdMapGetterAndSetter() {
        // Given
        Map<String, String> appIdMap = new HashMap<>();
        appIdMap.put("cli_1234567890abcdef", "cli_saas567890abcdef");
        appIdMap.put("cli_abcdef1234567890", "cli_saasdef1234567890");

        // When
        larkSaasMapConfig.setAppIdMap(appIdMap);
        Map<String, String> retrievedAppIdMap = larkSaasMapConfig.getAppIdMap();

        // Then
        assertNotNull("appIdMap不应该为null", retrievedAppIdMap);
        assertEquals("appIdMap应该相等", appIdMap, retrievedAppIdMap);
        assertEquals("appIdMap大小应该正确", 2, retrievedAppIdMap.size());
        assertTrue("应该包含第一个AppId映射", retrievedAppIdMap.containsKey("cli_1234567890abcdef"));
        assertTrue("应该包含第二个AppId映射", retrievedAppIdMap.containsKey("cli_abcdef1234567890"));
        assertEquals("第一个AppId映射值应该正确", "cli_saas567890abcdef", retrievedAppIdMap.get("cli_1234567890abcdef"));
        assertEquals("第二个AppId映射值应该正确", "cli_saasdef1234567890", retrievedAppIdMap.get("cli_abcdef1234567890"));
    }

    @Test
    public void testSaasIdSecretMapGetterAndSetter() {
        // Given
        Map<String, String> saasIdSecretMap = new HashMap<>();
        saasIdSecretMap.put("app-id-1", "secret-1");
        saasIdSecretMap.put("app-id-2", "secret-2");

        // When
        larkSaasMapConfig.setSaasIdSecretMap(saasIdSecretMap);
        Map<String, String> retrievedSaasIdSecretMap = larkSaasMapConfig.getSaasIdSecretMap();

        // Then
        assertNotNull("saasIdSecretMap不应该为null", retrievedSaasIdSecretMap);
        assertEquals("saasIdSecretMap应该相等", saasIdSecretMap, retrievedSaasIdSecretMap);
        assertEquals("saasIdSecretMap大小应该正确", 2, retrievedSaasIdSecretMap.size());
        assertTrue("应该包含第一个密钥映射", retrievedSaasIdSecretMap.containsKey("app-id-1"));
        assertTrue("应该包含第二个密钥映射", retrievedSaasIdSecretMap.containsKey("app-id-2"));
        assertEquals("第一个密钥映射值应该正确", "secret-1", retrievedSaasIdSecretMap.get("app-id-1"));
        assertEquals("第二个密钥映射值应该正确", "secret-2", retrievedSaasIdSecretMap.get("app-id-2"));
    }

    @Test
    public void testImgKeyMapGetterAndSetter() {
        // Given
        Map<String, String> imgKeyMap = new HashMap<>();
        imgKeyMap.put("img_v2_123abc-def", "img_saas_123abc-def");
        imgKeyMap.put("img_simple-key", "img_saas-simple-key");

        // When
        larkSaasMapConfig.setImgKeyMap(imgKeyMap);
        Map<String, String> retrievedImgKeyMap = larkSaasMapConfig.getImgKeyMap();

        // Then
        assertNotNull("imgKeyMap不应该为null", retrievedImgKeyMap);
        assertEquals("imgKeyMap应该相等", imgKeyMap, retrievedImgKeyMap);
        assertEquals("imgKeyMap大小应该正确", 2, retrievedImgKeyMap.size());
        assertTrue("应该包含第一个图片key映射", retrievedImgKeyMap.containsKey("img_v2_123abc-def"));
        assertTrue("应该包含第二个图片key映射", retrievedImgKeyMap.containsKey("img_simple-key"));
        assertEquals("第一个图片key映射值应该正确", "img_saas_123abc-def", retrievedImgKeyMap.get("img_v2_123abc-def"));
        assertEquals("第二个图片key映射值应该正确", "img_saas-simple-key", retrievedImgKeyMap.get("img_simple-key"));
    }

    @Test
    public void testSettersWithNull() {
        // Given & When
        larkSaasMapConfig.setLinkMap(null);
        larkSaasMapConfig.setAppIdMap(null);
        larkSaasMapConfig.setSaasIdSecretMap(null);
        larkSaasMapConfig.setImgKeyMap(null);

        // Then
        assertNull("linkMap设置为null后应该为null", larkSaasMapConfig.getLinkMap());
        assertNull("appIdMap设置为null后应该为null", larkSaasMapConfig.getAppIdMap());
        assertNull("saasIdSecretMap设置为null后应该为null", larkSaasMapConfig.getSaasIdSecretMap());
        assertNull("imgKeyMap设置为null后应该为null", larkSaasMapConfig.getImgKeyMap());
    }

    @Test
    public void testSettersWithEmptyMaps() {
        // Given
        Map<String, String> emptyMap = new HashMap<>();

        // When
        larkSaasMapConfig.setLinkMap(emptyMap);
        larkSaasMapConfig.setAppIdMap(emptyMap);
        larkSaasMapConfig.setSaasIdSecretMap(emptyMap);
        larkSaasMapConfig.setImgKeyMap(emptyMap);

        // Then
        assertNotNull("linkMap不应该为null", larkSaasMapConfig.getLinkMap());
        assertNotNull("appIdMap不应该为null", larkSaasMapConfig.getAppIdMap());
        assertNotNull("saasIdSecretMap不应该为null", larkSaasMapConfig.getSaasIdSecretMap());
        assertNotNull("imgKeyMap不应该为null", larkSaasMapConfig.getImgKeyMap());
        
        assertTrue("linkMap应该为空", larkSaasMapConfig.getLinkMap().isEmpty());
        assertTrue("appIdMap应该为空", larkSaasMapConfig.getAppIdMap().isEmpty());
        assertTrue("saasIdSecretMap应该为空", larkSaasMapConfig.getSaasIdSecretMap().isEmpty());
        assertTrue("imgKeyMap应该为空", larkSaasMapConfig.getImgKeyMap().isEmpty());
    }

    @Test
    public void testEqualsAndHashCode() {
        // Given
        LarkSaasMapConfig config1 = new LarkSaasMapConfig();
        LarkSaasMapConfig config2 = new LarkSaasMapConfig();
        LarkSaasMapConfig config3 = new LarkSaasMapConfig();

        Map<String, String> linkMap = new HashMap<>();
        linkMap.put("key1", "value1");

        Map<String, String> appIdMap = new HashMap<>();
        appIdMap.put("app1", "id1");

        // When & Then - 测试相等的对象
        assertEquals("相同配置的对象应该相等", config1, config2);
        assertEquals("相等对象的hashCode应该相同", config1.hashCode(), config2.hashCode());

        // 设置相同的值
        config1.setLinkMap(linkMap);
        config1.setAppIdMap(appIdMap);
        config2.setLinkMap(linkMap);
        config2.setAppIdMap(appIdMap);

        assertEquals("设置相同值后对象应该相等", config1, config2);
        assertEquals("相等对象的hashCode应该相同", config1.hashCode(), config2.hashCode());

        // 设置不同的值
        Map<String, String> differentMap = new HashMap<>();
        differentMap.put("different", "value");
        config3.setLinkMap(differentMap);

        assertNotEquals("不同配置的对象应该不相等", config1, config3);
        assertNotEquals("不相等对象的hashCode应该不同", config1.hashCode(), config3.hashCode());
    }

    @Test
    public void testEqualsWithNull() {
        // Given
        LarkSaasMapConfig config = new LarkSaasMapConfig();

        // When & Then
        assertNotEquals("与null比较应该返回false", config, null);
        assertNotEquals("与不同类型对象比较应该返回false", config, "string");
        assertEquals("自己与自己比较应该返回true", config, config);
    }

    @Test
    public void testEqualsWithDifferentFields() {
        // Given
        LarkSaasMapConfig config1 = new LarkSaasMapConfig();
        LarkSaasMapConfig config2 = new LarkSaasMapConfig();

        Map<String, String> map1 = new HashMap<>();
        map1.put("key1", "value1");

        Map<String, String> map2 = new HashMap<>();
        map2.put("key2", "value2");

        // Test different linkMap
        config1.setLinkMap(map1);
        config2.setLinkMap(map2);
        assertNotEquals("不同linkMap的配置应该不相等", config1, config2);

        // Reset and test different appIdMap
        config1 = new LarkSaasMapConfig();
        config2 = new LarkSaasMapConfig();
        config1.setAppIdMap(map1);
        config2.setAppIdMap(map2);
        assertNotEquals("不同appIdMap的配置应该不相等", config1, config2);

        // Reset and test different saasIdSecretMap
        config1 = new LarkSaasMapConfig();
        config2 = new LarkSaasMapConfig();
        config1.setSaasIdSecretMap(map1);
        config2.setSaasIdSecretMap(map2);
        assertNotEquals("不同saasIdSecretMap的配置应该不相等", config1, config2);

        // Reset and test different imgKeyMap
        config1 = new LarkSaasMapConfig();
        config2 = new LarkSaasMapConfig();
        config1.setImgKeyMap(map1);
        config2.setImgKeyMap(map2);
        assertNotEquals("不同imgKeyMap的配置应该不相等", config1, config2);
    }

    @Test
    public void testToString() {
        // Given
        LarkSaasMapConfig config = new LarkSaasMapConfig();

        // When
        String toStringResult = config.toString();

        // Then
        assertNotNull("toString结果不应该为null", toStringResult);
        assertTrue("toString应该包含类名", toStringResult.contains("LarkSaasMapConfig"));
        assertTrue("toString应该包含linkMap字段", toStringResult.contains("linkMap"));
        assertTrue("toString应该包含appIdMap字段", toStringResult.contains("appIdMap"));
        assertTrue("toString应该包含saasIdSecretMap字段", toStringResult.contains("saasIdSecretMap"));
        assertTrue("toString应该包含imgKeyMap字段", toStringResult.contains("imgKeyMap"));
    }

    @Test
    public void testToStringWithValues() {
        // Given
        LarkSaasMapConfig config = new LarkSaasMapConfig();
        
        Map<String, String> linkMap = new HashMap<>();
        linkMap.put("link1", "value1");
        
        Map<String, String> appIdMap = new HashMap<>();
        appIdMap.put("app1", "id1");

        config.setLinkMap(linkMap);
        config.setAppIdMap(appIdMap);

        // When
        String toStringResult = config.toString();

        // Then
        assertNotNull("toString结果不应该为null", toStringResult);
        assertTrue("toString应该包含linkMap的值", toStringResult.contains("link1"));
        assertTrue("toString应该包含appIdMap的值", toStringResult.contains("app1"));
    }

    @Test
    public void testCompleteObjectLifecycle() {
        // Given
        LarkSaasMapConfig config = new LarkSaasMapConfig();

        // 创建测试数据
        Map<String, String> linkMap = new HashMap<>();
        linkMap.put("https://example.com", "https://saas.example.com");

        Map<String, String> appIdMap = new HashMap<>();
        appIdMap.put("cli_original", "cli_saas");

        Map<String, String> saasIdSecretMap = new HashMap<>();
        saasIdSecretMap.put("saas-app-1", "secret-123");

        Map<String, String> imgKeyMap = new HashMap<>();
        imgKeyMap.put("img_original", "img_saas");

        // When - 设置所有字段
        config.setLinkMap(linkMap);
        config.setAppIdMap(appIdMap);
        config.setSaasIdSecretMap(saasIdSecretMap);
        config.setImgKeyMap(imgKeyMap);

        // Then - 验证所有字段都正确设置
        assertEquals("linkMap应该正确设置", linkMap, config.getLinkMap());
        assertEquals("appIdMap应该正确设置", appIdMap, config.getAppIdMap());
        assertEquals("saasIdSecretMap应该正确设置", saasIdSecretMap, config.getSaasIdSecretMap());
        assertEquals("imgKeyMap应该正确设置", imgKeyMap, config.getImgKeyMap());

        // 验证toString包含所有信息
        String toStringResult = config.toString();
        assertTrue("toString应该包含linkMap信息", toStringResult.contains("example.com"));
        assertTrue("toString应该包含appIdMap信息", toStringResult.contains("cli_original"));
        assertTrue("toString应该包含saasIdSecretMap信息", toStringResult.contains("saas-app-1"));
        assertTrue("toString应该包含imgKeyMap信息", toStringResult.contains("img_original"));

        // 创建相同配置的另一个对象测试equals
        LarkSaasMapConfig anotherConfig = new LarkSaasMapConfig();
        anotherConfig.setLinkMap(new HashMap<>(linkMap));
        anotherConfig.setAppIdMap(new HashMap<>(appIdMap));
        anotherConfig.setSaasIdSecretMap(new HashMap<>(saasIdSecretMap));
        anotherConfig.setImgKeyMap(new HashMap<>(imgKeyMap));

        assertEquals("相同配置的对象应该相等", config, anotherConfig);
        assertEquals("相等对象的hashCode应该相同", config.hashCode(), anotherConfig.hashCode());
    }

    @Test
    public void testMapModification() {
        // Given
        Map<String, String> originalMap = new HashMap<>();
        originalMap.put("key1", "value1");
        
        larkSaasMapConfig.setLinkMap(originalMap);

        // When - 修改原始map
        originalMap.put("key2", "value2");

        // Then - 验证配置中的map也被修改（因为是引用）
        assertEquals("配置中的map应该反映原始map的变化", 2, larkSaasMapConfig.getLinkMap().size());
        assertTrue("配置中的map应该包含新添加的key", larkSaasMapConfig.getLinkMap().containsKey("key2"));
    }

    @Test
    public void testAnnotationProperties() {
        // Given
        LarkSaasMapConfig config = new LarkSaasMapConfig();

        // When & Then - 验证类上的注解效果
        // 验证getter方法可以正常调用（初始值可能为null）
        config.getLinkMap(); // 不会抛出异常即表示方法存在
        config.getAppIdMap(); // 不会抛出异常即表示方法存在
        config.getSaasIdSecretMap(); // 不会抛出异常即表示方法存在
        config.getImgKeyMap(); // 不会抛出异常即表示方法存在

        // 验证可以调用setter方法
        config.setLinkMap(new HashMap<>());
        config.setAppIdMap(new HashMap<>());
        config.setSaasIdSecretMap(new HashMap<>());
        config.setImgKeyMap(new HashMap<>());

        // 验证设置后的值不为null
        assertNotNull("设置后linkMap不应该为null", config.getLinkMap());
        assertNotNull("设置后appIdMap不应该为null", config.getAppIdMap());
        assertNotNull("设置后saasIdSecretMap不应该为null", config.getSaasIdSecretMap());
        assertNotNull("设置后imgKeyMap不应该为null", config.getImgKeyMap());

        // 验证toString方法存在并可调用
        assertNotNull("toString方法应该正常工作", config.toString());
    }
}
