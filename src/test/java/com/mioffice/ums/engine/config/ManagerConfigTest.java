package com.mioffice.ums.engine.config;

import com.google.gson.Gson;
import com.mioffice.ums.engine.manager.EmailRobotManager;
import com.mioffice.ums.engine.manager.EnhanceMiWorkRobotManager;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.LarkSaasRobotManager;
import com.mioffice.ums.engine.manager.LarkSaasV1RobotManager;
import com.mioffice.ums.engine.manager.MessageV1MiWorkRobotManager;
import com.mioffice.ums.engine.manager.SmsRobotManager;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import com.mioffice.ums.engine.message.LarkSaasMessageHelper;
import com.mioffice.ums.engine.template.LarkSaasTemplateParse;
import com.mioffice.ums.engine.template.TemplateParse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * ManagerConfig 单元测试类
 * 测试Spring配置类中各种Manager Bean的创建和配置
 * 
 * <AUTHOR> Generated Code
 */
@RunWith(MockitoJUnitRunner.class)
public class ManagerConfigTest {

    @Mock
    private RobotInfoMapper robotInfoMapper;

    @Mock
    private EmailRobotInfoMapper emailRobotInfoMapper;

    @Mock
    private SmsRobotInfoMapper smsRobotInfoMapper;

    @Mock
    private TemplateParse templateParse;

    @Mock
    private LarkSaasTemplateParse larkSaasTemplateParse;

    @Mock
    private LarkSaasMapConfig larkSaasMapConfig;

    @Mock
    private RedissonClient redissonClient;

    private Gson gson = new Gson();

    private ManagerConfig managerConfig;

    private static final String TEST_OUT_GUARD_URL = "http://test-guard.example.com";
    private static final String TEST_OUT_GUARD_MULTIPLE_FILE_URL = "http://test-guard-multiple.example.com";

    @Before
    public void setUp() {
        managerConfig = new ManagerConfig();
        
        // 通过反射设置私有字段
        ReflectionTestUtils.setField(managerConfig, "outGuardUrl", TEST_OUT_GUARD_URL);
        ReflectionTestUtils.setField(managerConfig, "outGuardMultipleFileUrl", TEST_OUT_GUARD_MULTIPLE_FILE_URL);
        ReflectionTestUtils.setField(managerConfig, "larkSaasMapConfig", larkSaasMapConfig);
    }

    /**
     * 测试 enhanceMiWorkRobotManager Bean 的创建
     */
    @Test
    public void testEnhanceMiWorkRobotManager() {
        // 执行Bean创建
        EnhanceMiWorkRobotManager result = managerConfig.enhanceMiWorkRobotManager(robotInfoMapper, templateParse);

        // 验证Bean创建成功
        assertNotNull("EnhanceMiWorkRobotManager应该被成功创建", result);

        // 验证依赖设置（通过反射检查私有字段）
        RobotInfoMapper injectedMapper = (RobotInfoMapper) ReflectionTestUtils.getField(result, "robotInfoMapper");
        TemplateParse injectedTemplate = (TemplateParse) ReflectionTestUtils.getField(result, "templateParse");
        LarkSaasMapConfig injectedConfig = (LarkSaasMapConfig) ReflectionTestUtils.getField(result, "larkSaasMapConfig");

        assertTrue("RobotInfoMapper应该被正确设置", injectedMapper == robotInfoMapper);
        assertTrue("TemplateParse应该被正确设置", injectedTemplate == templateParse);
        assertTrue("LarkSaasMapConfig应该被正确设置", injectedConfig == larkSaasMapConfig);
    }

    /**
     * 测试 messageV1MiWorkRobotManager Bean 的创建
     */
    @Test
    public void testMessageV1MiWorkRobotManager() {
        // 执行Bean创建
        MessageV1MiWorkRobotManager result = managerConfig.messageV1MiWorkRobotManager(robotInfoMapper, templateParse);

        // 验证Bean创建成功
        assertNotNull("MessageV1MiWorkRobotManager应该被成功创建", result);

        // 验证依赖设置
        RobotInfoMapper injectedMapper = (RobotInfoMapper) ReflectionTestUtils.getField(result, "robotInfoMapper");
        TemplateParse injectedTemplate = (TemplateParse) ReflectionTestUtils.getField(result, "templateParse");
        LarkSaasMapConfig injectedConfig = (LarkSaasMapConfig) ReflectionTestUtils.getField(result, "larkSaasMapConfig");

        assertTrue("RobotInfoMapper应该被正确设置", injectedMapper == robotInfoMapper);
        assertTrue("TemplateParse应该被正确设置", injectedTemplate == templateParse);
        assertTrue("LarkSaasMapConfig应该被正确设置", injectedConfig == larkSaasMapConfig);
    }

    /**
     * 测试 larkSaasRobotManager Bean 的创建
     */
    @Test
    public void testLarkSaasRobotManager() {
        // 执行Bean创建
        LarkSaasRobotManager result = managerConfig.larkSaasRobotManager(robotInfoMapper, larkSaasTemplateParse);

        // 验证Bean创建成功
        assertNotNull("LarkSaasRobotManager应该被成功创建", result);

        // 验证依赖设置
        RobotInfoMapper injectedMapper = (RobotInfoMapper) ReflectionTestUtils.getField(result, "robotInfoMapper");
        LarkSaasTemplateParse injectedTemplate = (LarkSaasTemplateParse) ReflectionTestUtils.getField(result, "templateParse");
        LarkSaasMapConfig injectedConfig = (LarkSaasMapConfig) ReflectionTestUtils.getField(result, "larkSaasMapConfig");
        LarkSaasMessageHelper injectedHelper = (LarkSaasMessageHelper) ReflectionTestUtils.getField(result, "larkSaasMessageHelper");

        assertTrue("RobotInfoMapper应该被正确设置", injectedMapper == robotInfoMapper);
        assertTrue("LarkSaasTemplateParse应该被正确设置", injectedTemplate == larkSaasTemplateParse);
        assertTrue("LarkSaasMapConfig应该被正确设置", injectedConfig == larkSaasMapConfig);
        assertNotNull("LarkSaasMessageHelper应该被创建和设置", injectedHelper);
    }

    /**
     * 测试 larkSaasV1RobotManager Bean 的创建
     */
    @Test
    public void testLarkSaasV1RobotManager() {
        // 执行Bean创建
        LarkSaasV1RobotManager result = managerConfig.larkSaasV1RobotManager(robotInfoMapper, larkSaasTemplateParse);

        // 验证Bean创建成功
        assertNotNull("LarkSaasV1RobotManager应该被成功创建", result);

        // 验证依赖设置
        RobotInfoMapper injectedMapper = (RobotInfoMapper) ReflectionTestUtils.getField(result, "robotInfoMapper");
        LarkSaasTemplateParse injectedTemplate = (LarkSaasTemplateParse) ReflectionTestUtils.getField(result, "templateParse");
        LarkSaasMapConfig injectedConfig = (LarkSaasMapConfig) ReflectionTestUtils.getField(result, "larkSaasMapConfig");
        LarkSaasMessageHelper injectedHelper = (LarkSaasMessageHelper) ReflectionTestUtils.getField(result, "larkSaasMessageHelper");

        assertTrue("RobotInfoMapper应该被正确设置", injectedMapper == robotInfoMapper);
        assertTrue("LarkSaasTemplateParse应该被正确设置", injectedTemplate == larkSaasTemplateParse);
        assertTrue("LarkSaasMapConfig应该被正确设置", injectedConfig == larkSaasMapConfig);
        assertNotNull("LarkSaasMessageHelper应该被创建和设置", injectedHelper);
    }

    /**
     * 测试 emailRobotManager Bean 的创建
     */
    @Test
    public void testEmailRobotManager() {
        // 执行Bean创建
        EmailRobotManager result = managerConfig.emailRobotManager(emailRobotInfoMapper, templateParse);

        // 验证Bean创建成功
        assertNotNull("EmailRobotManager应该被成功创建", result);

        // 验证依赖设置
        EmailRobotInfoMapper injectedMapper = (EmailRobotInfoMapper) ReflectionTestUtils.getField(result, "emailRobotInfoMapper");
        TemplateParse injectedTemplate = (TemplateParse) ReflectionTestUtils.getField(result, "templateParse");
        String injectedOutGuardUrl = (String) ReflectionTestUtils.getField(result, "outGuardUrl");
        String injectedOutGuardMultipleFileUrl = (String) ReflectionTestUtils.getField(result, "outGuardMultipleFileUrl");

        assertTrue("EmailRobotInfoMapper应该被正确设置", injectedMapper == emailRobotInfoMapper);
        assertTrue("TemplateParse应该被正确设置", injectedTemplate == templateParse);
        assertEquals("OutGuardUrl应该被正确设置", TEST_OUT_GUARD_URL, injectedOutGuardUrl);
        assertEquals("OutGuardMultipleFileUrl应该被正确设置", TEST_OUT_GUARD_MULTIPLE_FILE_URL, injectedOutGuardMultipleFileUrl);
    }

    /**
     * 测试 smsRobotManager Bean 的创建
     */
    @Test
    public void testSmsRobotManager() {
        // 执行Bean创建
        SmsRobotManager result = managerConfig.smsRobotManager(smsRobotInfoMapper, templateParse, gson);

        // 验证Bean创建成功
        assertNotNull("SmsRobotManager应该被成功创建", result);

        // 验证依赖设置
        SmsRobotInfoMapper injectedMapper = (SmsRobotInfoMapper) ReflectionTestUtils.getField(result, "smsRobotInfoMapper");
        TemplateParse injectedTemplate = (TemplateParse) ReflectionTestUtils.getField(result, "templateParse");
        Gson injectedGson = (Gson) ReflectionTestUtils.getField(result, "gson");

        assertTrue("SmsRobotInfoMapper应该被正确设置", injectedMapper == smsRobotInfoMapper);
        assertTrue("TemplateParse应该被正确设置", injectedTemplate == templateParse);
        assertTrue("Gson应该被正确设置", injectedGson == gson);
    }

    /**
     * 测试 idDeduplicateManager Bean 的创建
     */
    @Test
    public void testIdDeduplicateManager() {
        // 执行Bean创建
        IdDeduplicateManager result = managerConfig.idDeduplicateManager(redissonClient);

        // 验证Bean创建成功
        assertNotNull("IdDeduplicateManager应该被成功创建", result);

        // 验证依赖设置
        RedissonClient injectedRedisson = (RedissonClient) ReflectionTestUtils.getField(result, "redissonClient");

        assertTrue("RedissonClient应该被正确设置", injectedRedisson == redissonClient);
    }

    /**
     * 测试配置字段的注入
     */
    @Test
    public void testConfigurationFields() {
        // 验证配置字段通过反射正确设置
        String outGuardUrl = (String) ReflectionTestUtils.getField(managerConfig, "outGuardUrl");
        String outGuardMultipleFileUrl = (String) ReflectionTestUtils.getField(managerConfig, "outGuardMultipleFileUrl");
        LarkSaasMapConfig larkSaasConfig = (LarkSaasMapConfig) ReflectionTestUtils.getField(managerConfig, "larkSaasMapConfig");

        assertEquals("OutGuardUrl应该被正确注入", TEST_OUT_GUARD_URL, outGuardUrl);
        assertEquals("OutGuardMultipleFileUrl应该被正确注入", TEST_OUT_GUARD_MULTIPLE_FILE_URL, outGuardMultipleFileUrl);
        assertTrue("LarkSaasMapConfig应该被正确注入", larkSaasConfig == larkSaasMapConfig);
    }

    /**
     * 测试 LarkSaasMessageHelper 的创建逻辑
     */
    @Test
    public void testLarkSaasMessageHelperCreation() {
        // 创建LarkSaasRobotManager来验证LarkSaasMessageHelper的创建
        LarkSaasRobotManager larkSaasManager = managerConfig.larkSaasRobotManager(robotInfoMapper, larkSaasTemplateParse);
        LarkSaasMessageHelper helper = (LarkSaasMessageHelper) ReflectionTestUtils.getField(larkSaasManager, "larkSaasMessageHelper");

        assertNotNull("LarkSaasMessageHelper应该被创建", helper);

        // 验证LarkSaasMessageHelper的构造参数
        LarkSaasTemplateParse helperTemplate = (LarkSaasTemplateParse) ReflectionTestUtils.getField(helper, "templateParse");
        assertTrue("LarkSaasMessageHelper应该使用正确的TemplateParse", helperTemplate == larkSaasTemplateParse);
    }

    /**
     * 测试 LarkSaasV1RobotManager 的 LarkSaasMessageHelper 创建
     */
    @Test
    public void testLarkSaasV1MessageHelperCreation() {
        // 创建LarkSaasV1RobotManager来验证LarkSaasMessageHelper的创建
        LarkSaasV1RobotManager larkSaasV1Manager = managerConfig.larkSaasV1RobotManager(robotInfoMapper, larkSaasTemplateParse);
        LarkSaasMessageHelper helper = (LarkSaasMessageHelper) ReflectionTestUtils.getField(larkSaasV1Manager, "larkSaasMessageHelper");

        assertNotNull("LarkSaasMessageHelper应该被创建", helper);

        // 验证LarkSaasMessageHelper的构造参数
        LarkSaasTemplateParse helperTemplate = (LarkSaasTemplateParse) ReflectionTestUtils.getField(helper, "templateParse");
        assertTrue("LarkSaasMessageHelper应该使用正确的TemplateParse", helperTemplate == larkSaasTemplateParse);
    }

    /**
     * 测试所有Manager的初始化调用
     */
    @Test
    public void testManagerInitialization() {
        // 创建spy对象来验证init方法调用
        EnhanceMiWorkRobotManager enhanceManager = spy(managerConfig.enhanceMiWorkRobotManager(robotInfoMapper, templateParse));
        MessageV1MiWorkRobotManager v1Manager = spy(managerConfig.messageV1MiWorkRobotManager(robotInfoMapper, templateParse));
        LarkSaasRobotManager saasManager = spy(managerConfig.larkSaasRobotManager(robotInfoMapper, larkSaasTemplateParse));
        LarkSaasV1RobotManager saasV1Manager = spy(managerConfig.larkSaasV1RobotManager(robotInfoMapper, larkSaasTemplateParse));
        EmailRobotManager emailManager = spy(managerConfig.emailRobotManager(emailRobotInfoMapper, templateParse));
        SmsRobotManager smsManager = spy(managerConfig.smsRobotManager(smsRobotInfoMapper, templateParse, gson));
        IdDeduplicateManager idManager = spy(managerConfig.idDeduplicateManager(redissonClient));

        // 验证所有Manager都被成功创建
        assertNotNull("EnhanceMiWorkRobotManager应该被创建", enhanceManager);
        assertNotNull("MessageV1MiWorkRobotManager应该被创建", v1Manager);
        assertNotNull("LarkSaasRobotManager应该被创建", saasManager);
        assertNotNull("LarkSaasV1RobotManager应该被创建", saasV1Manager);
        assertNotNull("EmailRobotManager应该被创建", emailManager);
        assertNotNull("SmsRobotManager应该被创建", smsManager);
        assertNotNull("IdDeduplicateManager应该被创建", idManager);
    }

    /**
     * 测试配置类的实例化
     */
    @Test
    public void testManagerConfigInstantiation() {
        assertNotNull("ManagerConfig实例应该被正确创建", managerConfig);
        
        // 验证ManagerConfig是配置类的实例
        assertTrue("ManagerConfig应该是ManagerConfig类的实例", managerConfig instanceof ManagerConfig);
    }

    /**
     * 测试参数验证 - null参数处理
     */
    @Test
    public void testNullParameterHandling() {
        // 测试使用null参数创建Bean（这些可能会抛出异常或创建部分配置的Bean）
        try {
            EnhanceMiWorkRobotManager result1 = managerConfig.enhanceMiWorkRobotManager(null, templateParse);
            assertNotNull("即使RobotInfoMapper为null，Manager也应该被创建", result1);
        } catch (Exception e) {
            // 某些Manager可能不允许null参数，这是正常的
            assertTrue("应该能够处理null参数", true);
        }

        try {
            MessageV1MiWorkRobotManager result2 = managerConfig.messageV1MiWorkRobotManager(robotInfoMapper, null);
            assertNotNull("即使TemplateParse为null，Manager也应该被创建", result2);
        } catch (Exception e) {
            // 某些Manager可能不允许null参数，这是正常的
            assertTrue("应该能够处理null参数", true);
        }
    }

    /**
     * 测试Bean方法的返回类型
     */
    @Test
    public void testBeanReturnTypes() {
        // 验证Bean方法返回正确的类型
        EnhanceMiWorkRobotManager enhanceManager = managerConfig.enhanceMiWorkRobotManager(robotInfoMapper, templateParse);
        MessageV1MiWorkRobotManager v1Manager = managerConfig.messageV1MiWorkRobotManager(robotInfoMapper, templateParse);
        LarkSaasRobotManager saasManager = managerConfig.larkSaasRobotManager(robotInfoMapper, larkSaasTemplateParse);
        LarkSaasV1RobotManager saasV1Manager = managerConfig.larkSaasV1RobotManager(robotInfoMapper, larkSaasTemplateParse);
        EmailRobotManager emailManager = managerConfig.emailRobotManager(emailRobotInfoMapper, templateParse);
        SmsRobotManager smsManager = managerConfig.smsRobotManager(smsRobotInfoMapper, templateParse, gson);
        IdDeduplicateManager idManager = managerConfig.idDeduplicateManager(redissonClient);

        // 验证类型
        assertTrue("应该返回EnhanceMiWorkRobotManager类型", enhanceManager instanceof EnhanceMiWorkRobotManager);
        assertTrue("应该返回MessageV1MiWorkRobotManager类型", v1Manager instanceof MessageV1MiWorkRobotManager);
        assertTrue("应该返回LarkSaasRobotManager类型", saasManager instanceof LarkSaasRobotManager);
        assertTrue("应该返回LarkSaasV1RobotManager类型", saasV1Manager instanceof LarkSaasV1RobotManager);
        assertTrue("应该返回EmailRobotManager类型", emailManager instanceof EmailRobotManager);
        assertTrue("应该返回SmsRobotManager类型", smsManager instanceof SmsRobotManager);
        assertTrue("应该返回IdDeduplicateManager类型", idManager instanceof IdDeduplicateManager);
    }

    /**
     * 测试重复创建Bean的行为
     */
    @Test
    public void testMultipleBeanCreation() {
        // 多次调用Bean方法应该创建不同的实例
        EnhanceMiWorkRobotManager manager1 = managerConfig.enhanceMiWorkRobotManager(robotInfoMapper, templateParse);
        EnhanceMiWorkRobotManager manager2 = managerConfig.enhanceMiWorkRobotManager(robotInfoMapper, templateParse);

        assertNotNull("第一个Manager实例应该被创建", manager1);
        assertNotNull("第二个Manager实例应该被创建", manager2);
        assertFalse("两次调用应该创建不同的实例", manager1 == manager2);
    }
}
