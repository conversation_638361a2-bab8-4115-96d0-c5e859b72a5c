package com.mioffice.ums.engine.email;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Splitter;
import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.entity.bo.SmsResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.manager.EmailRobotManager;
import com.mioffice.ums.engine.manager.SmsRobotManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.01
 */
@Slf4j
public class EmailTest extends BaseTest {

    @Autowired
    private EmailRobotManager emailRobotManager;

    @Autowired
    private SmsRobotManager smsRobotManager;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @NacosValue(value = "${ums.engine.executives:}", autoRefreshed = true)
    private String executives;

    @Test
    public void sendMail() throws Exception {
        if (StringUtils.isNotBlank(executives)) {
            List<String>  executiveUsernames = Splitter.on(",").splitToList(executives);
        }
//        28110
        MessageUserInfo messageUserInfo = messageUserInfoMapper.selectById(541753L);
        String msgId = emailRobotManager.sendMsg("<EMAIL>", messageUserInfo).getMsgId();
        Assert.hasText(msgId, "消息发送失败");
    }

    @Test
    public void sendSms() throws Exception {
        MessageUserInfo messageUserInfo = messageUserInfoMapper.selectById(541753L);
        SmsResponseBO smsResponseBO = smsRobotManager.sendMsg("9000085", messageUserInfo);
        System.out.println(smsResponseBO);
    }
}
