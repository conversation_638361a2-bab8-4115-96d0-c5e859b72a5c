package com.mioffice.ums.engine.email;

import com.mioffice.ums.engine.entity.bo.EmailRequestBO;
import com.mioffice.ums.engine.entity.bo.EmailResponseBO;
import com.mioffice.ums.engine.robot.InnerEmailRobot;
import com.mioffice.ums.engine.robot.Status;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.01
 */
@Slf4j
public class MainTest {

    public static void main(String[] args) throws Exception {
        Status status = new Status();
        status.setStopFlag((byte) 1);
        InnerEmailRobot innerEmailRobot = new InnerEmailRobot("mail.b2c.srv", "<EMAIL>", status);
        innerEmailRobot.init();

        EmailRequestBO emailRequestBO = new EmailRequestBO();
        emailRequestBO.setTo("<EMAIL>");
        emailRequestBO.setCcTo("<EMAIL>");
        emailRequestBO.setTitle("测试邮箱");
        emailRequestBO.setHtml("永远相信美好的事情即将发生");
        emailRequestBO.setAttachUrl("[\"http://cnbj1.fds.api.xiaomi.com/administration-notice-admin/notice/2020-08-04/8ab28feb-5176-43ab-ae63-3107b0ce805b.png?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=iS1aW890cwgHVS8HWVV84zqkdA0=\", \"http://cnbj1.fds.api.xiaomi.com/administration-notice-admin/notice/2020-08-04/8ab28feb-5176-43ab-ae63-3107b0ce805b.png?GalaxyAccessKeyId=5151729087601&Expires=9223372036854775807&Signature=iS1aW890cwgHVS8HWVV84zqkdA0=\"]");
        EmailResponseBO emailResponseBO = innerEmailRobot.sendMsg(emailRequestBO);
        Assert.assertTrue(Objects.nonNull(emailResponseBO.getMsgId()));

//        List<String> list = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");
//        for (String email : list) {
//            ThreadUtil.execute(() -> {
//                try {
//                    EmailRequestBO emailRequestBO = new EmailRequestBO();
//                    emailRequestBO.setTo(email);
//                    emailRequestBO.setTitle("测试邮箱");
//                    emailRequestBO.setHtml("永远相信美好的事情即将发生");
//                    Status status = new Status();
//                    status.setStopFlag((byte)1);
//                    OutEmailRobot outEmailRobot = new OutEmailRobot("https://mail.d.xiaomi.net/mail/send", status);
//                    String msgId = outEmailRobot.sendMsg(emailRequestBO).getMsgId();
//                    log.info("msgId = [{}]", msgId);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            });
//        }

//        Message ss = new TextMessage("ss");
//        System.out.println(JsonUtils.toJson(ss));

    }
}
