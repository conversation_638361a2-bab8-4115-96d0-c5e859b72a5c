package com.mioffice.ums.engine.entity.info;

import com.mioffice.ums.engine.enums.I18nEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.ResolveReadyEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import static org.junit.Assert.*;

/**
 * MessageTemplateInfo 单元测试类 - 测试消息模板实体类的各项功能
 * 
 * <AUTHOR> Generated Code
 */
@RunWith(JUnit4.class)
public class MessageTemplateInfoTest {

    private MessageTemplateInfo messageTemplateInfo;

    private static final Long TEST_ID = 12345L;
    private static final String TEST_TITLE_CN = "测试标题中文";
    private static final String TEST_TITLE_EN = "Test Title English";
    private static final String TEST_CONTENT_CN = "测试内容中文";
    private static final String TEST_CONTENT_EN = "Test Content English";
    private static final String TEST_APP_ID = "cli_test12345678";
    private static final String TEST_SYSTEM_ID = "test_system";
    private static final String TEST_CARD_ACTIONS = "[{\"name\": \"按钮\", \"landingUrl\":\"http://test.com\"}]";
    private static final String TEST_CARD_ACTIONS_CN = "[{\"name\": \"中文按钮\", \"landingUrl\":\"http://test.cn\"}]";
    private static final String TEST_CARD_ACTIONS_EN = "[{\"name\": \"English Button\", \"landingUrl\":\"http://test.en\"}]";
    private static final String TEST_IMAGES = "{\"123456\": \"img_key_123\"}";
    private static final String TEST_SAAS_IMAGES = "{\"654321\": \"saas_img_key_456\"}";
    private static final String TEST_THEME = "blue";

    @Before
    public void setUp() {
        messageTemplateInfo = new MessageTemplateInfo();
    }

    /**
     * 测试实体类创建
     */
    @Test
    public void testEntityCreation() {
        assertNotNull("MessageTemplateInfo实例应该被成功创建", messageTemplateInfo);
    }

    /**
     * 测试ID属性的设置和获取
     */
    @Test
    public void testIdSetterAndGetter() {
        messageTemplateInfo.setId(TEST_ID);
        
        assertEquals("ID应该被正确设置", TEST_ID, messageTemplateInfo.getId());
    }

    /**
     * 测试中文标题属性的设置和获取
     */
    @Test
    public void testTitleCnSetterAndGetter() {
        messageTemplateInfo.setTitleCn(TEST_TITLE_CN);
        
        assertEquals("中文标题应该被正确设置", TEST_TITLE_CN, messageTemplateInfo.getTitleCn());
    }

    /**
     * 测试英文标题属性的设置和获取
     */
    @Test
    public void testTitleEnSetterAndGetter() {
        messageTemplateInfo.setTitleEn(TEST_TITLE_EN);
        
        assertEquals("英文标题应该被正确设置", TEST_TITLE_EN, messageTemplateInfo.getTitleEn());
    }

    /**
     * 测试中文内容属性的设置和获取
     */
    @Test
    public void testContentCnSetterAndGetter() {
        messageTemplateInfo.setContentCn(TEST_CONTENT_CN);
        
        assertEquals("中文内容应该被正确设置", TEST_CONTENT_CN, messageTemplateInfo.getContentCn());
    }

    /**
     * 测试英文内容属性的设置和获取
     */
    @Test
    public void testContentEnSetterAndGetter() {
        messageTemplateInfo.setContentEn(TEST_CONTENT_EN);
        
        assertEquals("英文内容应该被正确设置", TEST_CONTENT_EN, messageTemplateInfo.getContentEn());
    }

    /**
     * 测试渠道属性的设置和获取
     */
    @Test
    public void testChannelSetterAndGetter() {
        Byte channel = MessageChannelEnum.MI_WORK.getType();
        messageTemplateInfo.setChannel(channel);
        
        assertEquals("渠道应该被正确设置", channel, messageTemplateInfo.getChannel());
    }

    /**
     * 测试应用ID属性的设置和获取
     */
    @Test
    public void testAppIdSetterAndGetter() {
        messageTemplateInfo.setAppId(TEST_APP_ID);
        
        assertEquals("应用ID应该被正确设置", TEST_APP_ID, messageTemplateInfo.getAppId());
    }

    /**
     * 测试卡片操作属性的设置和获取
     */
    @Test
    public void testCardActionsSetterAndGetter() {
        messageTemplateInfo.setCardActions(TEST_CARD_ACTIONS);
        
        assertEquals("卡片操作应该被正确设置", TEST_CARD_ACTIONS, messageTemplateInfo.getCardActions());
    }

    /**
     * 测试图片属性的设置和获取
     */
    @Test
    public void testImagesSetterAndGetter() {
        messageTemplateInfo.setImages(TEST_IMAGES);
        
        assertEquals("图片信息应该被正确设置", TEST_IMAGES, messageTemplateInfo.getImages());
    }

    /**
     * 测试SaaS图片属性的设置和获取
     */
    @Test
    public void testSaasImagesSetterAndGetter() {
        messageTemplateInfo.setSaasImages(TEST_SAAS_IMAGES);
        
        assertEquals("SaaS图片信息应该被正确设置", TEST_SAAS_IMAGES, messageTemplateInfo.getSaasImages());
    }

    /**
     * 测试创建时间属性的设置和获取
     */
    @Test
    public void testCreateTimeSetterAndGetter() {
        Long createTime = System.currentTimeMillis();
        messageTemplateInfo.setCreateTime(createTime);
        
        assertEquals("创建时间应该被正确设置", createTime, messageTemplateInfo.getCreateTime());
    }

    /**
     * 测试更新时间属性的设置和获取
     */
    @Test
    public void testUpdateTimeSetterAndGetter() {
        Long updateTime = System.currentTimeMillis();
        messageTemplateInfo.setUpdateTime(updateTime);
        
        assertEquals("更新时间应该被正确设置", updateTime, messageTemplateInfo.getUpdateTime());
    }

    /**
     * 测试系统ID属性的设置和获取
     */
    @Test
    public void testSystemIdSetterAndGetter() {
        messageTemplateInfo.setSystemId(TEST_SYSTEM_ID);
        
        assertEquals("系统ID应该被正确设置", TEST_SYSTEM_ID, messageTemplateInfo.getSystemId());
    }

    /**
     * 测试消息格式类型属性的设置和获取
     */
    @Test
    public void testMsgFormatTypeSetterAndGetter() {
        Byte msgFormatType = (byte) 1;
        messageTemplateInfo.setMsgFormatType(msgFormatType);
        
        assertEquals("消息格式类型应该被正确设置", msgFormatType, messageTemplateInfo.getMsgFormatType());
    }

    /**
     * 测试中文卡片操作属性的设置和获取
     */
    @Test
    public void testCardActionsCnSetterAndGetter() {
        messageTemplateInfo.setCardActionsCn(TEST_CARD_ACTIONS_CN);
        
        assertEquals("中文卡片操作应该被正确设置", TEST_CARD_ACTIONS_CN, messageTemplateInfo.getCardActionsCn());
    }

    /**
     * 测试英文卡片操作属性的设置和获取
     */
    @Test
    public void testCardActionsEnSetterAndGetter() {
        messageTemplateInfo.setCardActionsEn(TEST_CARD_ACTIONS_EN);
        
        assertEquals("英文卡片操作应该被正确设置", TEST_CARD_ACTIONS_EN, messageTemplateInfo.getCardActionsEn());
    }

    /**
     * 测试主题属性的设置和获取
     */
    @Test
    public void testThemeSetterAndGetter() {
        messageTemplateInfo.setTheme(TEST_THEME);
        
        assertEquals("主题应该被正确设置", TEST_THEME, messageTemplateInfo.getTheme());
    }

    /**
     * 测试解析就绪状态属性的设置和获取
     */
    @Test
    public void testResolveReadySetterAndGetter() {
        Byte resolveReady = ResolveReadyEnum.YES.getCode();
        messageTemplateInfo.setResolveReady(resolveReady);
        
        assertEquals("解析就绪状态应该被正确设置", resolveReady, messageTemplateInfo.getResolveReady());
    }

    /**
     * 测试newCreateAndUpdateTimeInstant静态方法
     */
    @Test
    public void testNewCreateAndUpdateTimeInstant() {
        MessageTemplateInfo newTemplate = MessageTemplateInfo.newCreateAndUpdateTimeInstant();
        
        assertNotNull("应该创建新的MessageTemplateInfo实例", newTemplate);
        assertNotNull("创建时间应该被设置", newTemplate.getCreateTime());
        assertNotNull("更新时间应该被设置", newTemplate.getUpdateTime());
                          assertEquals("解析就绪状态应该为YES", (byte) ResolveReadyEnum.YES.getCode(), newTemplate.getResolveReady().byteValue());
         assertEquals("创建时间和更新时间应该相等", newTemplate.getCreateTime().longValue(), newTemplate.getUpdateTime().longValue());
    }

    /**
     * 测试newUpdateTimeInstant静态方法
     */
    @Test
    public void testNewUpdateTimeInstant() {
        MessageTemplateInfo newTemplate = MessageTemplateInfo.newUpdateTimeInstant();
        
        assertNotNull("应该创建新的MessageTemplateInfo实例", newTemplate);
        assertNotNull("更新时间应该被设置", newTemplate.getUpdateTime());
        assertNull("创建时间应该为null", newTemplate.getCreateTime());
    }

    /**
     * 测试getI18nContent方法 - 中文
     */
    @Test
    public void testGetI18nContent_Chinese() {
        messageTemplateInfo.setContentCn(TEST_CONTENT_CN);
        messageTemplateInfo.setContentEn(TEST_CONTENT_EN);
        
        String content = messageTemplateInfo.getI18nContent(I18nEnum.zh_cn);
        
        assertEquals("应该返回中文内容", TEST_CONTENT_CN, content);
    }

    /**
     * 测试getI18nContent方法 - 英文
     */
    @Test
    public void testGetI18nContent_English() {
        messageTemplateInfo.setContentCn(TEST_CONTENT_CN);
        messageTemplateInfo.setContentEn(TEST_CONTENT_EN);
        
        String content = messageTemplateInfo.getI18nContent(I18nEnum.en_us);
        
        assertEquals("应该返回英文内容", TEST_CONTENT_EN, content);
    }

    /**
     * 测试getI18nTitle方法 - 中文
     */
    @Test
    public void testGetI18nTitle_Chinese() {
        messageTemplateInfo.setTitleCn(TEST_TITLE_CN);
        messageTemplateInfo.setTitleEn(TEST_TITLE_EN);
        
        String title = messageTemplateInfo.getI18nTitle(I18nEnum.zh_cn);
        
        assertEquals("应该返回中文标题", TEST_TITLE_CN, title);
    }

    /**
     * 测试getI18nTitle方法 - 英文
     */
    @Test
    public void testGetI18nTitle_English() {
        messageTemplateInfo.setTitleCn(TEST_TITLE_CN);
        messageTemplateInfo.setTitleEn(TEST_TITLE_EN);
        
        String title = messageTemplateInfo.getI18nTitle(I18nEnum.en_us);
        
        assertEquals("应该返回英文标题", TEST_TITLE_EN, title);
    }

    /**
     * 测试hasCardActions方法 - 有通用卡片操作
     */
    @Test
    public void testHasCardActions_WithGeneralActions() {
        messageTemplateInfo.setCardActions(TEST_CARD_ACTIONS);
        
        boolean hasActions = messageTemplateInfo.hasCardActions();
        
        assertTrue("应该有卡片操作", hasActions);
    }

    /**
     * 测试hasCardActions方法 - 有中文卡片操作
     */
    @Test
    public void testHasCardActions_WithChineseActions() {
        messageTemplateInfo.setCardActionsCn(TEST_CARD_ACTIONS_CN);
        
        boolean hasActions = messageTemplateInfo.hasCardActions();
        
        assertTrue("应该有卡片操作", hasActions);
    }

    /**
     * 测试hasCardActions方法 - 有英文卡片操作
     */
    @Test
    public void testHasCardActions_WithEnglishActions() {
        messageTemplateInfo.setCardActionsEn(TEST_CARD_ACTIONS_EN);
        
        boolean hasActions = messageTemplateInfo.hasCardActions();
        
        assertTrue("应该有卡片操作", hasActions);
    }

    /**
     * 测试hasCardActions方法 - 无卡片操作
     */
    @Test
    public void testHasCardActions_WithoutActions() {
        // 不设置任何卡片操作
        boolean hasActions = messageTemplateInfo.hasCardActions();
        
        assertFalse("应该没有卡片操作", hasActions);
    }

    /**
     * 测试hasCardActions方法 - 空字符串卡片操作
     */
    @Test
    public void testHasCardActions_WithEmptyActions() {
        messageTemplateInfo.setCardActions("");
        messageTemplateInfo.setCardActionsCn("");
        messageTemplateInfo.setCardActionsEn("");
        
        boolean hasActions = messageTemplateInfo.hasCardActions();
        
        assertFalse("空字符串应该被认为没有卡片操作", hasActions);
    }

    /**
     * 测试getCardActions方法 - 中文
     */
    @Test
    public void testGetCardActions_Chinese() {
        messageTemplateInfo.setCardActionsCn(TEST_CARD_ACTIONS_CN);
        messageTemplateInfo.setCardActionsEn(TEST_CARD_ACTIONS_EN);
        
        String cardActions = messageTemplateInfo.getCardActions(I18nEnum.zh_cn);
        
        assertEquals("应该返回中文卡片操作", TEST_CARD_ACTIONS_CN, cardActions);
    }

    /**
     * 测试getCardActions方法 - 英文
     */
    @Test
    public void testGetCardActions_English() {
        messageTemplateInfo.setCardActionsCn(TEST_CARD_ACTIONS_CN);
        messageTemplateInfo.setCardActionsEn(TEST_CARD_ACTIONS_EN);
        
        String cardActions = messageTemplateInfo.getCardActions(I18nEnum.en_us);
        
        assertEquals("应该返回英文卡片操作", TEST_CARD_ACTIONS_EN, cardActions);
    }

    /**
     * 测试空值处理 - 所有属性为null
     */
    @Test
    public void testNullValues() {
        // 验证默认值都为null
        assertNull("ID默认应该为null", messageTemplateInfo.getId());
        assertNull("中文标题默认应该为null", messageTemplateInfo.getTitleCn());
        assertNull("英文标题默认应该为null", messageTemplateInfo.getTitleEn());
        assertNull("中文内容默认应该为null", messageTemplateInfo.getContentCn());
        assertNull("英文内容默认应该为null", messageTemplateInfo.getContentEn());
        assertNull("渠道默认应该为null", messageTemplateInfo.getChannel());
        assertNull("应用ID默认应该为null", messageTemplateInfo.getAppId());
    }

    /**
     * 测试边界值 - 最大长度字符串
     */
    @Test
    public void testBoundaryValues() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("a");
        }
        String longString = sb.toString(); // 1000个字符的字符串
        
        messageTemplateInfo.setTitleCn(longString);
        messageTemplateInfo.setContentCn(longString);
        
        assertEquals("长字符串标题应该被正确设置", longString, messageTemplateInfo.getTitleCn());
        assertEquals("长字符串内容应该被正确设置", longString, messageTemplateInfo.getContentCn());
    }

    /**
     * 测试特殊字符处理
     */
    @Test
    public void testSpecialCharacters() {
        String specialChars = "特殊字符 @#$%^&*()_+-={}[]|\\:;\"'<>?,./~`";
        
        messageTemplateInfo.setTitleCn(specialChars);
        messageTemplateInfo.setContentCn(specialChars);
        
        assertEquals("特殊字符标题应该被正确设置", specialChars, messageTemplateInfo.getTitleCn());
        assertEquals("特殊字符内容应该被正确设置", specialChars, messageTemplateInfo.getContentCn());
    }

    /**
     * 测试JSON格式的字符串处理
     */
    @Test
    public void testJsonStringHandling() {
        String jsonString = "{\"key\": \"value\", \"number\": 123, \"boolean\": true}";
        
        messageTemplateInfo.setCardActions(jsonString);
        messageTemplateInfo.setImages(jsonString);
        
        assertEquals("JSON格式的卡片操作应该被正确设置", jsonString, messageTemplateInfo.getCardActions());
        assertEquals("JSON格式的图片信息应该被正确设置", jsonString, messageTemplateInfo.getImages());
    }

    /**
     * 测试多语言内容为null时的处理
     */
    @Test
    public void testI18nMethodsWithNullValues() {
        // 当内容为null时测试国际化方法
        String chineseContent = messageTemplateInfo.getI18nContent(I18nEnum.zh_cn);
        String englishContent = messageTemplateInfo.getI18nContent(I18nEnum.en_us);
        
        assertNull("中文内容为null时应该返回null", chineseContent);
        assertNull("英文内容为null时应该返回null", englishContent);
        
        String chineseTitle = messageTemplateInfo.getI18nTitle(I18nEnum.zh_cn);
        String englishTitle = messageTemplateInfo.getI18nTitle(I18nEnum.en_us);
        
        assertNull("中文标题为null时应该返回null", chineseTitle);
        assertNull("英文标题为null时应该返回null", englishTitle);
    }

    /**
     * 测试时间戳的合理性
     */
    @Test
    public void testTimestampReasonableness() {
        long currentTime = System.currentTimeMillis();
        MessageTemplateInfo newTemplate = MessageTemplateInfo.newCreateAndUpdateTimeInstant();
        
        // 验证时间戳在合理范围内（当前时间前后1秒内）
        assertTrue("创建时间应该在合理范围内", 
                   Math.abs(newTemplate.getCreateTime() - currentTime) < 1000);
        assertTrue("更新时间应该在合理范围内", 
                   Math.abs(newTemplate.getUpdateTime() - currentTime) < 1000);
    }

    /**
     * 测试Lombok生成的toString方法（间接测试）
     */
    @Test
    public void testToStringMethod() {
        messageTemplateInfo.setId(TEST_ID);
        messageTemplateInfo.setTitleCn(TEST_TITLE_CN);
        messageTemplateInfo.setAppId(TEST_APP_ID);
        
        String toString = messageTemplateInfo.toString();
        
        assertNotNull("toString方法应该返回非null值", toString);
        assertTrue("toString应该包含类名", toString.contains("MessageTemplateInfo"));
    }

    /**
     * 测试Lombok生成的equals和hashCode方法（间接测试）
     */
    @Test
    public void testEqualsAndHashCode() {
        MessageTemplateInfo template1 = new MessageTemplateInfo();
        MessageTemplateInfo template2 = new MessageTemplateInfo();
        
        // 测试相等性
        assertEquals("相同内容的对象应该相等", template1, template2);
        assertEquals("相同内容的对象hashCode应该相等", template1.hashCode(), template2.hashCode());
        
        // 设置不同内容后测试
        template1.setId(1L);
        template2.setId(2L);
        
        assertNotEquals("不同内容的对象不应该相等", template1, template2);
    }
}
