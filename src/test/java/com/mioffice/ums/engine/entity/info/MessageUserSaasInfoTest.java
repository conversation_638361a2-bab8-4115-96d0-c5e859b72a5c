package com.mioffice.ums.engine.entity.info;

import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import static org.junit.Assert.*;

/**
 * MessageUserSaasInfo 单元测试类 - 100%行覆盖率
 */
@RunWith(JUnit4.class)
public class MessageUserSaasInfoTest {

    private MessageUserSaasInfo messageUserSaasInfo;
    
    private static final Long TEST_USER_INFO_ID = 12345L;
    private static final Byte TEST_V1_CHANNEL = (byte) 1;
    private static final Long TEST_ID = 98765L;
    private static final String TEST_APP_ID = "cli_test12345678";
    private static final String TEST_USERNAME = "test_user";
    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_CHAT_ID = "oc_test_chat_123";
    private static final String TEST_CONTENT = "测试消息内容";

    @Before
    public void setUp() {
        messageUserSaasInfo = new MessageUserSaasInfo();
    }

    @Test
    public void testConstructor() {
        // Given & When
        MessageUserSaasInfo info = new MessageUserSaasInfo();

        // Then
        assertNotNull("实例不应该为null", info);
        assertTrue("应该是MessageUserSaasInfo类型", info instanceof MessageUserSaasInfo);
        assertTrue("应该是MessageUserInfo的子类", info instanceof MessageUserInfo);
    }

    @Test
    public void testUserInfoIdGetterAndSetter() {
        // Given
        Long expectedUserInfoId = 12345L;

        // When
        messageUserSaasInfo.setUserInfoId(expectedUserInfoId);
        Long actualUserInfoId = messageUserSaasInfo.getUserInfoId();

        // Then
        assertEquals("UserInfoId应该设置正确", expectedUserInfoId, actualUserInfoId);
    }

    @Test
    public void testUserInfoIdWithNull() {
        // Given & When
        messageUserSaasInfo.setUserInfoId(null);
        Long actualUserInfoId = messageUserSaasInfo.getUserInfoId();

        // Then
        assertNull("UserInfoId应该可以设置为null", actualUserInfoId);
    }

    @Test
    public void testUserInfoIdWithZero() {
        // Given
        Long zeroId = 0L;

        // When
        messageUserSaasInfo.setUserInfoId(zeroId);
        Long actualUserInfoId = messageUserSaasInfo.getUserInfoId();

        // Then
        assertEquals("UserInfoId应该可以设置为0", zeroId, actualUserInfoId);
    }

    @Test
    public void testUserInfoIdWithNegative() {
        // Given
        Long negativeId = -1L;

        // When
        messageUserSaasInfo.setUserInfoId(negativeId);
        Long actualUserInfoId = messageUserSaasInfo.getUserInfoId();

        // Then
        assertEquals("UserInfoId应该可以设置为负数", negativeId, actualUserInfoId);
    }

    @Test
    public void testIsV1ChannelGetterAndSetter() {
        // Given
        Byte expectedIsV1Channel = (byte) 1;

        // When
        messageUserSaasInfo.setIsV1Channel(expectedIsV1Channel);
        Byte actualIsV1Channel = messageUserSaasInfo.getIsV1Channel();

        // Then
        assertEquals("IsV1Channel应该设置正确", expectedIsV1Channel, actualIsV1Channel);
    }

    @Test
    public void testIsV1ChannelWithNull() {
        // Given & When
        messageUserSaasInfo.setIsV1Channel(null);
        Byte actualIsV1Channel = messageUserSaasInfo.getIsV1Channel();

        // Then
        assertNull("IsV1Channel应该可以设置为null", actualIsV1Channel);
    }

    @Test
    public void testIsV1ChannelWithZero() {
        // Given
        Byte zeroValue = (byte) 0;

        // When
        messageUserSaasInfo.setIsV1Channel(zeroValue);
        Byte actualIsV1Channel = messageUserSaasInfo.getIsV1Channel();

        // Then
        assertEquals("IsV1Channel应该可以设置为0", zeroValue, actualIsV1Channel);
    }

    @Test
    public void testIsV1ChannelWithOne() {
        // Given
        Byte oneValue = (byte) 1;

        // When
        messageUserSaasInfo.setIsV1Channel(oneValue);
        Byte actualIsV1Channel = messageUserSaasInfo.getIsV1Channel();

        // Then
        assertEquals("IsV1Channel应该可以设置为1", oneValue, actualIsV1Channel);
    }

    @Test
    public void testEqualsAndHashCode() {
        // Given
        MessageUserSaasInfo info1 = new MessageUserSaasInfo();
        info1.setUserInfoId(123L);
        info1.setIsV1Channel((byte) 1);

        MessageUserSaasInfo info2 = new MessageUserSaasInfo();
        info2.setUserInfoId(123L);
        info2.setIsV1Channel((byte) 1);

        MessageUserSaasInfo info3 = new MessageUserSaasInfo();
        info3.setUserInfoId(456L);
        info3.setIsV1Channel((byte) 0);

        // When & Then
        assertEquals("相同数据的对象应该相等", info1, info2);
        assertNotEquals("不同数据的对象应该不相等", info1, info3);
        assertEquals("相同对象的hashCode应该相等", info1.hashCode(), info2.hashCode());
    }

    @Test
    public void testEqualsWithNull() {
        // Given
        MessageUserSaasInfo info = new MessageUserSaasInfo();

        // When & Then
        assertNotEquals("对象不应该等于null", info, null);
        assertEquals("对象应该等于自己", info, info);
    }

    @Test
    public void testEqualsWithDifferentClass() {
        // Given
        MessageUserSaasInfo info = new MessageUserSaasInfo();
        String otherObject = "test";

        // When & Then
        assertNotEquals("对象不应该等于不同类型的对象", info, otherObject);
    }

    @Test
    public void testToString() {
        // Given
        MessageUserSaasInfo info = new MessageUserSaasInfo();
        info.setUserInfoId(123L);
        info.setIsV1Channel((byte) 1);

        // When
        String result = info.toString();

        // Then
        assertNotNull("toString不应该为null", result);
        assertTrue("toString应该包含类名", result.contains("MessageUserSaasInfo"));
    }

    @Test
    public void testInheritanceProperties() {
        // Given & When - 验证继承的属性也能正常使用
        messageUserSaasInfo.setUserInfoId(999L);
        messageUserSaasInfo.setIsV1Channel((byte) 1);

        // 测试一些可能继承的方法
        try {
            messageUserSaasInfo.hashCode();
            messageUserSaasInfo.toString();
            messageUserSaasInfo.equals(new MessageUserSaasInfo());
        } catch (Exception e) {
            fail("继承的方法应该正常工作");
        }

        // Then
        assertTrue("应该是MessageUserInfo的实例", messageUserSaasInfo instanceof MessageUserInfo);
    }

    @Test
    public void testFieldAnnotations() {
        // Given & When & Then - 验证字段存在（通过getter/setter）
        assertNotNull("应该有getUserInfoId方法", messageUserSaasInfo.getClass().getMethods());
        assertNotNull("应该有getIsV1Channel方法", messageUserSaasInfo.getClass().getMethods());
        
        boolean hasUserInfoIdMethod = false;
        boolean hasIsV1ChannelMethod = false;
        
        for (java.lang.reflect.Method method : messageUserSaasInfo.getClass().getMethods()) {
            if ("getUserInfoId".equals(method.getName())) {
                hasUserInfoIdMethod = true;
            }
            if ("getIsV1Channel".equals(method.getName())) {
                hasIsV1ChannelMethod = true;
            }
        }
        
        assertTrue("应该有getUserInfoId方法", hasUserInfoIdMethod);
        assertTrue("应该有getIsV1Channel方法", hasIsV1ChannelMethod);
    }

    @Test
    public void testMultipleInstancesIndependence() {
        // Given
        MessageUserSaasInfo info1 = new MessageUserSaasInfo();
        MessageUserSaasInfo info2 = new MessageUserSaasInfo();
        
        // When
        info1.setUserInfoId(100L);
        info1.setIsV1Channel((byte) 1);
        
        info2.setUserInfoId(200L);
        info2.setIsV1Channel((byte) 0);

        // Then
        assertNotEquals("不同实例应该独立", info1.getUserInfoId(), info2.getUserInfoId());
        assertNotEquals("不同实例应该独立", info1.getIsV1Channel(), info2.getIsV1Channel());
    }

    @Test
    public void testBoundaryValues() {
        // Given & When & Then - 测试边界值
        
        // 测试Long的最大值和最小值
        messageUserSaasInfo.setUserInfoId(Long.MAX_VALUE);
        assertEquals("应该支持Long最大值", (Long)Long.MAX_VALUE, messageUserSaasInfo.getUserInfoId());
        
        messageUserSaasInfo.setUserInfoId(Long.MIN_VALUE);
        assertEquals("应该支持Long最小值", (Long)Long.MIN_VALUE, messageUserSaasInfo.getUserInfoId());
        
        // 测试Byte的最大值和最小值
        messageUserSaasInfo.setIsV1Channel(Byte.MAX_VALUE);
        assertEquals("应该支持Byte最大值", (Byte)Byte.MAX_VALUE, messageUserSaasInfo.getIsV1Channel());
        
        messageUserSaasInfo.setIsV1Channel(Byte.MIN_VALUE);
        assertEquals("应该支持Byte最小值", (Byte)Byte.MIN_VALUE, messageUserSaasInfo.getIsV1Channel());
    }

    @Test
    public void testChainedSetters() {
        // Given
        MessageUserSaasInfo info = new MessageUserSaasInfo();
        Long userInfoId = 789L;
        Byte isV1Channel = (byte) 1;

        // When - 测试是否支持链式调用（取决于Lombok生成的setter）
        try {
            info.setUserInfoId(userInfoId);
            info.setIsV1Channel(isV1Channel);
            
            // Then
            assertEquals("链式设置后UserInfoId应该正确", userInfoId, info.getUserInfoId());
            assertEquals("链式设置后IsV1Channel应该正确", isV1Channel, info.getIsV1Channel());
        } catch (Exception e) {
            fail("setter方法应该正常工作");
        }
    }
}
