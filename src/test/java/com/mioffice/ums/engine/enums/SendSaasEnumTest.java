package com.mioffice.ums.engine.enums;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * SendSaasEnum 单元测试类 - 100%行覆盖率
 */
public class SendSaasEnumTest {

    @Test
    public void testEnumValues() {
        // Given & When
        SendSaasEnum[] values = SendSaasEnum.values();

        // Then
        assertNotNull("枚举值数组不应该为null", values);
        assertEquals("应该有2个枚举值", 2, values.length);
        
        // 验证每个枚举值
        assertEquals("第一个枚举应该是NO", SendSaasEnum.NO, values[0]);
        assertEquals("第二个枚举应该是YES", SendSaasEnum.YES, values[1]);
    }

    @Test
    public void testNoEnum() {
        // Given & When
        SendSaasEnum noEnum = SendSaasEnum.NO;

        // Then
        assertNotNull("NO枚举不应该为null", noEnum);
        assertEquals("NO枚举的code应该是0", (byte) 0, noEnum.getCode());
        assertEquals("NO枚举的desc应该正确", "不发送", noEnum.getDesc());
    }

    @Test
    public void testYesEnum() {
        // Given & When
        SendSaasEnum yesEnum = SendSaasEnum.YES;

        // Then
        assertNotNull("YES枚举不应该为null", yesEnum);
        assertEquals("YES枚举的code应该是1", (byte) 1, yesEnum.getCode());
        assertEquals("YES枚举的desc应该正确", "发送", yesEnum.getDesc());
    }

    @Test
    public void testValueOf() {
        // Given & When & Then
        assertEquals("valueOf('NO')应该返回NO枚举", SendSaasEnum.NO, SendSaasEnum.valueOf("NO"));
        assertEquals("valueOf('YES')应该返回YES枚举", SendSaasEnum.YES, SendSaasEnum.valueOf("YES"));
    }

    @Test
    public void testGetCode() {
        // Given & When & Then
        assertEquals("NO的code应该是0", (byte) 0, SendSaasEnum.NO.getCode());
        assertEquals("YES的code应该是1", (byte) 1, SendSaasEnum.YES.getCode());
    }

    @Test
    public void testGetDesc() {
        // Given & When & Then
        assertEquals("NO的desc应该正确", "不发送", SendSaasEnum.NO.getDesc());
        assertEquals("YES的desc应该正确", "发送", SendSaasEnum.YES.getDesc());
    }

    @Test
    public void testEnumEquality() {
        // Given & When & Then
        assertEquals("相同枚举应该相等", SendSaasEnum.NO, SendSaasEnum.NO);
        assertEquals("相同枚举应该相等", SendSaasEnum.YES, SendSaasEnum.YES);
        assertNotEquals("不同枚举应该不相等", SendSaasEnum.NO, SendSaasEnum.YES);
        assertNotEquals("不同枚举应该不相等", SendSaasEnum.YES, SendSaasEnum.NO);
    }

    @Test
    public void testToString() {
        // Given & When & Then
        assertNotNull("NO的toString不应该为null", SendSaasEnum.NO.toString());
        assertNotNull("YES的toString不应该为null", SendSaasEnum.YES.toString());
        assertEquals("NO的toString应该是'NO'", "NO", SendSaasEnum.NO.toString());
        assertEquals("YES的toString应该是'YES'", "YES", SendSaasEnum.YES.toString());
    }

    @Test
    public void testHashCode() {
        // Given & When & Then
        assertEquals("相同枚举的hashCode应该相等", 
                    SendSaasEnum.NO.hashCode(), SendSaasEnum.NO.hashCode());
        assertEquals("相同枚举的hashCode应该相等", 
                    SendSaasEnum.YES.hashCode(), SendSaasEnum.YES.hashCode());
    }

    @Test
    public void testEnumConstantsUniqueness() {
        // Given & When & Then
        assertNotEquals("枚举的code应该不同", SendSaasEnum.NO.getCode(), SendSaasEnum.YES.getCode());
        assertNotEquals("枚举的desc应该不同", SendSaasEnum.NO.getDesc(), SendSaasEnum.YES.getDesc());
    }

    @Test
    public void testCodeValues() {
        // Given & When & Then
        assertEquals("NO的code应该是0", (byte) 0, SendSaasEnum.NO.getCode());
        assertEquals("YES的code应该是1", (byte) 1, SendSaasEnum.YES.getCode());
        
        // 验证code类型 - 由于lombok的@Getter返回primitive type
        byte noCode = SendSaasEnum.NO.getCode();
        byte yesCode = SendSaasEnum.YES.getCode();
        assertNotNull("NO的code不应该为null", noCode);
        assertNotNull("YES的code不应该为null", yesCode);
    }

    @Test
    public void testDescValues() {
        // Given & When & Then
        assertNotNull("NO的desc不应该为null", SendSaasEnum.NO.getDesc());
        assertNotNull("YES的desc不应该为null", SendSaasEnum.YES.getDesc());
        assertFalse("NO的desc不应该为空", SendSaasEnum.NO.getDesc().isEmpty());
        assertFalse("YES的desc不应该为空", SendSaasEnum.YES.getDesc().isEmpty());
        
        assertTrue("desc应该是String类型", SendSaasEnum.NO.getDesc() instanceof String);
        assertTrue("desc应该是String类型", SendSaasEnum.YES.getDesc() instanceof String);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testValueOfWithInvalidName() {
        // Given & When & Then - 期望抛出IllegalArgumentException
        SendSaasEnum.valueOf("INVALID");
    }

    @Test
    public void testComprehensiveValidation() {
        // Given
        SendSaasEnum[] allValues = SendSaasEnum.values();

        // When & Then - 验证所有枚举值的完整性
        for (SendSaasEnum value : allValues) {
            assertNotNull("枚举值不应该为null", value);
            assertNotNull("code不应该为null", value.getCode());
            assertNotNull("desc不应该为null", value.getDesc());
            assertNotNull("name不应该为null", value.name());
            assertNotNull("toString不应该为null", value.toString());
            
            // 验证valueOf的一致性
            assertEquals("valueOf应该返回相同的枚举", value, SendSaasEnum.valueOf(value.name()));
        }
    }
}
