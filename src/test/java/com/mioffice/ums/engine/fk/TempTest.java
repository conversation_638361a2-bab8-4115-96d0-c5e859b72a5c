package com.mioffice.ums.engine.fk;

import cn.hutool.core.thread.ThreadUtil;
import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.entity.bo.ErrorTypeListBo;
import com.mioffice.ums.engine.enums.ErrorTypeEnum;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.template.TemplateParse;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.09.08
 */
public class TempTest extends BaseTest {

    @Autowired
    private TemplateParse templateParse;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @Test
    public void testMultiProcess() {

        for (int i = 1; i < 500; i++) {
            int finalI = i;
            ThreadUtil.execute(() -> {
                TemplateParse.TmpContent content = null;
                try {
                    System.out.println("---------------------------------------");
//                    content = templateParse.parseContent((long) finalI, new HashMap<>());
                    System.out.println(content);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
    }

    @Test
    public void testSelect() throws Exception {
//        List<Byte> typeList = Stream.of(ErrorTypeEnum.values()).map(ErrorTypeEnum::getType).collect(Collectors.toList());
//        List<ErrorTypeListBo> errorTypeListBos = messageUserInfoMapper.selectErrCountByExtraIdList(Collections.singletonList("6b72154a3c4a43efb98eb7cc9701e66a"), typeList);
//        System.out.println(errorTypeListBos);

        String content = templateParse.parseContent(1453L, new HashMap<>());
        System.out.println(content);
    }
}
