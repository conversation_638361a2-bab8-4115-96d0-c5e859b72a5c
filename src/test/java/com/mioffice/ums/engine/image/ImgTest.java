package com.mioffice.ums.engine.image;

import com.larksuite.appframework.sdk.exception.LarkClientException;
import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.exceptions.NotFoundRobotException;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020.11.09
 */
public class ImgTest  extends BaseTest {

    @Autowired
    private MiWorkRobotManager miWorkRobotManager;

    @Test
    public void testGetImgKey() throws Exception {
        String imgKey = miWorkRobotManager.uploadImageForLark("cli_9fbb8f537ab31062", "https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/5df0838b-dc74-4424-b004-6434bd9113fl");
        System.out.println(imgKey);
    }
}
