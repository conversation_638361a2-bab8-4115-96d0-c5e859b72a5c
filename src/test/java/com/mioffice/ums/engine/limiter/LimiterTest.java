package com.mioffice.ums.engine.limiter;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2020/8/26
 */
@Slf4j
public class LimiterTest {

    RateLimiter limiter = RateLimiter.create(100);

    @Test
    public void testSpeed() {
        for (int i = 0; i < 100; i++) {
            double acquire = limiter.acquire();
            log.info("{}", acquire * 1000);
        }
    }
}