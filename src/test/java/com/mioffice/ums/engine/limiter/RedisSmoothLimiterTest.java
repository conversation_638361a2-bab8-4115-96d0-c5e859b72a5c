package com.mioffice.ums.engine.limiter;

import com.mioffice.ums.engine.config.LuaScriptConfig;
import com.mioffice.ums.engine.entity.bo.RateConfigBO;
import com.mioffice.ums.engine.exceptions.LimitException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2020/8/26
 */
@Slf4j
public class RedisSmoothLimiterTest {

    static RedissonClient client;

    static {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://wcc.cache01.test.b2c.srv:22122")
                .setPassword("cn_info-application_cn_info-application_administration_notice_admin_svxri9jWvrSo")
                .setDatabase(0);
        client = Redisson.create(config);
    }

    @Test
    public void testLimiter() throws InterruptedException {
        LuaScriptConfig luaScriptConfig = new LuaScriptConfig();

        RedisRateLimiter limiter = new RedisRateLimiter(client, "app", luaScriptConfig.getScriptSha());

        List<Thread> threadGroup = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            int finalI = i;
            Thread thread = new Thread(() -> {
                for (int j = 0; j < 100; j++) {
                    try {
                        limiter.acquire(0);
                        log.info("limit {} success ----", finalI);
                    } catch (LimitException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }, "limit-" + finalI);
            threadGroup.add(thread);
        }
        limiter.setRate(RateConfigBO.builder().rate(1).rateMs(10 * 1000L).build());

        for (Thread thread : threadGroup) {
            thread.start();
        }

        TimeUnit.SECONDS.sleep(100);
        Assert.assertTrue(true);
    }
}
