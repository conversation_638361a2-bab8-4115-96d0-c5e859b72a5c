package com.mioffice.ums.engine.limiter;

import com.mioffice.ums.engine.config.WindowRateLimitScriptConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.concurrent.ConcurrentMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * WindowRateLimiterManager 测试类
 * 测试限流器管理器的缓存和配置功能
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
class WindowRateLimiterManagerTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private WindowRateLimitScriptConfig scriptConfig;

    private WindowRateLimiterManager limiterManager;

    @BeforeEach
    void setUp() {
        when(scriptConfig.getScript()).thenReturn("test-script");
        limiterManager = new WindowRateLimiterManager(redissonClient, scriptConfig);
    }

    /**
     * 测试场景1：获取默认配置的限流器
     * 
     * 验证点：
     * - 能正确创建限流器实例
     * - 缓存机制正常工作
     * - 相同key返回相同实例
     */
    @Test
    void testGetLimiter_DefaultConfig_Success() {
        // Given
        String limitKey = "test-key";

        // When
        WindowRateLimiter limiter1 = limiterManager.getLimiter(limitKey);
        WindowRateLimiter limiter2 = limiterManager.getLimiter(limitKey);

        // Then
        assertNotNull(limiter1);
        assertNotNull(limiter2);
        assertSame(limiter1, limiter2); // 相同实例
        assertEquals(1, limiterManager.getCacheSize());
    }

    /**
     * 测试场景2：获取自定义配置的限流器
     * 
     * 验证点：
     * - 能正确创建自定义配置的限流器
     * - 缓存机制正常工作
     * - 不同配置创建不同实例
     */
    @Test
    void testGetLimiter_CustomConfig_Success() {
        // Given
        String limitKey = "test-key";
        int secondLimit = 10;
        int minuteLimit = 100;

        // When
        WindowRateLimiter limiter1 = limiterManager.getLimiter(limitKey, secondLimit, minuteLimit);
        WindowRateLimiter limiter2 = limiterManager.getLimiter(limitKey, secondLimit, minuteLimit);

        // Then
        assertNotNull(limiter1);
        assertNotNull(limiter2);
        assertSame(limiter1, limiter2); // 相同实例
        assertEquals(1, limiterManager.getCacheSize());
    }

    /**
     * 测试场景3：不同配置创建不同限流器
     * 
     * 验证点：
     * - 相同key但不同配置创建不同实例
     * - 缓存key包含配置信息
     * - 缓存数量正确
     */
    @Test
    void testGetLimiter_DifferentConfigs_DifferentInstances() {
        // Given
        String limitKey = "test-key";
        
        // When
        WindowRateLimiter limiter1 = limiterManager.getLimiter(limitKey, 10, 100);
        WindowRateLimiter limiter2 = limiterManager.getLimiter(limitKey, 20, 200);
        WindowRateLimiter limiter3 = limiterManager.getLimiter(limitKey); // 默认配置

        // Then
        assertNotNull(limiter1);
        assertNotNull(limiter2);
        assertNotNull(limiter3);
        assertNotSame(limiter1, limiter2);
        assertNotSame(limiter1, limiter3);
        assertNotSame(limiter2, limiter3);
        assertEquals(3, limiterManager.getCacheSize());
    }

    /**
     * 测试场景4：清除指定key的限流器
     * 
     * 验证点：
     * - 能正确清除指定key的限流器
     * - 缓存数量正确减少
     * - 不影响其他限流器
     */
    @Test
    void testRemoveLimiter_Success() {
        // Given
        String limitKey1 = "test-key-1";
        String limitKey2 = "test-key-2";
        
        limiterManager.getLimiter(limitKey1);
        limiterManager.getLimiter(limitKey2);
        assertEquals(2, limiterManager.getCacheSize());

        // When
        limiterManager.removeLimiter(limitKey1);

        // Then
        assertEquals(1, limiterManager.getCacheSize());
        
        // 验证被移除的key需要重新创建
        WindowRateLimiter limiter1New = limiterManager.getLimiter(limitKey1);
        assertNotNull(limiter1New);
        assertEquals(2, limiterManager.getCacheSize());
    }

    /**
     * 测试场景5：清除所有限流器
     * 
     * 验证点：
     * - 能正确清除所有限流器
     * - 缓存数量变为0
     * - 清除后可以重新创建
     */
    @Test
    void testClearAll_Success() {
        // Given
        limiterManager.getLimiter("key1");
        limiterManager.getLimiter("key2");
        limiterManager.getLimiter("key3", 10, 100);
        assertEquals(3, limiterManager.getCacheSize());

        // When
        limiterManager.clearAll();

        // Then
        assertEquals(0, limiterManager.getCacheSize());
        
        // 验证清除后可以重新创建
        WindowRateLimiter newLimiter = limiterManager.getLimiter("key1");
        assertNotNull(newLimiter);
        assertEquals(1, limiterManager.getCacheSize());
    }

    /**
     * 测试场景6：获取缓存大小
     * 
     * 验证点：
     * - 缓存大小统计正确
     * - 随着创建和清除动态变化
     */
    @Test
    void testGetCacheSize_Accuracy() {
        // Given & When & Then
        assertEquals(0, limiterManager.getCacheSize());
        
        limiterManager.getLimiter("key1");
        assertEquals(1, limiterManager.getCacheSize());
        
        limiterManager.getLimiter("key2");
        assertEquals(2, limiterManager.getCacheSize());
        
        limiterManager.getLimiter("key1"); // 相同key，不增加
        assertEquals(2, limiterManager.getCacheSize());
        
        limiterManager.removeLimiter("key1");
        assertEquals(1, limiterManager.getCacheSize());
        
        limiterManager.clearAll();
        assertEquals(0, limiterManager.getCacheSize());
    }

    /**
     * 测试场景7：自定义配置的缓存key格式
     * 
     * 验证点：
     * - 自定义配置的缓存key包含配置参数
     * - 相同key不同配置不会冲突
     */
    @Test
    void testCustomConfigCacheKey_Format() {
        // Given
        String limitKey = "test";
        
        // When
        WindowRateLimiter limiter1 = limiterManager.getLimiter(limitKey, 10, 100);
        WindowRateLimiter limiter2 = limiterManager.getLimiter(limitKey, 20, 200);
        
        // Then
        assertNotSame(limiter1, limiter2);
        assertEquals(2, limiterManager.getCacheSize());
        
        // 验证缓存key格式（通过反射获取缓存映射）
        ConcurrentMap<String, WindowRateLimiter> cache = 
            (ConcurrentMap<String, WindowRateLimiter>) ReflectionTestUtils.getField(limiterManager, "limiterCache");
        
        assertTrue(cache.containsKey("test:10:100"));
        assertTrue(cache.containsKey("test:20:200"));
    }

    /**
     * 测试场景8：构造函数参数验证
     * 
     * 验证点：
     * - 构造函数正确设置依赖
     * - RedissonClient和ScriptConfig正确注入
     */
    @Test
    void testConstructor_DependencyInjection() {
        // Given & When
        WindowRateLimiterManager manager = new WindowRateLimiterManager(redissonClient, scriptConfig);

        // Then
        assertNotNull(manager);
        assertEquals(0, manager.getCacheSize());
    }

    /**
     * 测试场景9：空key处理
     * 
     * 验证点：
     * - 空key能正确处理
     * - 不会导致系统异常
     */
    @Test
    void testGetLimiter_EmptyKey_Success() {
        // Given & When
        WindowRateLimiter limiter1 = limiterManager.getLimiter("");

        // Then
        assertNotNull(limiter1);
        assertEquals(1, limiterManager.getCacheSize());
    }

    /**
     * 测试场景10：清除不存在的key
     * 
     * 验证点：
     * - 清除不存在的key不会报错
     * - 不影响现有缓存
     */
    @Test
    void testRemoveLimiter_NonExistentKey_NoError() {
        // Given
        limiterManager.getLimiter("existing-key");
        assertEquals(1, limiterManager.getCacheSize());

        // When
        limiterManager.removeLimiter("non-existent-key");

        // Then
        assertEquals(1, limiterManager.getCacheSize());
    }

    /**
     * 测试场景11：大量并发创建限流器
     * 
     * 验证点：
     * - 并发创建相同key的限流器返回相同实例
     * - 线程安全
     */
    @Test
    void testConcurrentGetLimiter_ThreadSafety() {
        // Given
        String limitKey = "concurrent-test";
        int threadCount = 10;
        WindowRateLimiter[] limiters = new WindowRateLimiter[threadCount];

        // When
        Thread[] threads = new Thread[threadCount];
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                limiters[index] = limiterManager.getLimiter(limitKey);
            });
        }

        for (Thread thread : threads) {
            thread.start();
        }

        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        assertEquals(1, limiterManager.getCacheSize());
        for (int i = 1; i < threadCount; i++) {
            assertSame(limiters[0], limiters[i]);
        }
    }

    /**
     * 测试场景12：边界值配置测试
     * 
     * 验证点：
     * - 边界值配置能正确处理
     * - 零值和负值配置
     */
    @Test
    void testGetLimiter_BoundaryValues() {
        // Given & When
        WindowRateLimiter limiter1 = limiterManager.getLimiter("test", 0, 0);
        WindowRateLimiter limiter2 = limiterManager.getLimiter("test", -1, -1);
        WindowRateLimiter limiter3 = limiterManager.getLimiter("test", Integer.MAX_VALUE, Integer.MAX_VALUE);

        // Then
        assertNotNull(limiter1);
        assertNotNull(limiter2);
        assertNotNull(limiter3);
        assertEquals(3, limiterManager.getCacheSize());
    }
}
