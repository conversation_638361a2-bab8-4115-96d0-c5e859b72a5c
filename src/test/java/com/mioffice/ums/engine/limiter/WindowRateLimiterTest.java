package com.mioffice.ums.engine.limiter;

import com.mioffice.ums.engine.config.WindowRateLimitScriptConfig;
import com.mioffice.ums.engine.exceptions.LimitException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 时间窗口限流器测试
 * 
 * <AUTHOR>
 * @since 2020/8/27
 */
@Slf4j
public class WindowRateLimiterTest {

    static RedissonClient client;

    static {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://wcc.cache01.test.b2c.srv:22122")
                .setPassword("cn_info-application_cn_info-application_administration_notice_admin_svxri9jWvrSo")
                .setDatabase(0);
        client = Redisson.create(config);
    }

    @Test
    public void testWindowRateLimiter() throws InterruptedException {
        WindowRateLimitScriptConfig scriptConfig = new WindowRateLimitScriptConfig();
        WindowRateLimiter limiter = new WindowRateLimiter(client, scriptConfig.getScript(), 5, 10);

        String appId = "test_app_" + System.currentTimeMillis();
        
        // 测试1秒内限制
        log.info("Testing second limit...");
        int successCount = 0;
        for (int i = 0; i < 10; i++) {
            try {
                limiter.tryAcquire(appId);
                successCount++;
                log.info("Request {} passed", i + 1);
            } catch (LimitException e) {
                log.info("Request {} blocked: {}", i + 1, e.getMessage());
            }
        }
        
        log.info("Success count in 1 second: {}", successCount);
        Assert.assertEquals("Should allow exactly 5 requests per second", 5, successCount);
        
        // 等待1秒后再测试
        Thread.sleep(1000);
        
        // 测试新的1秒窗口
        log.info("Testing new second window...");
        successCount = 0;
        for (int i = 0; i < 3; i++) {
            try {
                limiter.tryAcquire(appId);
                successCount++;
                log.info("Request {} passed in new window", i + 1);
            } catch (LimitException e) {
                log.info("Request {} blocked in new window: {}", i + 1, e.getMessage());
            }
        }
        
        log.info("Success count in new 1 second window: {}", successCount);
        Assert.assertEquals("Should allow 3 more requests in new second window", 3, successCount);
    }

    @Test
    public void testConcurrentRequests() throws InterruptedException {
        WindowRateLimitScriptConfig scriptConfig = new WindowRateLimitScriptConfig();
        WindowRateLimiter limiter = new WindowRateLimiter(client, scriptConfig.getScript(), 10, 50);

        String appId = "concurrent_test_" + System.currentTimeMillis();
        int threadCount = 20;
        int requestsPerThread = 10;
        
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        for (int i = 0; i < threadCount; i++) {
            new Thread(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    try {
                        limiter.tryAcquire(appId);
                        successCount.incrementAndGet();
                    } catch (LimitException e) {
                        failCount.incrementAndGet();
                    }
                }
                latch.countDown();
            }).start();
        }
        
        latch.await(10, TimeUnit.SECONDS);
        
        log.info("Concurrent test - Success: {}, Failed: {}", successCount.get(), failCount.get());
        Assert.assertTrue("Should have some successful requests", successCount.get() > 0);
        Assert.assertTrue("Should have some failed requests due to rate limiting", failCount.get() > 0);
        Assert.assertTrue("Success count should not exceed second limit", successCount.get() <= 10);
    }

    @Test
    public void testQuietMode() {
        WindowRateLimitScriptConfig scriptConfig = new WindowRateLimitScriptConfig();
        WindowRateLimiter limiter = new WindowRateLimiter(client, scriptConfig.getScript(), 2, 5);

        String appId = "quiet_test_" + System.currentTimeMillis();
        
        // 测试静默模式
        log.info("Testing quiet mode...");
        int successCount = 0;
        for (int i = 0; i < 5; i++) {
            boolean result = limiter.tryAcquireQuietly(appId);
            if (result) {
                successCount++;
                log.info("Request {} passed (quiet)", i + 1);
            } else {
                log.info("Request {} blocked (quiet)", i + 1);
            }
        }
        
        log.info("Success count in quiet mode: {}", successCount);
        Assert.assertEquals("Should allow exactly 2 requests per second in quiet mode", 2, successCount);
    }
} 