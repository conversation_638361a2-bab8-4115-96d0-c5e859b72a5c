package com.mioffice.ums.engine.manager;

import com.google.common.cache.LoadingCache;
import com.larksuite.appframework.sdk.LarkAppInstance;
import com.larksuite.appframework.sdk.client.LarkClient;
import com.larksuite.appframework.sdk.client.MessageDestination;
import com.larksuite.appframework.sdk.client.MessageDestinations;
import com.larksuite.appframework.sdk.client.message.CardMessage;
import com.larksuite.appframework.sdk.client.message.Message;
import com.mioffice.ums.engine.entity.bo.MiWorkJsonAndSdkRequestBO;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MsgFormatTypeEnum;
import com.mioffice.ums.engine.exceptions.NotFoundRobotException;
import com.mioffice.ums.engine.message.LarkSaasMessageHelper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.template.TemplateParse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LarkSaasV1RobotManager的单元测试类
 * 
 * <AUTHOR> Generated
 * @date 2024
 */
@RunWith(MockitoJUnitRunner.class)
public class LarkSaasV1RobotManagerTest {

    @InjectMocks
    private LarkSaasV1RobotManager larkSaasV1RobotManager;

    @Mock
    private LarkSaasMessageHelper larkSaasMessageHelper;

    @Mock
    private LoadingCache<String, Optional<MiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO>>> robotCache;

    @Mock
    private TemplateParse templateParse;

    @Mock
    private MiWorkRobot<MiWorkJsonAndSdkRequestBO, MiWorkResponseBO> miWorkRobot;

    @Mock
    private LarkAppInstance larkSaasAppInstance;

    @Mock
    private LarkClient larkClient;

    @Mock
    private CardMessage mockCardMessage;

    private String testAppId = "test_app_id";
    private String testMsgId = "test_msg_id";

    @Before
    public void setUp() throws Exception {
        // 由于LarkSaasV1RobotManager继承自MiWorkRobotManager，需要通过反射设置父类的字段
        Class<?> superClass = larkSaasV1RobotManager.getClass().getSuperclass();
        Field robotCacheField = superClass.getDeclaredField("robotCache");
        robotCacheField.setAccessible(true);
        robotCacheField.set(larkSaasV1RobotManager, robotCache);
        
        Field templateParseField = superClass.getDeclaredField("templateParse");
        templateParseField.setAccessible(true);
        templateParseField.set(larkSaasV1RobotManager, templateParse);
    }

    /**
     * 测试sendMsg方法 - SDK格式消息发送成功
     */
    @Test
    public void testSendMsg_SdkFormat_Success() throws Exception {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMessageUserInfo();
        MiWorkRobot.Session session = createSession();
        MessageTemplateInfo templateInfo = createTemplateInfo(MsgFormatTypeEnum.SDK.getType());
        MessageDestination messageDestination = mock(MessageDestination.class);

        // 设置mock行为
        when(robotCache.get(testAppId)).thenReturn(Optional.of(miWorkRobot));
        when(templateParse.get(messageUserInfo.getMsgTmpId())).thenReturn(templateInfo);
        when(larkSaasMessageHelper.parseIi8nSdkV1(messageUserInfo)).thenReturn(mockCardMessage);
        when(session.toMessageDestination()).thenReturn(messageDestination);
        when(miWorkRobot.getLarkSaasAppInstance()).thenReturn(larkSaasAppInstance);
        when(larkSaasAppInstance.getLarkClient()).thenReturn(larkClient);
        when(larkClient.sendChatMessageV1(eq(messageDestination), eq(mockCardMessage))).thenReturn(testMsgId);

        // 执行测试
        MiWorkResponseBO result = larkSaasV1RobotManager.sendMsg(testAppId, messageUserInfo, session);

        // 验证结果
        assertNotNull("返回结果不应为null", result);
        assertEquals("消息ID应该匹配", testMsgId, result.getMsgId());

        // 验证方法调用
        verify(larkSaasMessageHelper).parseIi8nSdkV1(messageUserInfo);
        verify(larkClient).sendChatMessageV1(messageDestination, mockCardMessage);
    }

    /**
     * 测试sendMsg方法 - JSON格式消息发送成功（使用UserId）
     */
    @Test
    public void testSendMsg_JsonFormat_Success_WithUserId() throws Exception {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMessageUserInfo();
        MiWorkRobot.Session session = createSession();
        MessageTemplateInfo templateInfo = createTemplateInfo(MsgFormatTypeEnum.JSON.getType());
        MessageDestination messageDestination = mock(MessageDestinations.UserId.class);
        String testJsonMessage = "{\"msg_type\":\"interactive\",\"card\":{\"elements\":[{\"tag\":\"div\",\"text\":{\"content\":\"test message\",\"tag\":\"plain_text\"}}]}}";

        // 设置mock行为
        when(robotCache.get(testAppId)).thenReturn(Optional.of(miWorkRobot));
        when(templateParse.get(messageUserInfo.getMsgTmpId())).thenReturn(templateInfo);
        when(larkSaasMessageHelper.parseJson(messageUserInfo)).thenReturn(testJsonMessage);
        when(session.toMessageDestination()).thenReturn(messageDestination);
        when(messageDestination.identity()).thenReturn("test_user_id");
        when(miWorkRobot.getLarkSaasAppInstance()).thenReturn(larkSaasAppInstance);
        when(larkSaasAppInstance.getLarkClient()).thenReturn(larkClient);
        when(larkClient.sendChatMessageV1(anyString(), eq("user_id"))).thenReturn(testMsgId);

        // 执行测试
        MiWorkResponseBO result = larkSaasV1RobotManager.sendMsg(testAppId, messageUserInfo, session);

        // 验证结果
        assertNotNull("返回结果不应为null", result);
        assertEquals("消息ID应该匹配", testMsgId, result.getMsgId());

        // 验证方法调用
        verify(larkSaasMessageHelper).parseJson(messageUserInfo);
        verify(larkClient).sendChatMessageV1(anyString(), eq("user_id"));
    }

    /**
     * 测试sendMsg方法 - JSON格式消息发送成功（使用ChatId）
     */
    @Test
    public void testSendMsg_JsonFormat_Success_WithChatId() throws Exception {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMessageUserInfo();
        MiWorkRobot.Session session = createSession();
        MessageTemplateInfo templateInfo = createTemplateInfo(MsgFormatTypeEnum.JSON.getType());
        MessageDestination messageDestination = mock(MessageDestinations.ChatId.class);
        String testJsonMessage = "{\"msg_type\":\"interactive\",\"card\":{\"elements\":[{\"tag\":\"div\",\"text\":{\"content\":\"test message\",\"tag\":\"plain_text\"}}]}}";

        // 设置mock行为
        when(robotCache.get(testAppId)).thenReturn(Optional.of(miWorkRobot));
        when(templateParse.get(messageUserInfo.getMsgTmpId())).thenReturn(templateInfo);
        when(larkSaasMessageHelper.parseJson(messageUserInfo)).thenReturn(testJsonMessage);
        when(session.toMessageDestination()).thenReturn(messageDestination);
        when(messageDestination.identity()).thenReturn("test_chat_id");
        when(miWorkRobot.getLarkSaasAppInstance()).thenReturn(larkSaasAppInstance);
        when(larkSaasAppInstance.getLarkClient()).thenReturn(larkClient);
        when(larkClient.sendChatMessageV1(anyString(), eq("chat_id"))).thenReturn(testMsgId);

        // 执行测试
        MiWorkResponseBO result = larkSaasV1RobotManager.sendMsg(testAppId, messageUserInfo, session);

        // 验证结果
        assertNotNull("返回结果不应为null", result);
        assertEquals("消息ID应该匹配", testMsgId, result.getMsgId());

        // 验证方法调用
        verify(larkSaasMessageHelper).parseJson(messageUserInfo);
        verify(larkClient).sendChatMessageV1(anyString(), eq("chat_id"));
    }

    /**
     * 测试sendMsg方法 - 机器人不存在的情况
     */
    @Test(expected = NotFoundRobotException.class)
    public void testSendMsg_RobotNotFound_ThrowsException() throws Exception {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMessageUserInfo();
        MiWorkRobot.Session session = createSession();

        // 设置mock行为 - robotCache返回空
        when(robotCache.get(testAppId)).thenReturn(Optional.empty());

        // 执行测试 - 应该抛出异常
        larkSaasV1RobotManager.sendMsg(testAppId, messageUserInfo, session);
    }

    /**
     * 测试sendMsg方法 - 默认格式（兼容老版sdk）
     */
    @Test
    public void testSendMsg_DefaultFormat_FallbackToSdk() throws Exception {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMessageUserInfo();
        MiWorkRobot.Session session = createSession();
        MessageTemplateInfo templateInfo = createTemplateInfo((byte) 0); // 未知格式
        MessageDestination messageDestination = mock(MessageDestination.class);

        // 设置mock行为
        when(robotCache.get(testAppId)).thenReturn(Optional.of(miWorkRobot));
        when(templateParse.get(messageUserInfo.getMsgTmpId())).thenReturn(templateInfo);
        when(larkSaasMessageHelper.parseIi8nSdkV1(messageUserInfo)).thenReturn(mockCardMessage);
        when(session.toMessageDestination()).thenReturn(messageDestination);
        when(miWorkRobot.getLarkSaasAppInstance()).thenReturn(larkSaasAppInstance);
        when(larkSaasAppInstance.getLarkClient()).thenReturn(larkClient);
        when(larkClient.sendChatMessageV1(eq(messageDestination), eq(mockCardMessage))).thenReturn(testMsgId);

        // 执行测试
        MiWorkResponseBO result = larkSaasV1RobotManager.sendMsg(testAppId, messageUserInfo, session);

        // 验证结果
        assertNotNull("返回结果不应为null", result);
        assertEquals("消息ID应该匹配", testMsgId, result.getMsgId());

        // 验证使用SDK格式作为默认
        verify(larkSaasMessageHelper).parseIi8nSdkV1(messageUserInfo);
    }

    /**
     * 测试setter方法
     */
    @Test
    public void testSetLarkSaasMessageHelper() {
        // 准备测试数据
        LarkSaasMessageHelper newHelper = mock(LarkSaasMessageHelper.class);
        
        // 创建一个新的LarkSaasV1RobotManager实例
        LarkSaasV1RobotManager manager = new LarkSaasV1RobotManager();
        
        // 执行setter方法
        manager.setLarkSaasMessageHelper(newHelper);
        
        // 验证setter正常工作 - 由于字段是私有的，这里仅验证方法能正常执行
        assertNotNull("Manager不应为null", manager);
    }

    /**
     * 测试各种边界情况
     */
    @Test
    public void testSendMsg_EdgeCases() throws Exception {
        // 测试null消息模板类型的情况
        MessageUserInfo messageUserInfo = createMessageUserInfo();
        MiWorkRobot.Session session = createSession();
        MessageTemplateInfo templateInfo = createTemplateInfo(null); // null格式类型
        MessageDestination messageDestination = mock(MessageDestination.class);

        // 设置mock行为
        when(robotCache.get(testAppId)).thenReturn(Optional.of(miWorkRobot));
        when(templateParse.get(messageUserInfo.getMsgTmpId())).thenReturn(templateInfo);
        when(larkSaasMessageHelper.parseIi8nSdkV1(messageUserInfo)).thenReturn(mockCardMessage);
        when(session.toMessageDestination()).thenReturn(messageDestination);
        when(miWorkRobot.getLarkSaasAppInstance()).thenReturn(larkSaasAppInstance);
        when(larkSaasAppInstance.getLarkClient()).thenReturn(larkClient);
        when(larkClient.sendChatMessageV1(eq(messageDestination), eq(mockCardMessage))).thenReturn(testMsgId);

        // 执行测试 - 应该默认使用SDK格式
        MiWorkResponseBO result = larkSaasV1RobotManager.sendMsg(testAppId, messageUserInfo, session);

        assertNotNull("返回结果不应为null", result);
        verify(larkSaasMessageHelper).parseIi8nSdkV1(messageUserInfo);
    }

    // ========== 辅助方法 ==========

    /**
     * 创建测试用的MessageUserInfo
     */
    private MessageUserInfo createMessageUserInfo() {
        MessageUserInfo userInfo = new MessageUserInfo();
        userInfo.setMsgTmpId(1L);
        userInfo.setPlaceholderContent("{\"name\":\"test\"}");
        return userInfo;
    }

    /**
     * 创建测试用的Session
     */
    private MiWorkRobot.Session createSession() {
        return mock(MiWorkRobot.Session.class);
    }

    /**
     * 创建测试用的MessageTemplateInfo
     */
    private MessageTemplateInfo createTemplateInfo(Byte msgFormatType) {
        MessageTemplateInfo templateInfo = new MessageTemplateInfo();
        templateInfo.setMsgFormatType(msgFormatType);
        return templateInfo;
    }
}
