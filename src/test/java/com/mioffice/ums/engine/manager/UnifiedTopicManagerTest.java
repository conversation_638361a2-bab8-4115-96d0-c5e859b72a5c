package com.mioffice.ums.engine.manager;

import com.mioffice.ums.engine.config.MqProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * UnifiedTopicManager 测试类
 * 测试统一主题管理器的配置管理、白名单判断等功能
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
class UnifiedTopicManagerTest {

    @Mock
    private MqProperties mqProperties;

    @Mock
    private MqProperties.UnifiedTopic unifiedTopic;

    @InjectMocks
    private UnifiedTopicManager unifiedTopicManager;

    private Set<String> testRobotIds;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testRobotIds = new HashSet<>();
        testRobotIds.add("robot1");
        testRobotIds.add("robot2");
        testRobotIds.add("robot3");

        // 设置mock对象
        when(mqProperties.getUnifiedTopic()).thenReturn(unifiedTopic);
        when(unifiedTopic.isEnabled()).thenReturn(true);
        when(unifiedTopic.getName()).thenReturn("unified-topic");
        when(unifiedTopic.getRobotIds()).thenReturn(testRobotIds);
    }

    /**
     * 测试场景1：统一Topic功能启用时，白名单机器人应该返回true
     * 
     * 验证点：
     * - 统一Topic功能已启用
     * - 机器人ID在白名单中
     * - 返回true
     */
    @Test
    void testShouldUseUnifiedTopic_EnabledAndInWhitelist() {
        // Given
        String robotId = "robot1";

        // When
        boolean result = unifiedTopicManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertTrue(result);
        verify(unifiedTopic).isEnabled();
        verify(unifiedTopic).getRobotIds();
    }

    /**
     * 测试场景2：统一Topic功能启用时，非白名单机器人应该返回false
     * 
     * 验证点：
     * - 统一Topic功能已启用
     * - 机器人ID不在白名单中
     * - 返回false
     */
    @Test
    void testShouldUseUnifiedTopic_EnabledButNotInWhitelist() {
        // Given
        String robotId = "robot-not-in-whitelist";

        // When
        boolean result = unifiedTopicManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertFalse(result);
        verify(unifiedTopic).isEnabled();
        verify(unifiedTopic).getRobotIds();
    }

    /**
     * 测试场景3：统一Topic功能未启用时，应该返回false
     * 
     * 验证点：
     * - 统一Topic功能未启用
     * - 不管机器人ID是否在白名单中都返回false
     * - 不需要检查白名单
     */
    @Test
    void testShouldUseUnifiedTopic_Disabled() {
        // Given
        when(unifiedTopic.isEnabled()).thenReturn(false);
        String robotId = "robot1";

        // When
        boolean result = unifiedTopicManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertFalse(result);
        verify(unifiedTopic).isEnabled();
        verify(unifiedTopic, never()).getRobotIds();
    }

    /**
     * 测试场景4：机器人ID为空时，应该返回false
     * 
     * 验证点：
     * - 机器人ID为null或空字符串
     * - 返回false
     */
    @Test
    void testShouldUseUnifiedTopic_EmptyRobotId() {
        // Given & When & Then
        assertFalse(unifiedTopicManager.shouldUseUnifiedTopic(null));
        assertFalse(unifiedTopicManager.shouldUseUnifiedTopic(""));
        assertFalse(unifiedTopicManager.shouldUseUnifiedTopic("   "));

        // 验证所有情况都会检查功能是否启用
        verify(unifiedTopic, times(3)).isEnabled();
        verify(unifiedTopic, never()).getRobotIds();
    }

    /**
     * 测试场景5：获取统一Topic名称
     * 
     * 验证点：
     * - 正确返回配置的统一Topic名称
     */
    @Test
    void testGetUnifiedTopicName() {
        // Given
        String expectedTopicName = "unified-topic";

        // When
        String result = unifiedTopicManager.getUnifiedTopicName();

        // Then
        assertEquals(expectedTopicName, result);
        verify(unifiedTopic).getName();
    }

    /**
     * 测试场景6：检查统一Topic是否启用
     * 
     * 验证点：
     * - 正确返回统一Topic启用状态
     */
    @Test
    void testIsUnifiedTopicEnabled_True() {
        // Given
        when(unifiedTopic.isEnabled()).thenReturn(true);

        // When
        boolean result = unifiedTopicManager.isUnifiedTopicEnabled();

        // Then
        assertTrue(result);
        verify(unifiedTopic).isEnabled();
    }

    /**
     * 测试场景7：检查统一Topic是否启用 - 禁用状态
     * 
     * 验证点：
     * - 正确返回统一Topic禁用状态
     */
    @Test
    void testIsUnifiedTopicEnabled_False() {
        // Given
        when(unifiedTopic.isEnabled()).thenReturn(false);

        // When
        boolean result = unifiedTopicManager.isUnifiedTopicEnabled();

        // Then
        assertFalse(result);
        verify(unifiedTopic).isEnabled();
    }

    /**
     * 测试场景8：获取统一Topic机器人ID集合
     * 
     * 验证点：
     * - 返回正确的机器人ID集合
     * - 返回的是副本，不是原集合
     */
    @Test
    void testGetUnifiedTopicRobotIds() {
        // Given
        Set<String> expectedRobotIds = new HashSet<>(testRobotIds);

        // When
        Set<String> result = unifiedTopicManager.getUnifiedTopicRobotIds();

        // Then
        assertEquals(expectedRobotIds, result);
        assertEquals(3, result.size());
        assertTrue(result.contains("robot1"));
        assertTrue(result.contains("robot2"));
        assertTrue(result.contains("robot3"));
        verify(unifiedTopic).getRobotIds();
    }

    /**
     * 测试场景9：修改返回的机器人ID集合不影响原集合
     * 
     * 验证点：
     * - 返回的集合是副本
     * - 修改返回的集合不影响内部状态
     */
    @Test
    void testGetUnifiedTopicRobotIds_ReturnsCopy() {
        // Given
        Set<String> originalRobotIds = new HashSet<>(testRobotIds);

        // When
        Set<String> result = unifiedTopicManager.getUnifiedTopicRobotIds();
        result.add("new-robot");

        // Then
        assertEquals(4, result.size());
        assertTrue(result.contains("new-robot"));
        
        // 原集合不受影响
        Set<String> result2 = unifiedTopicManager.getUnifiedTopicRobotIds();
        assertEquals(3, result2.size());
        assertFalse(result2.contains("new-robot"));
    }

    /**
     * 测试场景10：空白名单的情况
     * 
     * 验证点：
     * - 白名单为空时，任何机器人ID都不应该使用统一Topic
     */
    @Test
    void testShouldUseUnifiedTopic_EmptyWhitelist() {
        // Given
        when(unifiedTopic.getRobotIds()).thenReturn(new HashSet<>());
        String robotId = "any-robot";

        // When
        boolean result = unifiedTopicManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertFalse(result);
        verify(unifiedTopic).isEnabled();
        verify(unifiedTopic).getRobotIds();
    }

    /**
     * 测试场景11：初始化方法的日志记录
     * 
     * 验证点：
     * - 初始化时正确调用配置方法
     */
    @Test
    void testInit() {
        // Given
        UnifiedTopicManager manager = new UnifiedTopicManager();
        ReflectionTestUtils.setField(manager, "mqProperties", mqProperties);

        // When
        manager.init();

        // Then
        verify(unifiedTopic).isEnabled();
        verify(unifiedTopic).getName();
        verify(unifiedTopic).getRobotIds();
    }

    /**
     * 测试场景12：大小写敏感的机器人ID匹配
     * 
     * 验证点：
     * - 机器人ID匹配是大小写敏感的
     */
    @Test
    void testShouldUseUnifiedTopic_CaseSensitive() {
        // Given
        String robotId = "Robot1"; // 大写R，而白名单中是robot1

        // When
        boolean result = unifiedTopicManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertFalse(result);
        verify(unifiedTopic).isEnabled();
        verify(unifiedTopic).getRobotIds();
    }

    /**
     * 测试场景13：包含空格的机器人ID
     * 
     * 验证点：
     * - 包含空格的机器人ID正确处理
     */
    @Test
    void testShouldUseUnifiedTopic_WithSpaces() {
        // Given
        testRobotIds.add("robot with spaces");
        String robotId = "robot with spaces";

        // When
        boolean result = unifiedTopicManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertTrue(result);
        verify(unifiedTopic).isEnabled();
        verify(unifiedTopic).getRobotIds();
    }
} 