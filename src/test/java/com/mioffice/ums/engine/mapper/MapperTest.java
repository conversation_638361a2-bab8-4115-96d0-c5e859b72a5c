package com.mioffice.ums.engine.mapper;

import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


@Slf4j
public class MapperTest extends BaseTest {

    @Autowired
    MessageUserInfoMapper messageUserInfoMapper;

    @Test
    public void messageMapperTest() {
        MessageUserInfo messageUserInfo = messageUserInfoMapper.selectByIdFromMaster(34167386L);
        log.info(messageUserInfo.getMessageStatus().toString());
    }
}
