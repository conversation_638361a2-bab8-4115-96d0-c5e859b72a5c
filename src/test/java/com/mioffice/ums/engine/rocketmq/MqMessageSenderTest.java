package com.mioffice.ums.engine.rocketmq;

import api.ClientFactory;
import api.producer.NormalProducer;
import com.mioffice.ums.engine.config.RocketMqProperties;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * MqMessageSender 测试类
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class MqMessageSenderTest {

    @Mock
    private RocketMqProperties rocketMqProperties;

    @InjectMocks
    private MqMessageSender mqMessageSender;

    @BeforeEach
    void setUp() {
        // 设置RocketMQ配置
        when(rocketMqProperties.getAk()).thenReturn("test-ak");
        when(rocketMqProperties.getSk()).thenReturn("test-sk");
        when(rocketMqProperties.getNameServer()).thenReturn("localhost:9876");

        mqMessageSender.init();
    }

    @Test
    void testSendToTopic_SingleMessage() {
        // Given
        String topicName = "test-topic";
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();

        // When
        boolean result = mqMessageSender.sendToTopic(topicName, messageUserInfo);

        // Then
        // 由于没有真实的RocketMQ环境，这里主要测试方法调用不会抛出异常
        // 在实际环境中，需要配置真实的RocketMQ进行集成测试
        assertNotNull(mqMessageSender);
    }

    @Test
    void testSendToTopic_MultipleMessages() {
        // Given
        String topicName = "test-topic";
        List<MessageUserInfo> messageUserInfoList = Arrays.asList(
                createTestMessageUserInfo(),
                createTestMessageUserInfo()
        );

        // When
        boolean result = mqMessageSender.sendToTopic(topicName, messageUserInfoList);

        // Then
        // 验证方法调用不会抛出异常
        assertNotNull(mqMessageSender);
    }

    private MessageUserInfo createTestMessageUserInfo() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setId(1L);
        messageUserInfo.setAppId("test-app");
        messageUserInfo.setChannel((byte) 1); // MI_WORK
        messageUserInfo.setExtraId("test-extra");
        return messageUserInfo;
    }
} 