package com.mioffice.ums.engine.rocketmq;

import com.mioffice.ums.engine.cache.TopicCache;
import com.mioffice.ums.engine.config.RocketMqProperties;
import com.mioffice.ums.engine.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.engine.entity.info.EmailRobotInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.RobotInfo;
import com.mioffice.ums.engine.entity.info.SmsRobotInfo;
import com.mioffice.ums.engine.manager.UnifiedTopicManager;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ProducerAndConsumerManager 优化后测试类
 * 验证使用MqMessageSender统一管理消息发送的功能
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
public class ProducerAndConsumerManagerOptimizedTest {

    @Mock
    private RocketMqProperties rocketMqProperties;

    @Mock
    private TopicCache topicCache;

    @Mock
    private RobotInfoMapper robotInfoMapper;

    @Mock
    private EmailRobotInfoMapper emailRobotInfoMapper;

    @Mock
    private SmsRobotInfoMapper smsRobotInfoMapper;

    @Mock
    private UnifiedTopicManager unifiedTopicManager;

    @Mock
    private UniversalMessageConsumer universalMessageConsumer;

    @Mock
    private MqMessageSender mqMessageSender;

    @InjectMocks
    private ProducerAndConsumerManager producerAndConsumerManager;

    private List<MessageUserInfo> testMessageList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testMessageList = createTestMessageList();
    }

    /**
     * 测试发送消息到统一Topic
     * 验证使用MqMessageSender发送消息
     */
    @Test
    void testSendToUnifiedTopic() {
        // Given
        String unifiedTopicName = "unified-topic";
        when(unifiedTopicManager.getUnifiedTopicName()).thenReturn(unifiedTopicName);
        when(mqMessageSender.sendToTopic(unifiedTopicName, testMessageList)).thenReturn(true);

        // When
        boolean result = producerAndConsumerManager.sendToUnifiedTopic(testMessageList);

        // Then
        assertTrue(result);
        verify(mqMessageSender).sendToTopic(unifiedTopicName, testMessageList);
        verify(unifiedTopicManager).getUnifiedTopicName();
    }

    /**
     * 测试发送消息到专属Topic
     * 验证使用MqMessageSender发送消息
     */
    @Test
    void testSendToSpecificTopic() {
        // Given
        String topicName = "specific-topic";
        when(mqMessageSender.sendToTopic(topicName, testMessageList)).thenReturn(true);

        // When
        boolean result = producerAndConsumerManager.sendToSpecificTopic(topicName, testMessageList);

        // Then
        assertTrue(result);
        verify(mqMessageSender).sendToTopic(topicName, testMessageList);
    }

    /**
     * 测试发送消息失败的情况
     */
    @Test
    void testSendToTopic_Failure() {
        // Given
        String topicName = "test-topic";
        when(mqMessageSender.sendToTopic(topicName, testMessageList)).thenReturn(false);

        // When
        boolean result = producerAndConsumerManager.sendToSpecificTopic(topicName, testMessageList);

        // Then
        assertFalse(result);
        verify(mqMessageSender).sendToTopic(topicName, testMessageList);
    }

    /**
     * 测试统一Topic功能启用判断
     */
    @Test
    void testIsUnifiedTopicEnabled() {
        // Given
        when(unifiedTopicManager.isUnifiedTopicEnabled()).thenReturn(true);

        // When
        boolean result = producerAndConsumerManager.isUnifiedTopicEnabled();

        // Then
        assertTrue(result);
        verify(unifiedTopicManager).isUnifiedTopicEnabled();
    }

    /**
     * 测试系统ID是否应该使用统一Topic
     */
    @Test
    void testShouldUseUnifiedTopic() {
        // Given
        String systemId = "test-system";
        when(unifiedTopicManager.shouldUseUnifiedTopic(systemId)).thenReturn(true);

        // When
        boolean result = producerAndConsumerManager.shouldUseUnifiedTopic(systemId);

        // Then
        assertTrue(result);
        verify(unifiedTopicManager).shouldUseUnifiedTopic(systemId);
    }

    private List<MessageUserInfo> createTestMessageList() {
        MessageUserInfo message1 = new MessageUserInfo();
        message1.setId(1L);
        message1.setAppId("test-app-1");
        message1.setChannel((byte) 1);
        message1.setExtraId("test-extra-1");

        MessageUserInfo message2 = new MessageUserInfo();
        message2.setId(2L);
        message2.setAppId("test-app-2");
        message2.setChannel((byte) 1);
        message2.setExtraId("test-extra-2");

        return Arrays.asList(message1, message2);
    }
} 