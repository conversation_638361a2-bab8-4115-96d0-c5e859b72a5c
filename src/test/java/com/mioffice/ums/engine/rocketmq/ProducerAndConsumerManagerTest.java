package com.mioffice.ums.engine.rocketmq;

import com.mioffice.ums.engine.cache.TopicCache;
import com.mioffice.ums.engine.config.RocketMqProperties;
import com.mioffice.ums.engine.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.engine.entity.info.EmailRobotInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.RobotInfo;
import com.mioffice.ums.engine.entity.info.SmsRobotInfo;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.limiter.LimiterManager;
import com.mioffice.ums.engine.manager.UnifiedTopicManager;
import com.mioffice.ums.engine.mapper.EmailRobotInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.mapper.RobotInfoMapper;
import com.mioffice.ums.engine.mapper.SmsRobotInfoMapper;
import com.mioffice.ums.engine.sender.EmailMessageSender;
import com.mioffice.ums.engine.sender.MiWorkMessageSenderV1;
import com.mioffice.ums.engine.sender.SmsMessageSender;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ProducerAndConsumerManager 测试类
 * 
 * 测试范围：
 * - 路由决策功能（统一Topic判断、系统队列获取、机器人队列获取）
 * - 配置管理功能（统一Topic配置、机器人队列配置）
 * - 基础功能验证
 */
@ExtendWith(MockitoExtension.class)
public class ProducerAndConsumerManagerTest {

    @Mock
    private RocketMqProperties rocketMqProperties;

    @Mock
    private TopicCache topicCache;

    @Mock
    private RobotInfoMapper robotInfoMapper;

    @Mock
    private EmailRobotInfoMapper emailRobotInfoMapper;

    @Mock
    private SmsRobotInfoMapper smsRobotInfoMapper;

    @Mock
    private MessageUserInfoMapper messageUserInfoMapper;

    @Mock
    private LimiterManager limiterManager;

    @Mock
    private MiWorkMessageSenderV1 miWorkMessageSenderV1;

    @Mock
    private EmailMessageSender emailMessageSender;

    @Mock
    private SmsMessageSender smsMessageSender;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private UnifiedTopicManager unifiedTopicManager;

    @Mock
    private UniversalMessageConsumer universalMessageConsumer;

    @InjectMocks
    private ProducerAndConsumerManager producerAndConsumerManager;

    private List<MessageUserInfo> testMessageList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testMessageList = createTestMessageList();
        
        // 移除不存在的字段设置，ProducerAndConsumerManager中没有这些字段
    }

    /**
     * 测试场景1：统一Topic使用判断 - 启用且在白名单
     * 
     * 场景描述：
     * - 统一Topic功能已启用
     * - 指定的机器人ID在白名单中
     * - 应该返回true表示该机器人应使用统一Topic
     * 
     * 验证点：
     * - shouldUseUnifiedTopic方法返回true
     * - 正确调用UnifiedTopicManager的判断逻辑
     */
    @Test
    void testShouldUseUnifiedTopicEnabled() {
        // Given
        String robotId = "test-robot";
        when(unifiedTopicManager.shouldUseUnifiedTopic(robotId)).thenReturn(true);

        // When
        boolean result = producerAndConsumerManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertTrue(result);
        verify(unifiedTopicManager).shouldUseUnifiedTopic(robotId);
    }

    /**
     * 测试场景2：统一Topic使用判断 - 未启用或不在白名单
     * 
     * 场景描述：
     * - 统一Topic功能未启用或机器人ID不在白名单中
     * - 应该返回false表示该机器人不应使用统一Topic
     * - 系统会回退到其他路由方式
     * 
     * 验证点：
     * - shouldUseUnifiedTopic方法返回false
     * - 正确调用UnifiedTopicManager的判断逻辑
     */
    @Test
    void testShouldUseUnifiedTopicDisabled() {
        // Given
        String robotId = "test-robot";
        when(unifiedTopicManager.shouldUseUnifiedTopic(robotId)).thenReturn(false);

        // When
        boolean result = producerAndConsumerManager.shouldUseUnifiedTopic(robotId);

        // Then
        assertFalse(result);
        verify(unifiedTopicManager).shouldUseUnifiedTopic(robotId);
    }

    /**
     * 测试场景3：获取统一Topic机器人ID集合
     * 
     * 场景描述：
     * - 需要获取配置的统一Topic白名单机器人ID集合
     * - 用于重试任务和路由决策
     * - 应该返回完整的机器人ID集合
     * 
     * 验证点：
     * - 返回正确的机器人ID集合
     * - 集合内容与配置一致
     */
    @Test
    void testGetUnifiedTopicRobotIds() {
        // Given
        Set<String> expectedRobotIds = new HashSet<>(Arrays.asList("robot1", "robot2", "robot3"));
        when(unifiedTopicManager.getUnifiedTopicRobotIds()).thenReturn(expectedRobotIds);

        // When
        Set<String> result = producerAndConsumerManager.getUnifiedTopicRobotIds();

        // Then
        assertEquals(expectedRobotIds, result);
        verify(unifiedTopicManager).getUnifiedTopicRobotIds();
    }

    /**
     * 测试场景4：统一Topic功能启用状态检查
     * 
     * 场景描述：
     * - 检查统一Topic功能是否在系统中启用
     * - 用于初始化和运行时决策
     * - 应该正确反映配置状态
     * 
     * 验证点：
     * - 返回值与UnifiedTopicManager的配置一致
     * - 正确调用相关方法
     */
    @Test
    void testIsUnifiedTopicEnabled() {
        // Given
        when(unifiedTopicManager.isUnifiedTopicEnabled()).thenReturn(true);

        // When
        boolean result = producerAndConsumerManager.isUnifiedTopicEnabled();

        // Then
        assertTrue(result);
        verify(unifiedTopicManager).isUnifiedTopicEnabled();
    }

    /**
     * 测试场景5：获取机器人生产者消费者 - 已存在
     * 
     * 场景描述：
     * - 机器人的专属队列已经初始化并缓存
     * - 直接从缓存中返回现有的ProducerAndConsumer
     * - 不需要重新初始化
     * 
     * 验证点：
     * - 返回Optional包含ProducerAndConsumer对象
     * - 不会触发重新初始化逻辑
     * - 缓存机制工作正常
     */
    @Test
    void testGetRobotProducerAndConsumerExisting() {
        // Given
        String robotKey = "test-robot";
        ProducerAndConsumer existingProducerConsumer = new ProducerAndConsumer();
        existingProducerConsumer.setRobotKey(robotKey);
        existingProducerConsumer.setTopic("test-topic");
        
        Map<String, ProducerAndConsumer> robotMap = new HashMap<>();
        robotMap.put(robotKey, existingProducerConsumer);
        ReflectionTestUtils.setField(producerAndConsumerManager, "robotProducerAndConsumerMap", robotMap);

        // When
        Optional<ProducerAndConsumer> result = producerAndConsumerManager.getRobotProducerAndConsumer(robotKey);

        // Then
        assertTrue(result.isPresent());
        assertEquals(robotKey, result.get().getRobotKey());
        verify(topicCache, never()).getByRobotId(robotKey);
    }

    /**
     * 测试场景6：获取机器人生产者消费者 - 无Topic配置
     * 
     * 场景描述：
     * - 机器人没有配置专属Topic
     * - TopicCache返回空列表
     * - 应该返回空的Optional
     * 
     * 验证点：
     * - 返回Optional.empty()
     * - 正确处理无配置的情况
     * - 不会尝试初始化消费者
     */
    @Test
    void testGetRobotProducerAndConsumerNoTopicConfig() {
        // Given
        String robotKey = "test-robot";
        when(topicCache.getByRobotId(robotKey)).thenReturn(Collections.emptyList());
        
        Map<String, ProducerAndConsumer> robotMap = new HashMap<>();
        ReflectionTestUtils.setField(producerAndConsumerManager, "robotProducerAndConsumerMap", robotMap);

        // When
        Optional<ProducerAndConsumer> result = producerAndConsumerManager.getRobotProducerAndConsumer(robotKey);

        // Then
        assertFalse(result.isPresent());
        verify(topicCache).getByRobotId(robotKey);
    }

    /**
     * 测试场景7：获取启用Topic的机器人ID集合
     * 
     * 场景描述：
     * - 获取所有已配置专属Topic的机器人ID
     * - 用于重试任务中排除已有专属队列的机器人
     * - 返回当前缓存中的所有机器人Key
     * 
     * 验证点：
     * - 返回正确的机器人ID集合
     * - 集合内容与缓存一致
     */
    @Test
    void testGetEnableTopicRobotIdSet() {
        // Given
        Map<String, ProducerAndConsumer> robotMap = new HashMap<>();
        robotMap.put("robot1", new ProducerAndConsumer());
        robotMap.put("robot2", new ProducerAndConsumer());
        ReflectionTestUtils.setField(producerAndConsumerManager, "robotProducerAndConsumerMap", robotMap);

        // When
        Set<String> result = producerAndConsumerManager.getEnableTopicRobotIdSet();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains("robot1"));
        assertTrue(result.contains("robot2"));
    }

    /**
     * 测试场景8：获取系统生产者消费者 - 有配置
     * 
     * 场景描述：
     * - 系统配置了专属Topic
     * - TopicCache能返回系统的Topic配置
     * - 在机器人缓存中找到对应的ProducerAndConsumer
     * 
     * 验证点：
     * - 返回Optional包含对应的ProducerAndConsumer
     * - 正确调用TopicCache获取系统配置
     * - Topic匹配逻辑工作正常
     */
    @Test
    void testGetSysProducerAndConsumerWithConfig() {
        // Given
        String sysId = "test-system";
        String topicName = "system-topic";
        
        AppTopicDetailBO appTopicDetail = createAppTopicDetailBO(topicName);
        when(topicCache.getBySysId(sysId)).thenReturn(appTopicDetail);
        
        ProducerAndConsumer producerConsumer = new ProducerAndConsumer();
        producerConsumer.setRobotKey(topicName);
        producerConsumer.setTopic(topicName);
        
        Map<String, ProducerAndConsumer> robotMap = new HashMap<>();
        robotMap.put("robot1", producerConsumer);
        ReflectionTestUtils.setField(producerAndConsumerManager, "robotProducerAndConsumerMap", robotMap);

        // When
        Optional<ProducerAndConsumer> result = producerAndConsumerManager.getSysProducerAndConsumer(sysId);

        // Then
        assertTrue(result.isPresent());
        assertEquals(topicName, result.get().getTopic());
        verify(topicCache).getBySysId(sysId);
    }

    /**
     * 测试场景9：获取系统生产者消费者 - 无配置
     * 
     * 场景描述：
     * - 系统没有配置专属Topic
     * - TopicCache返回null或空配置
     * - 应该返回空的Optional
     * 
     * 验证点：
     * - 返回Optional.empty()
     * - 正确处理无配置的情况
     * - 不会在机器人缓存中查找
     */
    @Test
    void testGetSysProducerAndConsumerNoConfig() {
        // Given
        String sysId = "test-system";
        when(topicCache.getBySysId(sysId)).thenReturn(null);

        // When
        Optional<ProducerAndConsumer> result = producerAndConsumerManager.getSysProducerAndConsumer(sysId);

        // Then
        assertFalse(result.isPresent());
        verify(topicCache).getBySysId(sysId);
    }

    /**
     * 创建测试用的MessageUserInfo列表
     */
    private List<MessageUserInfo> createTestMessageList() {
        List<MessageUserInfo> messages = new ArrayList<>();
        
        for (int i = 1; i <= 3; i++) {
            MessageUserInfo message = new MessageUserInfo();
            message.setId((long) i);
            message.setSysId("test-system");
            message.setAppId("test-app");
            message.setUsername("user" + i);
            message.setMessageStatus(MessageStatusEnum.SENDING.getStatus());
            messages.add(message);
        }
        
        return messages;
    }

    /**
     * 创建测试用的AppTopicDetailBO
     */
    private AppTopicDetailBO createAppTopicDetailBO(String topicName) {
        AppTopicDetailBO appTopicDetail = new AppTopicDetailBO();
        appTopicDetail.setTopic(topicName);
        appTopicDetail.setStatus(2); // 启用状态
        return appTopicDetail;
    }

    /**
     * 创建测试用的RobotInfo列表
     */
    private List<RobotInfo> createTestRobotInfoList() {
        List<RobotInfo> robotInfos = new ArrayList<>();
        
        RobotInfo robot1 = new RobotInfo();
        robot1.setAppId("robot1");
        robot1.setStopFlag((byte) 1);
        robotInfos.add(robot1);
        
        RobotInfo robot2 = new RobotInfo();
        robot2.setAppId("robot2");
        robot2.setStopFlag((byte) 1);
        robotInfos.add(robot2);
        
        return robotInfos;
    }

    /**
     * 创建测试用的EmailRobotInfo列表
     */
    private List<EmailRobotInfo> createTestEmailRobotInfoList() {
        List<EmailRobotInfo> emailRobots = new ArrayList<>();
        
        EmailRobotInfo email1 = new EmailRobotInfo();
        email1.setSender("email-robot1");
        email1.setStopFlag((byte) 1);
        emailRobots.add(email1);
        
        return emailRobots;
    }

    /**
     * 创建测试用的SmsRobotInfo列表
     */
    private List<SmsRobotInfo> createTestSmsRobotInfoList() {
        List<SmsRobotInfo> smsRobots = new ArrayList<>();
        
        SmsRobotInfo sms1 = new SmsRobotInfo();
        sms1.setContentTypeKey("sms-robot1");
        sms1.setStopFlag((byte) 1);
        smsRobots.add(sms1);
        
        return smsRobots;
    }
} 