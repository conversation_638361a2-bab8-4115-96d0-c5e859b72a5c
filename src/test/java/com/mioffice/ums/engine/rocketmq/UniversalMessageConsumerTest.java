package com.mioffice.ums.engine.rocketmq;

import com.mioffice.ums.engine.config.MqProperties;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.limiter.LimiterManager;
import com.mioffice.ums.engine.limiter.RedisRateLimiter;
import com.mioffice.ums.engine.limiter.WindowRateLimiter;
import com.mioffice.ums.engine.limiter.WindowRateLimiterManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.sender.EmailMessageSender;
import com.mioffice.ums.engine.sender.MiWorkMessageSender;
import com.mioffice.ums.engine.sender.MiWorkMessageSenderV1;
import com.mioffice.ums.engine.sender.SmsMessageSender;
import com.mioffice.ums.engine.utils.JsonUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * UniversalMessageConsumer 测试类
 *
 * 测试覆盖范围：
 * 1. 基本消息消费功能
 * 2. 不同渠道（飞书、邮件、短信）的消息处理
 * 3. 统一Topic和专属队列的路由逻辑
 * 4. 白名单机制的正确性
 * 5. 限流机制的有效性
 * 6. 异常处理和容错能力
 * 7. 并发处理和线程安全
 * 8. 性能和资源管理
 * 9. 边界条件和特殊情况
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class UniversalMessageConsumerTest {

    @Mock
    private MessageUserInfoMapper messageUserInfoMapper;

    @Mock
    private WindowRateLimiterManager windowRateLimiterManager;

    @Mock
    private LimiterManager limiterManager;

    @Mock
    private MiWorkMessageSenderV1 miWorkMessageSenderV1;

    @Mock
    private MiWorkMessageSender miWorkMessageSender;

    @Mock
    private EmailMessageSender emailMessageSender;

    @Mock
    private SmsMessageSender smsMessageSender;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private MqProperties mqProperties;

    @Mock
    private MqMessageSender mqMessageSender;

    @Mock
    private WindowRateLimiter windowRateLimiter;

    @Mock
    private RedisRateLimiter redisRateLimiter;

    @Mock
    private RLock rLock;

    @Mock
    private ConsumeConcurrentlyContext context;

    @InjectMocks
    private UniversalMessageConsumer universalMessageConsumer;

    private MqProperties.RateLimit rateLimit;
    private MqProperties.UnifiedTopic unifiedTopic;
    private MqProperties.LarkApiUpgrade larkApiUpgrade;

    @BeforeEach
    void setUp() {
        // 初始化配置对象
        setupMqProperties();
        
        // 设置基本的mock行为
        when(redissonClient.getLock(anyString())).thenReturn(rLock);
        when(rLock.tryLock()).thenReturn(true);
        when(rLock.isHeldByCurrentThread()).thenReturn(true);
        
        // 设置限流器相关mock
        when(windowRateLimiterManager.getLimiter(anyString(), anyInt(), anyInt())).thenReturn(windowRateLimiter);
        when(windowRateLimiter.tryAcquireQuietly(anyString())).thenReturn(true);
    }

    private void setupMqProperties() {
        rateLimit = new MqProperties.RateLimit();
        
        // 设置邮件限流配置
        MqProperties.RateLimit.Email emailConfig = new MqProperties.RateLimit.Email();
        emailConfig.setRateMs(1000L);
        emailConfig.setRate(10L);
        rateLimit.setEmail(emailConfig);
        
        // 设置短信限流配置
        MqProperties.RateLimit.Sms smsConfig = new MqProperties.RateLimit.Sms();
        smsConfig.setRateMs(1000L);
        smsConfig.setRate(10L);
        rateLimit.setSms(smsConfig);
        
        // 设置飞书限流配置
        MqProperties.RateLimit.Miwork miworkConfig = new MqProperties.RateLimit.Miwork();
        miworkConfig.setRateMs(1000L);
        miworkConfig.setRate(50L);
        miworkConfig.setSecondLimit(50);
        miworkConfig.setMinuteLimit(1000);
        rateLimit.setMiwork(miworkConfig);
        
        // 设置统一Topic配置
        unifiedTopic = new MqProperties.UnifiedTopic();
        unifiedTopic.setEnabled(true);
        unifiedTopic.setName("ums-unified-topic");
        unifiedTopic.setDelayMinSeconds(1);
        unifiedTopic.setDelayMaxSeconds(10);
        
        // 设置飞书API升级配置
        larkApiUpgrade = new MqProperties.LarkApiUpgrade();
        larkApiUpgrade.setEnableWhiteList(true);
        Set<String> whiteList = new HashSet<>();
        whiteList.add("test-app-id");
        larkApiUpgrade.setWhiteList(whiteList);
        
        // 设置重试配置
        MqProperties.Retry retry = new MqProperties.Retry();
        retry.setLimit(50);
        
        when(mqProperties.getRateLimit()).thenReturn(rateLimit);
        when(mqProperties.getUnifiedTopic()).thenReturn(unifiedTopic);
        when(mqProperties.getLarkApiUpgrade()).thenReturn(larkApiUpgrade);
        when(mqProperties.getRetry()).thenReturn(retry);
    }

    @Test
    void testConsumeMessage_Success() {
        // 准备测试数据
        MessageExt messageExt = createMessageExt("test-topic", createMiWorkMessageUserInfo());
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        // 设置发送器返回成功
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
        verify(rLock).tryLock();
        verify(rLock).unlock();
    }

    @Test
    void testConsumeMessage_MiWorkChannel_UnifiedTopic_InWhiteList() {
        // 准备测试数据 - 统一Topic
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setAppId("test-app-id"); // 在白名单中
        MessageExt messageExt = createMessageExt("ums-unified-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果 - 应该使用V1发送器
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
        verify(miWorkMessageSender, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_MiWorkChannel_UnifiedTopic_NotInWhiteList() {
        // 准备测试数据 - 统一Topic
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setAppId("not-in-whitelist"); // 不在白名单中
        MessageExt messageExt = createMessageExt("ums-unified-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        when(miWorkMessageSender.send(any(MessageUserInfo.class))).thenReturn(true);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果 - 应该使用普通发送器
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSender).send(any(MessageUserInfo.class));
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_EmailChannel_Success() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createEmailMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        // 设置限流器返回不限流
        when(limiterManager.getLimiter(anyString(), anyLong(), anyLong())).thenReturn(redisRateLimiter);
        when(redisRateLimiter.acquire()).thenReturn(0L);
        when(emailMessageSender.send(any(MessageUserInfo.class))).thenReturn(true);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(emailMessageSender).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_SmsChannel_Success() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createSmsMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        // 设置限流器返回不限流
        when(limiterManager.getLimiter(anyString(), anyLong(), anyLong())).thenReturn(redisRateLimiter);
        when(redisRateLimiter.acquire()).thenReturn(0L);
        when(smsMessageSender.send(any(MessageUserInfo.class))).thenReturn(true);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(smsMessageSender).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_RateLimited() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        // 设置限流器返回限流
        when(windowRateLimiter.tryAcquireQuietly(anyString())).thenReturn(false);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_InvalidMessage() {
        // 准备测试数据 - 无效消息
        MessageExt messageExt = new MessageExt();
        messageExt.setTopic("test-topic");
        messageExt.setBody("invalid json".getBytes());
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果 - 应该继续处理，不阻塞
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_RetryCountExceeded() {
        // 准备测试数据 - 重试次数超限
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setRetryCount(60); // 超过限制的50次
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_LockFailed() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);
        
        // 设置获取锁失败
        when(rLock.tryLock()).thenReturn(false);
        when(rLock.isHeldByCurrentThread()).thenReturn(false);
        
        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);
        
        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    // 辅助方法
    private MessageExt createMessageExt(String topic, MessageUserInfo messageUserInfo) {
        MessageExt messageExt = new MessageExt();
        messageExt.setTopic(topic);
        messageExt.setBody(JsonUtils.toJson(messageUserInfo).getBytes());
        messageExt.setMsgId("test-msg-id-" + System.currentTimeMillis());
        return messageExt;
    }

    private MessageUserInfo createMiWorkMessageUserInfo() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setId(1L);
        messageUserInfo.setAppId("test-app-id");
        messageUserInfo.setExtraId("test-extra-id");
        messageUserInfo.setChannel(MessageChannelEnum.MI_WORK.getType());
        messageUserInfo.setRetryCount(0);
        messageUserInfo.setSysId("test-system-id");
        return messageUserInfo;
    }

    private MessageUserInfo createEmailMessageUserInfo() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setId(2L);
        messageUserInfo.setAppId("test-email-app-id");
        messageUserInfo.setExtraId("test-email-extra-id");
        messageUserInfo.setChannel(MessageChannelEnum.EMAIL.getType());
        messageUserInfo.setRetryCount(0);
        return messageUserInfo;
    }

    private MessageUserInfo createSmsMessageUserInfo() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setId(3L);
        messageUserInfo.setAppId("test-sms-app-id");
        messageUserInfo.setExtraId("test-sms-extra-id");
        messageUserInfo.setChannel(MessageChannelEnum.SMS.getType());
        messageUserInfo.setRetryCount(0);
        return messageUserInfo;
    }

    @Test
    void testConsumeMessage_EmailChannel_RateLimited() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createEmailMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置限流器返回限流（waitMs > 0）
        when(limiterManager.getLimiter(anyString(), anyLong(), anyLong())).thenReturn(redisRateLimiter);
        when(redisRateLimiter.acquire()).thenReturn(1000L);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(emailMessageSender, never()).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_SmsChannel_RateLimited() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createSmsMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置限流器返回限流（waitMs > 0）
        when(limiterManager.getLimiter(anyString(), anyLong(), anyLong())).thenReturn(redisRateLimiter);
        when(redisRateLimiter.acquire()).thenReturn(2000L);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(smsMessageSender, never()).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_UnsupportedChannel() {
        // 准备测试数据 - 不支持的渠道
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setId(4L);
        messageUserInfo.setAppId("test-app-id");
        messageUserInfo.setExtraId("test-extra-id");
        messageUserInfo.setChannel((byte) 99); // 不支持的渠道
        messageUserInfo.setRetryCount(0);

        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
        verify(emailMessageSender, never()).send(any(MessageUserInfo.class));
        verify(smsMessageSender, never()).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_SendException() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置发送器抛出异常
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenThrow(new RuntimeException("Send failed"));

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 应该处理异常并发送延迟消息
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_WhiteListDisabled() {
        // 准备测试数据 - 禁用白名单
        larkApiUpgrade.setEnableWhiteList(false);

        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setAppId("not-in-whitelist");
        MessageExt messageExt = createMessageExt("ums-unified-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 白名单禁用时应该使用V1发送器
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
        verify(miWorkMessageSender, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_EmptyWhiteList() {
        // 准备测试数据 - 空白名单
        larkApiUpgrade.setEnableWhiteList(true);
        larkApiUpgrade.setWhiteList(new HashSet<>());

        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setAppId("any-app-id");
        MessageExt messageExt = createMessageExt("ums-unified-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 空白名单时应该使用V1发送器
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
        verify(miWorkMessageSender, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_MultipleMessages() {
        // 准备测试数据 - 多条消息
        MessageUserInfo messageUserInfo1 = createMiWorkMessageUserInfo();
        messageUserInfo1.setId(1L);
        MessageUserInfo messageUserInfo2 = createEmailMessageUserInfo();
        messageUserInfo2.setId(2L);

        MessageExt messageExt1 = createMessageExt("test-topic", messageUserInfo1);
        MessageExt messageExt2 = createMessageExt("test-topic", messageUserInfo2);
        List<MessageExt> messages = Arrays.asList(messageExt1, messageExt2);

        // 设置发送器返回成功
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);
        when(limiterManager.getLimiter(anyString(), anyLong(), anyLong())).thenReturn(redisRateLimiter);
        when(redisRateLimiter.acquire()).thenReturn(0L);
        when(emailMessageSender.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
        verify(emailMessageSender).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_PartialFailure() {
        // 准备测试数据 - 部分消息失败
        MessageUserInfo messageUserInfo1 = createMiWorkMessageUserInfo();
        messageUserInfo1.setId(1L);
        MessageUserInfo messageUserInfo2 = createMiWorkMessageUserInfo();
        messageUserInfo2.setId(2L);

        MessageExt messageExt1 = createMessageExt("test-topic", messageUserInfo1);
        MessageExt messageExt2 = createMessageExt("test-topic", messageUserInfo2);
        List<MessageExt> messages = Arrays.asList(messageExt1, messageExt2);

        // 设置第一条消息成功，第二条消息失败
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenAnswer(invocation -> {
            MessageUserInfo msg = invocation.getArgument(0);
            if (msg.getId() != null && msg.getId().equals(2L)) {
                throw new RuntimeException("Send failed");
            }
            if (msg.getId() != null && msg.getId().equals(1L)) {
                return true;
            }
            return true;
        });



        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 应该继续处理所有消息
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, times(2)).send(any(MessageUserInfo.class));
        verify(mqMessageSender, times(1)).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_NullMessageBody() {
        // 准备测试数据 - 空消息体
        MessageExt messageExt = new MessageExt();
        messageExt.setTopic("test-topic");
        messageExt.setBody(null);
        messageExt.setMsgId("test-msg-id");
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 应该处理异常并继续
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_EmptyMessageList() {
        // 准备测试数据 - 空消息列表
        List<MessageExt> messages = Arrays.asList();

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_LockInterrupted() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置锁被中断
        when(rLock.tryLock()).thenThrow(new RuntimeException("Lock interrupted"));
        when(rLock.isHeldByCurrentThread()).thenReturn(false);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 应该处理异常并发送延迟消息
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_WindowRateLimiterException() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置限流器抛出异常
        when(windowRateLimiterManager.getLimiter(anyString(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("Rate limiter error"));

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 应该处理异常并发送延迟消息
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_DelayMessageSendFailed() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置限流
        when(windowRateLimiter.tryAcquireQuietly(anyString())).thenReturn(false);
        // 设置延迟消息发送失败
        when(mqMessageSender.sendDelayToTopic(anyString(), any(MessageUserInfo.class), anyLong()))
                .thenReturn(false);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 即使延迟消息发送失败，也应该返回成功
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_ConfigurationEdgeCases() {
        // 测试配置边界情况

        // 设置极端的延迟配置
        unifiedTopic.setDelayMinSeconds(0);
        unifiedTopic.setDelayMaxSeconds(1);

        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置限流
        when(windowRateLimiter.tryAcquireQuietly(anyString())).thenReturn(false);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_HighRetryCount() {
        // 准备测试数据 - 接近重试限制的消息
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setRetryCount(49); // 接近限制的50次
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 应该正常处理
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_ExactRetryLimit() {
        // 准备测试数据 - 正好达到重试限制
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setRetryCount(50); // 正好等于限制
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 应该跳过处理
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, never()).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_LongTopicName() {
        // 准备测试数据 - 长Topic名称
        String longTopicName = "very-long-topic-name-that-might-cause-issues-in-some-systems-" +
                              "with-many-characters-and-special-symbols-123456789";
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt(longTopicName, messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
    }

    @Test
    void testConsumeMessage_ConcurrentProcessing() {
        // 准备测试数据 - 模拟并发处理
        MessageUserInfo messageUserInfo1 = createMiWorkMessageUserInfo();
        messageUserInfo1.setId(1L);
        messageUserInfo1.setAppId("concurrent-app-1");

        MessageUserInfo messageUserInfo2 = createMiWorkMessageUserInfo();
        messageUserInfo2.setId(2L);
        messageUserInfo2.setAppId("concurrent-app-2");

        MessageExt messageExt1 = createMessageExt("test-topic", messageUserInfo1);
        MessageExt messageExt2 = createMessageExt("test-topic", messageUserInfo2);
        List<MessageExt> messages = Arrays.asList(messageExt1, messageExt2);

        // 设置不同的锁行为来模拟并发
        RLock lock1 = mock(RLock.class);
        RLock lock2 = mock(RLock.class);
        when(redissonClient.getLock(contains("1"))).thenReturn(lock1);
        when(redissonClient.getLock(contains("2"))).thenReturn(lock2);
        when(lock1.tryLock()).thenReturn(true);
        when(lock2.tryLock()).thenReturn(true);
        when(lock1.isHeldByCurrentThread()).thenReturn(true);
        when(lock2.isHeldByCurrentThread()).thenReturn(true);

        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, times(2)).send(any(MessageUserInfo.class));
        verify(lock1).tryLock();
        verify(lock2).tryLock();
        verify(lock1).unlock();
        verify(lock2).unlock();
    }

    @Test
    void testConsumeMessage_PerformanceWithLargeMessageBatch() {
        // 准备测试数据 - 大批量消息
        List<MessageExt> messages = new java.util.ArrayList<>();
        for (int i = 0; i < 100; i++) {
            MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
            messageUserInfo.setId((long) i);
            messageUserInfo.setAppId("perf-test-app-" + i);
            messages.add(createMessageExt("test-topic", messageUserInfo));
        }

        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 记录结束时间
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1, times(100)).send(any(MessageUserInfo.class));

        // 简单的性能断言（处理100条消息应该在合理时间内完成）
        assertTrue(endTime - startTime < 5000, "Processing 100 messages should complete within 5 seconds");
    }

    @Test
    void testConsumeMessage_MixedChannelsAndTopics() {
        // 准备测试数据 - 混合渠道和Topic
        MessageUserInfo miWorkMsg = createMiWorkMessageUserInfo();
        miWorkMsg.setAppId("test-app-in-whitelist");
        MessageExt miWorkExt = createMessageExt("ums-unified-topic", miWorkMsg);

        MessageUserInfo emailMsg = createEmailMessageUserInfo();
        MessageExt emailExt = createMessageExt("email-topic", emailMsg);

        MessageUserInfo smsMsg = createSmsMessageUserInfo();
        MessageExt smsExt = createMessageExt("sms-topic", smsMsg);

        List<MessageExt> messages = Arrays.asList(miWorkExt, emailExt, smsExt);

        // 设置各种发送器的行为
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);
        when(limiterManager.getLimiter(anyString(), anyLong(), anyLong())).thenReturn(redisRateLimiter);
        when(redisRateLimiter.acquire()).thenReturn(0L);
        when(emailMessageSender.send(any(MessageUserInfo.class))).thenReturn(true);
        when(smsMessageSender.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
    }

    @Test
    void testConsumeMessage_ResourceCleanup() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 设置发送器抛出异常
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenThrow(new RuntimeException("Send failed"));

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果 - 即使异常也要确保资源清理
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(rLock).tryLock();
        verify(rLock).unlock(); // 确保锁被释放
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_ThreadSafety() {
        // 准备测试数据 - 测试线程安全
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        // 模拟多线程环境下的锁竞争
        when(rLock.tryLock()).thenReturn(false, true); // 第一次失败，第二次成功
        when(rLock.isHeldByCurrentThread()).thenReturn(false, true);
        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(rLock, times(1)).tryLock(); // 只尝试一次获取锁
        verify(mqMessageSender).sendDelayToTopic(eq("test-topic"), any(MessageUserInfo.class), anyLong());
    }

    @Test
    void testConsumeMessage_MessageWithSpecialCharacters() {
        // 准备测试数据 - 包含特殊字符的消息
        MessageUserInfo messageUserInfo = createMiWorkMessageUserInfo();
        messageUserInfo.setAppId("app-with-特殊字符-and-émojis-🚀");
        messageUserInfo.setExtraId("extra-id-with-symbols-@#$%^&*()");

        MessageExt messageExt = createMessageExt("test-topic", messageUserInfo);
        List<MessageExt> messages = Arrays.asList(messageExt);

        when(miWorkMessageSenderV1.send(any(MessageUserInfo.class))).thenReturn(true);

        // 执行测试
        ConsumeConcurrentlyStatus result = universalMessageConsumer.consumeMessage(messages, context);

        // 验证结果
        assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
        verify(miWorkMessageSenderV1).send(any(MessageUserInfo.class));
    }
}
