package com.mioffice.ums.engine.sender;

import com.google.common.collect.Lists;
import com.mioffice.ums.engine.Application;
import com.mioffice.ums.engine.cache.TopicCache;
import com.mioffice.ums.engine.config.ManagerConfig;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.manager.*;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.service.grpc.client.AppListGrpcClient;
import com.mioffice.ums.engine.service.grpc.server.LarkRobotRpcService;
import com.mioffice.ums.engine.service.grpc.server.MessageBotRpcService;
import com.mioffice.ums.engine.service.grpc.server.MessageRpcService;
import com.mioffice.ums.engine.talos.processor.EmailMessageProcessor;
import com.mioffice.ums.engine.talos.processor.SmsMessageProcessor;
import com.xiaomi.cloud.plan.starter.PlanClientAutoConfiguration;
import com.xiaomi.info.grpc.client.autoconfigure.MrpcClientAutoConfiguration;
import com.xiaomi.info.grpc.server.autoconfigure.MrpcServerAutoConfiguration;
import com.xiaomi.info.grpc.server.context.MrpcBeanFactory;
import com.xiaomi.info.grpc.server.soa.registry.ServerRegistry;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ComponentScan(basePackages = "com.mioffice.ums.engine", excludeFilters
        = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {MrpcBeanFactory.class,
        MrpcServerAutoConfiguration.class, MrpcClientAutoConfiguration.class}))
@EnableAutoConfiguration(exclude = {MrpcClientAutoConfiguration.class, MrpcServerAutoConfiguration.class,
        PlanClientAutoConfiguration.class})
public class MessageProcessorTest {

    @Autowired
    private EmailMessageProcessor emailMessageProcessor;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @Autowired
    private SmsMessageProcessor smsMessageProcessor;

    @MockBean
    private MessageRpcService messageRpcService;

    @MockBean
    private AppListGrpcClient appListGrpcClient;

    @MockBean
    private MessageBotRpcService messageBotRpcService;

    @MockBean
    private LarkRobotRpcService larkRobotRpcService;

    @MockBean
    private TopicCache topicCache;

    @MockBean
    private ManagerConfig managerConfig;

    @MockBean
    private EnhanceMiWorkRobotManager enhanceMiWorkRobotManager;
    @MockBean
    private MessageV1MiWorkRobotManager messageV1MiWorkRobotManager;
    @MockBean
    private EmailRobotManager emailRobotManager;
    @MockBean
    private SmsRobotManager smsRobotManager;
    @MockBean
    private IdDeduplicateManager idDeduplicateManager;

    @MockBean
    private MrpcBeanFactory mrpcBeanFactory;

    @MockBean
    private ServerRegistry serverRegistry;

    @MockBean
    private LarkSaasRobotManager larkSaasRobotManager;

    @MockBean
    private LarkSaasV1RobotManager larkSaasV1RobotManager;


    private final String topic = "ums-email-topic";

    // 用于存储测试过程中插入的数据ID，便于清理
    private List<Long> testDataIds = new ArrayList<>();

    @BeforeEach
    void setUp() {
        when(topicCache.getByRobotId(any())).thenReturn(Lists.newArrayList());
        testDataIds.clear();
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        for (Long id : testDataIds) {
            try {
                messageUserInfoMapper.deleteById(id);
            } catch (Exception e) {
                // 忽略删除失败的情况
            }
        }
        testDataIds.clear();
    }

    @Test
    void testEmailFail() {
        // 1. 先插入测试数据
        MessageUserInfo messageUserInfo = createTestEmailMessage();
        messageUserInfoMapper.insert(messageUserInfo);
        testDataIds.add(messageUserInfo.getId());

        // 验证数据插入成功
        MessageUserInfo insertedMessage = messageUserInfoMapper.selectById(messageUserInfo.getId());
        assertNotNull(insertedMessage, "测试数据插入失败");
        assertEquals(Byte.valueOf(MessageStatusEnum.SENDING.getStatus()), insertedMessage.getMessageStatus());

        // 2. 执行发送失败处理
        ConsumerRecord<String, MessageUserInfo> record = new ConsumerRecord<>(topic,
                0, 0, messageUserInfo.getId() + "", messageUserInfo);
        emailMessageProcessor.updatedFail(record);

        // 3. 验证处理结果
        MessageUserInfo updatedMessage = messageUserInfoMapper.selectById(messageUserInfo.getId());
        assertNotNull(updatedMessage, "处理后数据不应为空");
        assertEquals(Byte.valueOf(MessageStatusEnum.SEND_FAIL.getStatus()), updatedMessage.getMessageStatus(), "消息状态应该更新为发送失败");

        // 4. 数据会在 @AfterEach 中自动删除
    }

    @Test
    void testSmsFail() {
        // 1. 先插入测试数据
        MessageUserInfo messageUserInfo = createTestSmsMessage();
        messageUserInfoMapper.insert(messageUserInfo);
        testDataIds.add(messageUserInfo.getId());

        // 验证数据插入成功
        MessageUserInfo insertedMessage = messageUserInfoMapper.selectById(messageUserInfo.getId());
        assertNotNull(insertedMessage, "测试数据插入失败");
        assertEquals(Byte.valueOf(MessageStatusEnum.SENDING.getStatus()), insertedMessage.getMessageStatus());

        // 2. 执行发送失败处理
        ConsumerRecord<String, MessageUserInfo> record = new ConsumerRecord<>(topic,
                0, 0, messageUserInfo.getId() + "", messageUserInfo);
        smsMessageProcessor.updatedFail(record);

        // 3. 验证处理结果
        MessageUserInfo updatedMessage = messageUserInfoMapper.selectById(messageUserInfo.getId());
        assertNotNull(updatedMessage, "处理后数据不应为空");
        assertEquals(Byte.valueOf(MessageStatusEnum.SEND_FAIL.getStatus()), updatedMessage.getMessageStatus(), "消息状态应该更新为发送失败");

        // 4. 数据会在 @AfterEach 中自动删除
    }

    @Test
    void testMiWorkFail() {
        // 1. 先插入测试数据
        MessageUserInfo messageUserInfo = createTestMiWorkMessage();
        messageUserInfoMapper.insert(messageUserInfo);
        testDataIds.add(messageUserInfo.getId());

        // 验证数据插入成功
        MessageUserInfo insertedMessage = messageUserInfoMapper.selectById(messageUserInfo.getId());
        assertNotNull(insertedMessage, "测试数据插入失败");
        assertEquals(Byte.valueOf(MessageStatusEnum.SENDING.getStatus()), insertedMessage.getMessageStatus());

        // 2. 执行发送失败处理 (注意：这里应该使用对应的processor，而不是smsMessageProcessor)
        ConsumerRecord<String, MessageUserInfo> record = new ConsumerRecord<>(topic,
                0, 0, messageUserInfo.getId() + "", messageUserInfo);
        // TODO: 这里应该使用 miWorkMessageProcessor，但当前代码中使用的是 smsMessageProcessor
        // 如果有 miWorkMessageProcessor，请替换为正确的processor
        smsMessageProcessor.updatedFail(record);

        // 3. 验证处理结果
        MessageUserInfo updatedMessage = messageUserInfoMapper.selectById(messageUserInfo.getId());
        assertNotNull(updatedMessage, "处理后数据不应为空");
        assertEquals(Byte.valueOf(MessageStatusEnum.SEND_FAIL.getStatus()), updatedMessage.getMessageStatus(), "消息状态应该更新为发送失败");

        // 4. 数据会在 @AfterEach 中自动删除
    }

    /**
     * 创建测试用的邮件消息
     */
    private MessageUserInfo createTestEmailMessage() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setAppId("test-email-app-" + System.currentTimeMillis());
        messageUserInfo.setExtraId("test-email-extra-" + System.currentTimeMillis());
        messageUserInfo.setChannel(Byte.valueOf(MessageChannelEnum.EMAIL.getType()));
        messageUserInfo.setMessageStatus(Byte.valueOf(MessageStatusEnum.SENDING.getStatus()));
        messageUserInfo.setRetryCount(0);
        messageUserInfo.setTitleCn("测试邮件标题");
        messageUserInfo.setExtraContent("测试邮件内容");
        messageUserInfo.setEmail("test-user-email");
        messageUserInfo.setSysId("test-system");
        messageUserInfo.setCreateTime(System.currentTimeMillis());
        messageUserInfo.setUpdateTime(System.currentTimeMillis());
        return messageUserInfo;
    }

    /**
     * 创建测试用的短信消息
     */
    private MessageUserInfo createTestSmsMessage() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setAppId("test-sms-app-" + System.currentTimeMillis());
        messageUserInfo.setExtraId("test-sms-extra-" + System.currentTimeMillis());
        messageUserInfo.setChannel(Byte.valueOf(MessageChannelEnum.SMS.getType()));
        messageUserInfo.setMessageStatus(Byte.valueOf(MessageStatusEnum.SENDING.getStatus()));
        messageUserInfo.setRetryCount(0);
        messageUserInfo.setTitleCn("测试短信标题");
        messageUserInfo.setExtraContent("测试短信内容");
        messageUserInfo.setPhone("test-user-sms");
        messageUserInfo.setSysId("test-system");
        messageUserInfo.setCreateTime(System.currentTimeMillis());
        messageUserInfo.setUpdateTime(System.currentTimeMillis());
        return messageUserInfo;
    }

    /**
     * 创建测试用的飞书消息
     */
    private MessageUserInfo createTestMiWorkMessage() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setAppId("test-miwork-app-" + System.currentTimeMillis());
        messageUserInfo.setExtraId("test-miwork-extra-" + System.currentTimeMillis());
        messageUserInfo.setChannel(Byte.valueOf(MessageChannelEnum.MI_WORK.getType()));
        messageUserInfo.setMessageStatus(Byte.valueOf(MessageStatusEnum.SENDING.getStatus()));
        messageUserInfo.setRetryCount(0);
        messageUserInfo.setTitleCn("测试飞书标题");
        messageUserInfo.setExtraContent("测试飞书内容");
        messageUserInfo.setUsername("test-user-miwork");
        messageUserInfo.setSysId("test-system");
        messageUserInfo.setCreateTime(System.currentTimeMillis());
        messageUserInfo.setUpdateTime(System.currentTimeMillis());
        return messageUserInfo;
    }
}
