package com.mioffice.ums.engine.sender;

import com.mioffice.ums.engine.cache.MessageFilterCache;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.MessageUserSaasInfo;
import com.mioffice.ums.engine.enums.ContentFlagEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.enums.SendSaasEnum;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserSaasInfoMapper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MiWorkMessageSender 单元测试类 - 测试飞书消息发送器的核心功能
 * 
 * <AUTHOR> Generated Code
 */
@RunWith(MockitoJUnitRunner.class)
public class MiWorkMessageSenderTest {

    @Mock
    private MessageUserInfoMapper messageUserInfoMapper;

    @Mock
    private MessageUserSaasInfoMapper messageUserSaasInfoMapper;

    @Mock
    private MiWorkRobotManager miWorkRobotManager;

    @Mock
    private IdDeduplicateManager idDeduplicateManager;

    @Mock
    private MessageFilterCache messageFilterCache;

    private MiWorkMessageSender miWorkMessageSender;

    private static final String TEST_APP_ID = "cli_test12345678";
    private static final String TEST_USERNAME = "test_user";
    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_CHAT_ID = "oc_test_chat_123";
    private static final String TEST_MESSAGE_ID = "msg_test_123";
    private static final String TEST_EXTRA_ID = "extra_test_123";
    private static final String TEST_MESSAGE_JSON = "{\"msg_type\":\"text\",\"content\":{\"text\":\"测试消息\"}}";

    @Before
    public void setUp() {
        // 使用构造器注入创建发送器实例
        miWorkMessageSender = new MiWorkMessageSender(
            messageUserInfoMapper,
            messageUserSaasInfoMapper,
            miWorkRobotManager,
            idDeduplicateManager,
            messageFilterCache
        );
    }

    /**
     * 测试构造器注入
     */
    @Test
    public void testConstructor() {
        MiWorkMessageSender sender = new MiWorkMessageSender(
            messageUserInfoMapper,
            messageUserSaasInfoMapper,
            miWorkRobotManager,
            idDeduplicateManager,
            messageFilterCache
        );

        assertNotNull("MiWorkMessageSender应该被成功创建", sender);
    }

    /**
     * 测试依赖注入验证
     */
    @Test
    public void testDependencyInjection() {
        assertNotNull("messageUserInfoMapper不应为null", messageUserInfoMapper);
        assertNotNull("messageUserSaasInfoMapper不应为null", messageUserSaasInfoMapper);
        assertNotNull("miWorkRobotManager不应为null", miWorkRobotManager);
        assertNotNull("idDeduplicateManager不应为null", idDeduplicateManager);
        assertNotNull("messageFilterCache不应为null", messageFilterCache);
    }

    /**
     * 测试消息发送成功场景
     */
    @Test
    public void testSend_Success() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        MiWorkResponseBO responseBO = createTestMiWorkResponseBO();

        // 设置mock行为 - check方法返回true
        MiWorkMessageSender spySender = spy(miWorkMessageSender);
        doReturn(true).when(spySender).check(any(MessageUserInfo.class));

        try {
            when(miWorkRobotManager.sendMsg(eq(TEST_APP_ID), eq(messageUserInfo), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);
        } catch (Exception e) {
            // Mock设置不应该抛异常
        }
        
        when(messageUserInfoMapper.updateById(any(MessageUserInfo.class))).thenReturn(1);

        // 执行测试
        boolean result = spySender.send(messageUserInfo);

        // 验证结果
        assertTrue("消息发送应该成功", result);
        verify(messageUserInfoMapper, times(1)).updateById(any(MessageUserInfo.class));
        
        // 验证消息ID被正确设置
        assertEquals("消息ID应该被正确设置", TEST_MESSAGE_ID, messageUserInfo.getMessageId());
    }

    /**
     * 测试消息发送成功且包含Lark内容标志
     */
    @Test
    public void testSend_SuccessWithLarkContent() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setContentFlag(ContentFlagEnum.LARK_CONTENT.getFlag());
        MiWorkResponseBO responseBO = createTestMiWorkResponseBO();

        // 设置mock行为
        MiWorkMessageSender spySender = spy(miWorkMessageSender);
        doReturn(true).when(spySender).check(any(MessageUserInfo.class));

        try {
            when(miWorkRobotManager.sendMsg(eq(TEST_APP_ID), eq(messageUserInfo), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);
        } catch (Exception e) {
            // Mock设置不应该抛异常
        }
        
        when(messageUserInfoMapper.updateById(any(MessageUserInfo.class))).thenReturn(1);

        // 执行测试
        boolean result = spySender.send(messageUserInfo);

        // 验证结果
        assertTrue("消息发送应该成功", result);
        assertEquals("最终内容应该被设置", TEST_MESSAGE_JSON, messageUserInfo.getFinalContent());
    }

    /**
     * 测试消息发送失败场景 - check方法返回false
     */
    @Test
    public void testSend_CheckFailed() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();

        // 设置mock行为 - check方法返回false
        MiWorkMessageSender spySender = spy(miWorkMessageSender);
        doReturn(false).when(spySender).check(any(MessageUserInfo.class));

        // 执行测试
        boolean result = spySender.send(messageUserInfo);

        // 验证结果
        assertTrue("check失败时应该返回true（跳过发送）", result);
        
        // 验证没有调用发送相关方法
        verify(messageUserInfoMapper, never()).updateById(any(MessageUserInfo.class));
    }

    /**
     * 测试发送Lark SaaS消息 - 需要发送SaaS
     */
    @Test
    public void testSendLarkSaas_ShouldSend() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setIsSendSaas(SendSaasEnum.YES.getCode());

        when(messageUserSaasInfoMapper.insert(any(MessageUserSaasInfo.class))).thenReturn(1);

        // 执行测试
        miWorkMessageSender.sendLarkSaas(messageUserInfo);

        // 验证结果
        verify(messageUserSaasInfoMapper, times(1)).insert(any(MessageUserSaasInfo.class));
    }

    /**
     * 测试发送Lark SaaS消息 - 不需要发送SaaS (标志为NO)
     */
    @Test
    public void testSendLarkSaas_ShouldNotSend_FlagNo() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setIsSendSaas(SendSaasEnum.NO.getCode());

        // 执行测试
        miWorkMessageSender.sendLarkSaas(messageUserInfo);

        // 验证结果 - 不应该插入SaaS消息
        verify(messageUserSaasInfoMapper, never()).insert(any(MessageUserSaasInfo.class));
    }

    /**
     * 测试发送Lark SaaS消息 - 不需要发送SaaS (标志为null)
     */
    @Test
    public void testSendLarkSaas_ShouldNotSend_FlagNull() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setIsSendSaas(null);

        // 执行测试
        miWorkMessageSender.sendLarkSaas(messageUserInfo);

        // 验证结果 - 不应该插入SaaS消息
        verify(messageUserSaasInfoMapper, never()).insert(any(MessageUserSaasInfo.class));
    }

    /**
     * 测试SaaS消息对象的正确创建
     */
    @Test
    public void testSendLarkSaas_SaasInfoCorrectlyCreated() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setIsSendSaas(SendSaasEnum.YES.getCode());

        when(messageUserSaasInfoMapper.insert(any(MessageUserSaasInfo.class))).thenAnswer(invocation -> {
            MessageUserSaasInfo saasInfo = invocation.getArgument(0);
            
            // 验证SaaS消息对象的属性设置
            assertEquals("用户信息ID应该正确设置", messageUserInfo.getId(), saasInfo.getUserInfoId());
            assertEquals("应用ID应该被复制", TEST_APP_ID, saasInfo.getAppId());
            assertEquals("用户名应该被复制", TEST_USERNAME, saasInfo.getUsername());
            assertEquals("邮箱应该被复制", TEST_EMAIL, saasInfo.getEmail());
            assertNotNull("创建时间应该被设置", saasInfo.getCreateTime());
            assertNotNull("更新时间应该被设置", saasInfo.getUpdateTime());
            
            return 1;
        });

        // 执行测试
        miWorkMessageSender.sendLarkSaas(messageUserInfo);

        // 验证调用
        verify(messageUserSaasInfoMapper, times(1)).insert(any(MessageUserSaasInfo.class));
    }

    /**
     * 测试继承自父类的功能 - 验证父类构造器调用
     */
    @Test
    public void testParentClassInitialization() {
        // 验证父类依赖正确初始化
        assertNotNull("MiWorkMessageSender应该继承父类功能", miWorkMessageSender);
        assertTrue("应该是DefaultMessageSender的实例", miWorkMessageSender instanceof DefaultMessageSender);
    }

    /**
     * 测试空邮箱和聊天ID的消息发送
     */
    @Test
    public void testSend_WithNullEmailAndChatId() {
        // 准备测试数据 - 邮箱和聊天ID为null
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setEmail(null);
        messageUserInfo.setChatId(null);
        MiWorkResponseBO responseBO = createTestMiWorkResponseBO();

        // 设置mock行为
        MiWorkMessageSender spySender = spy(miWorkMessageSender);
        doReturn(true).when(spySender).check(any(MessageUserInfo.class));

        try {
            when(miWorkRobotManager.sendMsg(eq(TEST_APP_ID), eq(messageUserInfo), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);
        } catch (Exception e) {
            // Mock设置不应该抛异常
        }
        
        when(messageUserInfoMapper.updateById(any(MessageUserInfo.class))).thenReturn(1);

        // 执行测试
        boolean result = spySender.send(messageUserInfo);

        // 验证基本功能正常
        assertTrue("即使有null值，消息发送仍应成功", result);
    }

    /**
     * 测试各种内容标志的处理
     */
    @Test
    public void testSend_DifferentContentFlags() {
        // 测试无内容标志的情况
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setContentFlag((byte) 0); // 设置为0或其他非Lark内容标志
        MiWorkResponseBO responseBO = createTestMiWorkResponseBO();

        MiWorkMessageSender spySender = spy(miWorkMessageSender);
        doReturn(true).when(spySender).check(any(MessageUserInfo.class));

        try {
            when(miWorkRobotManager.sendMsg(eq(TEST_APP_ID), eq(messageUserInfo), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);
        } catch (Exception e) {
            // Mock设置不应该抛异常
        }
        
        when(messageUserInfoMapper.updateById(any(MessageUserInfo.class))).thenReturn(1);

        // 执行测试
        boolean result = spySender.send(messageUserInfo);

        // 验证结果
        assertTrue("普通内容标志消息发送应该成功", result);
        assertNull("非Lark内容不应该设置最终内容", messageUserInfo.getFinalContent());
    }

    /**
     * 测试消息更新时间被正确设置
     */
    @Test
    public void testSend_UpdateTimeSet() {
        // 准备测试数据
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        long originalUpdateTime = messageUserInfo.getUpdateTime();
        MiWorkResponseBO responseBO = createTestMiWorkResponseBO();

        // 设置mock行为
        MiWorkMessageSender spySender = spy(miWorkMessageSender);
        doReturn(true).when(spySender).check(any(MessageUserInfo.class));

        try {
            when(miWorkRobotManager.sendMsg(eq(TEST_APP_ID), eq(messageUserInfo), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);
        } catch (Exception e) {
            // Mock设置不应该抛异常
        }
        
        when(messageUserInfoMapper.updateById(any(MessageUserInfo.class))).thenReturn(1);

        // 执行测试
        spySender.send(messageUserInfo);

        // 验证更新时间被修改
        assertTrue("更新时间应该被刷新", messageUserInfo.getUpdateTime() >= originalUpdateTime);
    }

    /**
     * 创建测试用的消息用户信息
     */
    private MessageUserInfo createTestMessageUserInfo() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setId(12345L);
        messageUserInfo.setAppId(TEST_APP_ID);
        messageUserInfo.setUsername(TEST_USERNAME);
        messageUserInfo.setEmail(TEST_EMAIL);
        messageUserInfo.setChatId(TEST_CHAT_ID);
        messageUserInfo.setExtraId(TEST_EXTRA_ID);
        messageUserInfo.setChannel(MessageChannelEnum.MI_WORK.getType());
        messageUserInfo.setMessageStatus(MessageStatusEnum.SENDING.getStatus());
        messageUserInfo.setContentFlag((byte) 0); // 设置为普通内容标志
        messageUserInfo.setCreateTime(System.currentTimeMillis());
        messageUserInfo.setUpdateTime(System.currentTimeMillis());
        return messageUserInfo;
    }

    /**
     * 创建测试用的飞书响应对象
     */
    private MiWorkResponseBO createTestMiWorkResponseBO() {
        MiWorkResponseBO responseBO = new MiWorkResponseBO();
        responseBO.setMsgId(TEST_MESSAGE_ID);
        responseBO.setMessageJson(TEST_MESSAGE_JSON);
        return responseBO;
    }
}
