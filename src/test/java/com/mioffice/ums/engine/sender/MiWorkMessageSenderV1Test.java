package com.mioffice.ums.engine.sender;

import com.mioffice.ums.engine.cache.MessageFilterCache;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.entity.info.MessageUserSaasInfo;
import com.mioffice.ums.engine.enums.ContentFlagEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.enums.SendSaasEnum;
import com.mioffice.ums.engine.manager.IdDeduplicateManager;
import com.mioffice.ums.engine.manager.MessageV1MiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserSaasInfoMapper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MiWorkMessageSenderV1 单元测试类
 * 测试飞书消息发送器V1版本的核心功能
 * 
 * <AUTHOR> Generated Code
 */
@RunWith(MockitoJUnitRunner.class)
public class MiWorkMessageSenderV1Test {

    @Mock
    private MessageUserInfoMapper messageUserInfoMapper;

    @Mock
    private MessageUserSaasInfoMapper messageUserSaasInfoMapper;

    @Mock
    private MessageV1MiWorkRobotManager messageV1MiWorkRobotManager;

    @Mock
    private IdDeduplicateManager idDeduplicateManager;

    @Mock
    private MessageFilterCache messageFilterCache;

    private MiWorkMessageSenderV1 miWorkMessageSenderV1;

    @Before
    public void setUp() {
        // 创建测试实例
        miWorkMessageSenderV1 = new MiWorkMessageSenderV1(
                messageUserInfoMapper,
                messageUserSaasInfoMapper,
                messageV1MiWorkRobotManager,
                idDeduplicateManager,
                messageFilterCache
        );
    }

    /**
     * 测试构造函数和依赖注入
     */
    @Test
    public void testConstructor() {
        MiWorkMessageSenderV1 sender = new MiWorkMessageSenderV1(
                messageUserInfoMapper,
                messageUserSaasInfoMapper,
                messageV1MiWorkRobotManager,
                idDeduplicateManager,
                messageFilterCache
        );

        assertNotNull("发送器实例不应该为null", sender);
        
        // 验证依赖是否正确注入
        MessageUserInfoMapper injectedMapper = 
            (MessageUserInfoMapper) ReflectionTestUtils.getField(sender, "messageUserInfoMapper");
        MessageUserSaasInfoMapper injectedSaasMapper = 
            (MessageUserSaasInfoMapper) ReflectionTestUtils.getField(sender, "messageUserSaasInfoMapper");
        MessageV1MiWorkRobotManager injectedManager = 
            (MessageV1MiWorkRobotManager) ReflectionTestUtils.getField(sender, "messageV1MiWorkRobotManager");
        IdDeduplicateManager injectedDeduplicateManager = 
            (IdDeduplicateManager) ReflectionTestUtils.getField(sender, "idDeduplicateManager");

        assertTrue("MessageUserInfoMapper应该正确注入", injectedMapper == messageUserInfoMapper);
        assertTrue("MessageUserSaasInfoMapper应该正确注入", injectedSaasMapper == messageUserSaasInfoMapper);
        assertTrue("MessageV1MiWorkRobotManager应该正确注入", injectedManager == messageV1MiWorkRobotManager);
        assertTrue("IdDeduplicateManager应该正确注入", injectedDeduplicateManager == idDeduplicateManager);
    }

    /**
     * 测试send方法 - check失败场景
     */
    @Test
    public void testSend_CheckFailed() throws Exception {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        
        // Mock check方法返回false
        MiWorkMessageSenderV1 spySender = spy(miWorkMessageSenderV1);
        doReturn(false).when(spySender).check(any(MessageUserInfo.class));
        
        // 执行测试
        boolean result = spySender.send(messageUserInfo);
        
        // 验证结果
        assertTrue("check失败时应该返回true（跳过发送）", result);
        
        // 验证没有调用发送方法
        verify(messageV1MiWorkRobotManager, never())
                .sendMsg(anyString(), any(MessageUserInfo.class), any(MiWorkRobot.Session.class));
        verify(messageUserInfoMapper, never()).updateById(any(MessageUserInfo.class));
    }

    /**
     * 测试sendLarkSaas方法 - 需要发送SaaS的场景
     */
    @Test
    public void testSendLarkSaas_NeedSend() {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setIsSendSaas(SendSaasEnum.YES.getCode());
        
        // Mock数据库插入
        when(messageUserSaasInfoMapper.insert(any(MessageUserSaasInfo.class))).thenReturn(1);
        
        // 执行测试
        miWorkMessageSenderV1.sendLarkSaas(messageUserInfo);
        
        // 验证insert方法被调用
        verify(messageUserSaasInfoMapper, times(1)).insert(any(MessageUserSaasInfo.class));
    }

    /**
     * 测试sendLarkSaas方法 - 不需要发送SaaS的场景
     */
    @Test
    public void testSendLarkSaas_NoNeedSend() {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setIsSendSaas(SendSaasEnum.NO.getCode());
        
        // 执行测试
        miWorkMessageSenderV1.sendLarkSaas(messageUserInfo);
        
        // 验证没有调用insert方法
        verify(messageUserSaasInfoMapper, never()).insert(any(MessageUserSaasInfo.class));
    }

    /**
     * 测试sendLarkSaas方法 - isSendSaas为null的场景
     */
    @Test
    public void testSendLarkSaas_NullSendSaas() {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setIsSendSaas(null);
        
        // 执行测试
        miWorkMessageSenderV1.sendLarkSaas(messageUserInfo);
        
        // 验证没有调用insert方法
        verify(messageUserSaasInfoMapper, never()).insert(any(MessageUserSaasInfo.class));
    }

    /**
     * 测试Session对象的创建逻辑
     */
    @Test
    public void testSessionCreation() {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        messageUserInfo.setUsername("test_user");
        messageUserInfo.setEmail("<EMAIL>");
        messageUserInfo.setChatId("test_chat_id");
        
        // 验证Session对象的字段设置逻辑
        MiWorkRobot.Session session = new MiWorkRobot.Session();
        session.setUserId(messageUserInfo.getUsername());
        session.setEmail(messageUserInfo.getEmail());
        session.setOpenChatId(messageUserInfo.getChatId());
        
        // 验证Session字段设置
        assertEquals("UserId应该正确设置", "test_user", session.getUserId());
        assertEquals("Email应该正确设置", "<EMAIL>", session.getEmail());
        assertEquals("OpenChatId应该正确设置", "test_chat_id", session.getOpenChatId());
    }

    /**
     * 测试MessageUserInfo的状态更新逻辑
     */
    @Test
    public void testMessageUserInfoUpdate() {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        
        // 测试更新逻辑
        MessageUserInfo updateMessageUserInfo = MessageUserInfo.newUpdateTimeInstant();
        updateMessageUserInfo.setId(messageUserInfo.getId());
        updateMessageUserInfo.setMessageId("test_msg_id");
        updateMessageUserInfo.setMessageStatus(MessageStatusEnum.SEND_SUCCESS.getStatus());
        
        // 验证更新字段
        assertEquals("ID应该正确设置", messageUserInfo.getId(), updateMessageUserInfo.getId());
        assertEquals("MessageId应该正确设置", "test_msg_id", updateMessageUserInfo.getMessageId());
        assertTrue("MessageStatus应该是发送成功", 
            updateMessageUserInfo.getMessageStatus() == MessageStatusEnum.SEND_SUCCESS.getStatus());
    }

    /**
     * 测试ContentFlag的处理逻辑
     */
    @Test
    public void testContentFlagHandling() {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        
        // 测试默认ContentFlag
        messageUserInfo.setContentFlag(ContentFlagEnum.NO.getFlag());
        assertTrue("默认ContentFlag应该是NO", 
            messageUserInfo.getContentFlag() == ContentFlagEnum.NO.getFlag());
        
        // 测试Lark ContentFlag
        messageUserInfo.setContentFlag(ContentFlagEnum.LARK_CONTENT.getFlag());
        assertTrue("Lark ContentFlag应该正确设置", 
            messageUserInfo.getContentFlag() == ContentFlagEnum.LARK_CONTENT.getFlag());
    }

    /**
     * 测试SendSaasEnum的处理逻辑
     */
    @Test
    public void testSendSaasEnumHandling() {
        MessageUserInfo messageUserInfo = createTestMessageUserInfo();
        
        // 测试不发送SaaS
        messageUserInfo.setIsSendSaas(SendSaasEnum.NO.getCode());
        assertTrue("不应该发送SaaS", 
            messageUserInfo.getIsSendSaas() == SendSaasEnum.NO.getCode());
        
        // 测试发送SaaS
        messageUserInfo.setIsSendSaas(SendSaasEnum.YES.getCode());
        assertTrue("应该发送SaaS", 
            messageUserInfo.getIsSendSaas() == SendSaasEnum.YES.getCode());
        
        // 测试null值处理
        messageUserInfo.setIsSendSaas(null);
        assertNull("isSendSaas可以为null", messageUserInfo.getIsSendSaas());
    }

    /**
     * 测试MessageChannelEnum的使用
     */
    @Test
    public void testMessageChannelEnum() {
        // 验证消息渠道枚举
        MessageChannelEnum channel = MessageChannelEnum.MI_WORK;
        assertNotNull("MessageChannelEnum不应该为null", channel);
        
        // 测试枚举在去重管理器中的使用逻辑
        assertTrue("MI_WORK渠道应该存在", MessageChannelEnum.MI_WORK != null);
    }

    /**
     * 测试MessageStatusEnum的使用
     */
    @Test
    public void testMessageStatusEnum() {
        // 验证消息状态枚举
        byte sendingStatus = MessageStatusEnum.SENDING.getStatus();
        byte successStatus = MessageStatusEnum.SEND_SUCCESS.getStatus();
        
        assertTrue("发送中状态应该有效", sendingStatus >= 0);
        assertTrue("发送成功状态应该有效", successStatus >= 0);
        assertTrue("发送成功状态应该不同于发送中状态", sendingStatus != successStatus);
    }

    /**
     * 测试MiWorkResponseBO的基本功能
     */
    @Test
    public void testMiWorkResponseBO() {
        MiWorkResponseBO response = new MiWorkResponseBO("test_msg_id");
        
        assertEquals("消息ID应该正确设置", "test_msg_id", response.getMsgId());
        
        response.setMessageJson("{\"test\": \"json\"}");
        assertEquals("MessageJson应该正确设置", "{\"test\": \"json\"}", response.getMessageJson());
    }

    /**
     * 测试依赖注入的完整性
     */
    @Test
    public void testDependencyInjection() {
        assertNotNull("MessageUserInfoMapper不应该为null", messageUserInfoMapper);
        assertNotNull("MessageUserSaasInfoMapper不应该为null", messageUserSaasInfoMapper);
        assertNotNull("MessageV1MiWorkRobotManager不应该为null", messageV1MiWorkRobotManager);
        assertNotNull("IdDeduplicateManager不应该为null", idDeduplicateManager);
        assertNotNull("MessageFilterCache不应该为null", messageFilterCache);
    }

    /**
     * 测试继承关系
     */
    @Test
    public void testInheritance() {
        assertTrue("MiWorkMessageSenderV1应该继承自DefaultMessageSender", 
            miWorkMessageSenderV1 instanceof DefaultMessageSender);
    }

    /**
     * 创建测试用的MessageUserInfo对象
     */
    private MessageUserInfo createTestMessageUserInfo() {
        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setId(12345L);
        messageUserInfo.setAppId("test_app_id");
        messageUserInfo.setUsername("test_user");
        messageUserInfo.setEmail("<EMAIL>");
        messageUserInfo.setChatId("test_chat_id");
        messageUserInfo.setExtraId("test_extra_id");
        messageUserInfo.setContentFlag(ContentFlagEnum.NO.getFlag());
        messageUserInfo.setIsSendSaas(SendSaasEnum.NO.getCode());
        return messageUserInfo;
    }
}
