package com.mioffice.ums.engine.service;

import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageFilterInfoMapper;
import com.mioffice.ums.engine.mapper.MessageTemplateInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumer;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumerManager;
import com.mioffice.ums.engine.service.grpc.server.MessageRpcService;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.MessageUserResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 消息路由机制测试类
 * 测试各种路由场景：系统专属队列、机器人专属队列、统一Topic、公共队列
 */
@ExtendWith(MockitoExtension.class)
public class MessageRouteTest {

    @Mock
    private ProducerAndConsumerManager producerAndConsumerManager;

    @Mock
    private MessageService messageService;

    @Mock
    private ProducerAndConsumer mockProducerAndConsumer;

    private MessageRpcService messageRpcService;
    private List<MessageUserInfo> testUserInfoList;

    @BeforeEach
    void setUp() {
        // 创建测试用的MessageUserInfo列表
        testUserInfoList = createTestMessageUserInfoList();
        
        // 创建MessageRpcService的依赖mock对象
        MessageTemplateInfoMapper messageTemplateInfoMapper = mock(MessageTemplateInfoMapper.class);
        MessageUserInfoMapper messageUserInfoMapper = mock(MessageUserInfoMapper.class);
        MessageFilterInfoMapper messageFilterInfoMapper = mock(MessageFilterInfoMapper.class);
        SendMqEventBus sendMqEventBus = mock(SendMqEventBus.class);
        MiWorkRobotManager miWorkRobotManager = mock(MiWorkRobotManager.class);
        
        // 创建真实的MessageRpcService实例
        messageRpcService = new MessageRpcService(
                messageTemplateInfoMapper,
                messageUserInfoMapper,
                messageFilterInfoMapper,
                sendMqEventBus,
                miWorkRobotManager
        );
        
        // 注入mock对象
        ReflectionTestUtils.setField(messageRpcService, "producerAndConsumerManager", producerAndConsumerManager);
        ReflectionTestUtils.setField(messageRpcService, "messageService", messageService);
    }

    /**
     * 测试场景1：系统专属队列路由
     * 
     * 场景描述：
     * - 系统配置了专属的消息队列Topic
     * - 消息应该优先路由到系统专属队列进行处理
     * - 这是路由优先级的最高级别，确保重要系统的消息隔离
     * 
     * 验证点：
     * - 系统专属队列能被正确识别和获取
     * - 消息能成功发送到指定的系统专属Topic
     * - 不会尝试其他路由方式(机器人队列、统一Topic、公共队列)
     * - 路由决策的优先级逻辑正确
     */
    @Test
    void testSystemDedicatedQueueRouting() {
        // Given
        String sysId = "test-system";
        String topicName = "system-dedicated-topic";
        
        when(mockProducerAndConsumer.getTopic()).thenReturn(topicName);
        when(producerAndConsumerManager.getSysProducerAndConsumer(sysId))
                .thenReturn(Optional.of(mockProducerAndConsumer));
        when(producerAndConsumerManager.sendToSpecificTopic(eq(topicName), any()))
                .thenReturn(true);

        // When
        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        callRouteMessage(testUserInfoList, responseBuilder);

        // Then
        verify(producerAndConsumerManager).getSysProducerAndConsumer(sysId);
        verify(producerAndConsumerManager).sendToSpecificTopic(topicName, testUserInfoList);
        
        // 验证不会继续尝试其他路由方式
        verify(producerAndConsumerManager, never()).getRobotProducerAndConsumer(anyString());
        verify(producerAndConsumerManager, never()).shouldUseUnifiedTopic(anyString());
        verify(messageService, never()).asyncSendMsg2EventBus(any());
    }

    /**
     * 测试场景2：机器人专属队列路由
     * 
     * 场景描述：
     * - 系统没有配置专属队列，但机器人(App)配置了专属队列
     * - 消息应该路由到机器人专属队列进行处理
     * - 这是路由优先级的第二级别，按机器人维度进行消息隔离
     * 
     * 验证点：
     * - 系统专属队列查询返回空，触发下一级路由
     * - 机器人专属队列能被正确识别和获取
     * - 消息能成功发送到机器人专属Topic
     * - 不会继续尝试统一Topic和公共队列
     */
    @Test
    void testRobotDedicatedQueueRouting() {
        // Given
        String sysId = "test-system";
        String robotId = "test-robot";
        String topicName = "robot-dedicated-topic";
        
        // 系统专属队列不存在
        when(producerAndConsumerManager.getSysProducerAndConsumer(sysId))
                .thenReturn(Optional.empty());
        
        // 机器人专属队列存在
        when(mockProducerAndConsumer.getTopic()).thenReturn(topicName);
        when(producerAndConsumerManager.getRobotProducerAndConsumer(robotId))
                .thenReturn(Optional.of(mockProducerAndConsumer));
        when(producerAndConsumerManager.sendToSpecificTopic(eq(topicName), any()))
                .thenReturn(true);

        // When
        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        callRouteMessage(testUserInfoList, responseBuilder);

        // Then
        verify(producerAndConsumerManager).getSysProducerAndConsumer(sysId);
        verify(producerAndConsumerManager).getRobotProducerAndConsumer(robotId);
        verify(producerAndConsumerManager).sendToSpecificTopic(topicName, testUserInfoList);
        
        // 验证不会继续尝试统一Topic和公共队列
        verify(producerAndConsumerManager, never()).shouldUseUnifiedTopic(anyString());
        verify(messageService, never()).asyncSendMsg2EventBus(any());
    }

    /**
     * 测试场景3：统一Topic路由
     * 
     * 场景描述：
     * - 系统和机器人都没有配置专属队列
     * - 系统配置为使用统一Topic进行消息处理
     * - 统一Topic是为了减少Topic数量，提高资源利用率的方案
     * 
     * 验证点：
     * - 专属队列查询都返回空，触发统一Topic检查
     * - 系统被正确识别为应使用统一Topic
     * - 消息能成功发送到统一Topic
     * - 不会使用公共队列作为最后兜底
     */
    @Test
    void testUnifiedTopicRouting() {
        // Given
        String sysId = "test-system";
        String robotId = "test-robot";
        
        // 专属队列都不存在
        when(producerAndConsumerManager.getSysProducerAndConsumer(sysId))
                .thenReturn(Optional.empty());
        when(producerAndConsumerManager.getRobotProducerAndConsumer(robotId))
                .thenReturn(Optional.empty());
        
        // 机器人应该使用统一Topic
        when(producerAndConsumerManager.shouldUseUnifiedTopic(robotId))
                .thenReturn(true);
        when(producerAndConsumerManager.sendToUnifiedTopic(testUserInfoList))
                .thenReturn(true);

        // When
        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        callRouteMessage(testUserInfoList, responseBuilder);

        // Then
        verify(producerAndConsumerManager).getSysProducerAndConsumer(sysId);
        verify(producerAndConsumerManager).getRobotProducerAndConsumer(robotId);
        verify(producerAndConsumerManager).shouldUseUnifiedTopic(robotId);
        verify(producerAndConsumerManager).sendToUnifiedTopic(testUserInfoList);
        
        // 验证不会使用公共队列
        verify(messageService, never()).asyncSendMsg2EventBus(any());
    }

    /**
     * 测试场景4：公共队列路由(兜底方案)
     * 
     * 场景描述：
     * - 系统和机器人都没有专属队列
     * - 系统不使用统一Topic
     * - 消息最终通过公共队列(Event Bus)进行处理
     * - 这是路由的最后兜底方案，确保消息不会丢失
     * 
     * 验证点：
     * - 所有高优先级路由方式都不可用
     * - 系统被正确识别为不使用统一Topic
     * - 消息最终通过Event Bus异步发送
     * - 路由降级机制工作正常
     */
    @Test
    void testPublicQueueRouting() {
        // Given
        String sysId = "test-system";
        String robotId = "test-robot";
        
        // 专属队列都不存在
        when(producerAndConsumerManager.getSysProducerAndConsumer(sysId))
                .thenReturn(Optional.empty());
        when(producerAndConsumerManager.getRobotProducerAndConsumer(robotId))
                .thenReturn(Optional.empty());
        
        // 机器人不使用统一Topic
        when(producerAndConsumerManager.shouldUseUnifiedTopic(robotId))
                .thenReturn(false);

        // When
        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        callRouteMessage(testUserInfoList, responseBuilder);

        // Then
        verify(producerAndConsumerManager).getSysProducerAndConsumer(sysId);
        verify(producerAndConsumerManager).getRobotProducerAndConsumer(robotId);
        verify(producerAndConsumerManager).shouldUseUnifiedTopic(robotId);
        verify(messageService).asyncSendMsg2EventBus(testUserInfoList);
        
        // 验证不会尝试统一Topic发送
        verify(producerAndConsumerManager, never()).sendToUnifiedTopic(any());
    }

    /**
     * 测试场景5：路由失败回退机制
     * 
     * 场景描述：
     * - 系统专属队列存在但发送失败(网络问题、Topic不可用等)
     * - 系统应该自动回退到下一级路由方式(机器人专属队列)
     * - 机器人专属队列发送成功，完成消息路由
     * - 验证多级回退机制的可靠性
     * 
     * 验证点：
     * - 系统专属队列发送失败时能正确回退
     * - 回退到机器人专属队列并成功发送
     * - 回退逻辑不会跳过中间层级
     * - 失败重试机制工作正常
     */
    @Test
    void testFallbackMechanism() {
        // Given
        String sysId = "test-system";
        String robotId = "test-robot";
        String sysTopicName = "system-topic";
        String robotTopicName = "robot-topic";
        
        // 系统专属队列存在但发送失败
        ProducerAndConsumer sysProducerAndConsumer = mock(ProducerAndConsumer.class);
        when(sysProducerAndConsumer.getTopic()).thenReturn(sysTopicName);
        when(producerAndConsumerManager.getSysProducerAndConsumer(sysId))
                .thenReturn(Optional.of(sysProducerAndConsumer));
        doReturn(false).when(producerAndConsumerManager)
                .sendToSpecificTopic(eq(sysTopicName), any());
        
        // 机器人专属队列存在且发送成功
        when(mockProducerAndConsumer.getTopic()).thenReturn(robotTopicName);
        when(producerAndConsumerManager.getRobotProducerAndConsumer(robotId))
                .thenReturn(Optional.of(mockProducerAndConsumer));
        doReturn(true).when(producerAndConsumerManager)
                .sendToSpecificTopic(eq(robotTopicName), any());

        // When
        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        callRouteMessage(testUserInfoList, responseBuilder);

        // Then
        verify(producerAndConsumerManager).getSysProducerAndConsumer(sysId);
        verify(producerAndConsumerManager).sendToSpecificTopic(sysTopicName, testUserInfoList);
        verify(producerAndConsumerManager).getRobotProducerAndConsumer(robotId);
        verify(producerAndConsumerManager).sendToSpecificTopic(robotTopicName, testUserInfoList);
        
        // 验证不会继续尝试统一Topic
        verify(producerAndConsumerManager, never()).shouldUseUnifiedTopic(anyString());
    }

    /**
     * 测试场景6：完全回退到公共队列
     * 
     * 场景描述：
     * - 所有高优先级路由方式都尝试但均失败
     * - 系统专属队列发送失败
     * - 机器人专属队列发送失败  
     * - 统一Topic发送失败
     * - 最终必须回退到公共队列确保消息不丢失
     * 
     * 验证点：
     * - 所有路由方式都被尝试
     * - 每个路由层级的失败都能正确处理
     * - 最终能成功回退到公共队列
     * - 完整的降级链路工作正常
     */
    @Test
    void testCompletelyFallbackToPublicQueue() {
        // Given
        String sysId = "test-system";
        String robotId = "test-robot";
        String sysTopicName = "system-topic";
        String robotTopicName = "robot-topic";
        
        // 系统专属队列发送失败
        ProducerAndConsumer sysProducerAndConsumer = mock(ProducerAndConsumer.class);
        when(sysProducerAndConsumer.getTopic()).thenReturn(sysTopicName);
        when(producerAndConsumerManager.getSysProducerAndConsumer(sysId))
                .thenReturn(Optional.of(sysProducerAndConsumer));
        doReturn(false).when(producerAndConsumerManager)
                .sendToSpecificTopic(eq(sysTopicName), any());
        
        // 机器人专属队列发送失败
        ProducerAndConsumer robotProducerAndConsumer = mock(ProducerAndConsumer.class);
        when(robotProducerAndConsumer.getTopic()).thenReturn(robotTopicName);
        when(producerAndConsumerManager.getRobotProducerAndConsumer(robotId))
                .thenReturn(Optional.of(robotProducerAndConsumer));
        doReturn(false).when(producerAndConsumerManager)
                .sendToSpecificTopic(eq(robotTopicName), any());
        
        // 统一Topic发送失败
        when(producerAndConsumerManager.shouldUseUnifiedTopic(robotId))
                .thenReturn(true);
        when(producerAndConsumerManager.sendToUnifiedTopic(testUserInfoList))
                .thenReturn(false);

        // When
        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        callRouteMessage(testUserInfoList, responseBuilder);

        // Then
        verify(producerAndConsumerManager).getSysProducerAndConsumer(sysId);
        verify(producerAndConsumerManager).sendToSpecificTopic(sysTopicName, testUserInfoList);
        verify(producerAndConsumerManager).getRobotProducerAndConsumer(robotId);
        verify(producerAndConsumerManager).sendToSpecificTopic(robotTopicName, testUserInfoList);
        verify(producerAndConsumerManager).shouldUseUnifiedTopic(robotId);
        verify(producerAndConsumerManager).sendToUnifiedTopic(testUserInfoList);
        verify(messageService).asyncSendMsg2EventBus(testUserInfoList);
    }

    /**
     * 测试场景7：路由异常处理
     * 
     * 场景描述：
     * - 路由过程中发生运行时异常(如网络中断、服务不可用等)
     * - 系统应该能优雅地处理异常，不影响整体服务稳定性
     * - 异常应该被正确捕获和记录，但不会触发公共队列兜底
     * - 验证异常处理机制的健壮性
     * 
     * 验证点：
     * - 路由异常能被正确捕获，不导致服务崩溃
     * - 异常处理只记录日志，不执行额外的路由逻辑
     * - 系统在异常情况下能保持基本稳定性
     * - 异常不会触发公共队列的兜底机制
     */
    @Test
    void testRoutingExceptionHandling() {
        // Given
        String sysId = "test-system";
        
        // 模拟路由过程中抛出异常
        when(producerAndConsumerManager.getSysProducerAndConsumer(sysId))
                .thenThrow(new RuntimeException("Mock routing exception"));

        // When & Then - 异常应该被捕获，不会影响系统
        MessageUserResponse.Builder responseBuilder = MessageUserResponse.newBuilder();
        callRouteMessage(testUserInfoList, responseBuilder);
        
        // 验证异常被处理，只会记录日志，不会调用公共队列
        // 因为routeMessage方法的异常处理只是记录日志，不会回退到公共队列
        verify(producerAndConsumerManager).getSysProducerAndConsumer(sysId);
        verify(messageService, never()).asyncSendMsg2EventBus(any());
    }

    /**
     * 创建测试用的MessageUserInfo列表
     */
    private List<MessageUserInfo> createTestMessageUserInfoList() {
        MessageUserInfo userInfo1 = new MessageUserInfo();
        userInfo1.setSysId("test-system");
        userInfo1.setAppId("test-robot");
        userInfo1.setUsername("user1");
        userInfo1.setMessageStatus(MessageStatusEnum.SENDING.getStatus());
        
        MessageUserInfo userInfo2 = new MessageUserInfo();
        userInfo2.setSysId("test-system");
        userInfo2.setAppId("test-robot");
        userInfo2.setUsername("user2");
        userInfo2.setMessageStatus(MessageStatusEnum.SENDING.getStatus());
        
        return Arrays.asList(userInfo1, userInfo2);
    }

    /**
     * 调用路由消息的私有方法
     * 注意：这里需要根据实际的MessageRpcService实现来调整
     */
    private void callRouteMessage(List<MessageUserInfo> userInfoList, MessageUserResponse.Builder responseBuilder) {
        try {
            // 使用反射调用私有方法
            java.lang.reflect.Method method = MessageRpcService.class.getDeclaredMethod(
                    "routeMessage", List.class, MessageUserResponse.Builder.class);
            method.setAccessible(true);
            method.invoke(messageRpcService, userInfoList, responseBuilder);
        } catch (Exception e) {
            throw new RuntimeException("Failed to call routeMessage method", e);
        }
    }
} 