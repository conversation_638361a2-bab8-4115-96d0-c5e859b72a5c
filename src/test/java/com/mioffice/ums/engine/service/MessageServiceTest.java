package com.mioffice.ums.engine.service;

import com.mioffice.ums.engine.base.BaseTest;
import com.mioffice.ums.engine.cache.TopicCache;
import com.mioffice.ums.engine.entity.bo.AppTopicDetailBO;
import com.mioffice.ums.engine.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @ClassName messageServiceTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/8/16 20:16
 **/
@Slf4j
public class MessageServiceTest extends BaseTest {
    @Autowired
    private MessageService messageService;

    @Autowired
    private TopicCache topicCache;

    @Test
    public void scanMessageReadTest() {
        messageService.scanMessageRead(1, 2);
    }

    @Test
    public void getTopicTest() {
        List<AppTopicDetailBO> appTopicDetailBOList = topicCache.getByRobotId("cli_9fba144ce633d063");
        log.info(JsonUtils.toJson(appTopicDetailBOList));
    }
}
