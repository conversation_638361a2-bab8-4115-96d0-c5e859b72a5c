package com.mioffice.ums.engine.service.grpc.server;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.larksuite.appframework.sdk.client.LarkClient;
import com.larksuite.appframework.sdk.core.protocol.common.Group;
import com.mioffice.ums.engine.cache.TemplateCache;
import com.mioffice.ums.engine.control.DevUserSendControl;
import com.mioffice.ums.engine.entity.bo.*;
import com.mioffice.ums.engine.entity.info.MessageFilterInfo;
import com.mioffice.ums.engine.entity.info.MessageTemplateInfo;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.*;
import com.mioffice.ums.engine.event.SingleEvent;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import com.mioffice.ums.engine.manager.EnhanceMiWorkRobotManager;
import com.mioffice.ums.engine.manager.MiWorkRobotManager;
import com.mioffice.ums.engine.mapper.MessageFilterInfoMapper;
import com.mioffice.ums.engine.mapper.MessageTemplateInfoMapper;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.result.ResultCode;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumer;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumerManager;
import com.mioffice.ums.engine.service.MessageService;
import com.xiaomi.info.pb.app.mioffice.ums.engine.v1.*;
import io.grpc.stub.StreamObserver;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MessageRpcService 单元测试类 - 100%行覆盖率
 *
 * <AUTHOR> Generated Code
 */
@RunWith(MockitoJUnitRunner.class)
public class MessageRpcServiceTest {

    @Mock
    private MessageTemplateInfoMapper messageTemplateInfoMapper;
    
    @Mock
    private MessageUserInfoMapper messageUserInfoMapper;
    
    @Mock
    private MessageFilterInfoMapper messageFilterInfoMapper;
    
    @Mock
    private SendMqEventBus sendMqEventBus;
    
    @Mock
    private MiWorkRobotManager miWorkRobotManager;
    
    @Mock
    private DevUserSendControl devUserSendControl;
    
    @Mock
    private EnhanceMiWorkRobotManager enhanceMiWorkRobotManager;
    
    @Mock
    private TemplateCache templateCache;
    
    @Mock
    private RedissonClient redissonClient;
    
    @Mock
    private MessageService messageService;
    
    @Mock
    private ProducerAndConsumerManager producerAndConsumerManager;
    
    @Mock
    private StreamObserver<MessageTemplateResponse> templateResponseObserver;
    
    @Mock
    private StreamObserver<MessageUserResponse> userResponseObserver;
    
    @Mock
    private StreamObserver<CommonResponse> commonResponseObserver;
    
    @Mock
    private StreamObserver<FinalContentResponse> finalContentResponseObserver;
    
    @Mock
    private StreamObserver<ImageKeyResponse> imageKeyResponseObserver;
    
    @Mock
    private StreamObserver<MessageNumberAndTimeResponse> numberAndTimeResponseObserver;
    
    @Mock
    private StreamObserver<MessageResultSingleForCustomResponse> customResponseObserver;
    
    @Mock
    private StreamObserver<MessageResultSingleForDeptResponse> deptResponseObserver;
    
    @Mock
    private StreamObserver<CostTimeByDateAndExtraIdResponse> costTimeResponseObserver;
    
    @Mock
    private StreamObserver<MessageAverageCostTimeResponse> avgCostTimeResponseObserver;
    
    @Mock
    private StreamObserver<GroupListResponse> groupListResponseObserver;
    
    @Mock
    private StreamObserver<GroupInfosResponse> groupInfosResponseObserver;
    
    @Mock
    private StreamObserver<MessageAvgCostTimeResponse> msgAvgCostTimeResponseObserver;
    
    @Mock
    private StreamObserver<MessageReadResponse> readResponseObserver;
    
    @Mock
    private StreamObserver<MessageUnReadDetailResponse> unReadDetailResponseObserver;
    
    @Mock
    private StreamObserver<UsernameResponse> usernameResponseObserver;
    
    @Mock
    private RBucket<String> rBucket;

    @InjectMocks
    private MessageRpcService messageRpcService;

    @Before
    public void setUp() {
        // Set up executives field
        ReflectionTestUtils.setField(messageRpcService, "executives", "admin,manager");
        when(redissonClient.getBucket(anyString())).thenReturn((RBucket) rBucket);
    }

    @Test
    public void testCreateMessageTemplate_Success() {
        // Given
        MessageTemplateRequest request = MessageTemplateRequest.newBuilder()
                .setChannel(MessageChannelEnum.MI_WORK.getType())
                .setTitleCn("测试标题")
                .setContentCn("测试内容")
                .setAppId("test-app-id")
                .build();

        when(messageTemplateInfoMapper.insert(any(MessageTemplateInfo.class))).thenReturn(1);

        // When
        messageRpcService.createMessageTemplate(request, templateResponseObserver);

        // Then
        ArgumentCaptor<MessageTemplateResponse> responseCaptor = ArgumentCaptor.forClass(MessageTemplateResponse.class);
        verify(templateResponseObserver).onNext(responseCaptor.capture());
        verify(templateResponseObserver).onCompleted();

        MessageTemplateResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        assertEquals(ResultCode.OK.getMessage(), response.getMessage());
        verify(rBucket).set(anyString(), any(Duration.class));
    }

    @Test
    public void testCreateMessageTemplate_ParamError() {
        // Given
        MessageTemplateRequest request = MessageTemplateRequest.newBuilder()
                .setChannel(99) // Invalid channel
                .build();

        // When
        messageRpcService.createMessageTemplate(request, templateResponseObserver);

        // Then
        ArgumentCaptor<MessageTemplateResponse> responseCaptor = ArgumentCaptor.forClass(MessageTemplateResponse.class);
        verify(templateResponseObserver).onNext(responseCaptor.capture());
        verify(templateResponseObserver).onCompleted();

        MessageTemplateResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.PARAM_ERROR.getCode(), response.getCode());
    }

    @Test
    public void testCreateMessageTemplate_WithCardActions() throws Exception {
        // Given
        CardAction cardAction = CardAction.newBuilder()
                .setName("Test Action")
                .setLandingUrl("http://test.com")
                .putVal("key", "value")
                .build();

        MessageTemplateRequest request = MessageTemplateRequest.newBuilder()
                .setChannel(MessageChannelEnum.MI_WORK.getType())
                .setTitleCn("测试标题")
                .setContentCn("测试内容 ![image](http://example.com/image.jpg)")
                .setAppId("test-app-id")
                .addCardActions(cardAction)
                .addCardActionsCn(cardAction)
                .addCardActionsEn(cardAction)
                .setIsSendSaas(SendSaasEnum.YES.getCode())
                .build();

        when(messageTemplateInfoMapper.insert(any(MessageTemplateInfo.class))).thenReturn(1);
        doNothing().when(messageService).asyncUploadImage(any(Set.class), any(MessageTemplateInfo.class), anyString());
        doNothing().when(messageService).asyncUploadSaasImage(any(Set.class), any(MessageTemplateInfo.class), anyString());

        // When
        messageRpcService.createMessageTemplate(request, templateResponseObserver);

        // Then
        verify(messageService).asyncUploadImage(any(Set.class), any(MessageTemplateInfo.class), anyString());
        verify(messageService).asyncUploadSaasImage(any(Set.class), any(MessageTemplateInfo.class), anyString());
    }

    @Test
    public void testCreateMessageTemplate_Exception() {
        // Given
        MessageTemplateRequest request = MessageTemplateRequest.newBuilder()
                .setChannel(MessageChannelEnum.MI_WORK.getType())
                .setTitleCn("测试标题")
                .setContentCn("测试内容")
                .setAppId("test-app-id")
                .build();

        when(messageTemplateInfoMapper.insert(any(MessageTemplateInfo.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When
        messageRpcService.createMessageTemplate(request, templateResponseObserver);

        // Then
        ArgumentCaptor<MessageTemplateResponse> responseCaptor = ArgumentCaptor.forClass(MessageTemplateResponse.class);
        verify(templateResponseObserver).onNext(responseCaptor.capture());

        MessageTemplateResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.SERVER_INNER_ERROR.getCode(), response.getCode());
    }

    @Test
    public void testSendMessage_Success() {
        // Given
        MessageUser user = MessageUser.newBuilder()
                .setExtraId("extra-123")
                .setAppId("app-123")
                .setUsername("testuser")
                .setEmail("<EMAIL>")
                .setSysId("sys-123")
                .build();

        MessageRequest request = MessageRequest.newBuilder()
                .setMessageTemplateId(MessageTemplateId.newBuilder().setMessageTemplateId(123L))
                .addUsers(user)
                .build();

        MessageTemplateInfo templateInfo = new MessageTemplateInfo();
        templateInfo.setId(123L);
        templateInfo.setChannel(MessageChannelEnum.MI_WORK.getType());
        templateInfo.setTitleCn("Test Title");
        templateInfo.setContentCn("Test Content");

        when(templateCache.get(123L)).thenReturn(templateInfo);
        when(messageUserInfoMapper.batchInsert(anyList())).thenReturn(1);
        when(producerAndConsumerManager.getSysProducerAndConsumer("sys-123"))
                .thenReturn(Optional.empty());
        when(producerAndConsumerManager.getRobotProducerAndConsumer("app-123"))
                .thenReturn(Optional.empty());

        // When
        messageRpcService.sendMessage(request, userResponseObserver);

        // Then
        ArgumentCaptor<MessageUserResponse> responseCaptor = ArgumentCaptor.forClass(MessageUserResponse.class);
        verify(userResponseObserver).onNext(responseCaptor.capture());
        verify(userResponseObserver).onCompleted();

        MessageUserResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        verify(messageService).asyncSendMsg2EventBus(anyList());
    }

    @Test
    public void testSendMessage_WithSpecialQueue() {
        // Given
        MessageUser user = MessageUser.newBuilder()
                .setExtraId("extra-123")
                .setAppId("app-123")
                .setUsername("testuser")
                .setSysId("sys-123")
                .build();

        MessageRequest request = MessageRequest.newBuilder()
                .setMessageTemplateId(MessageTemplateId.newBuilder().setMessageTemplateId(123L))
                .addUsers(user)
                .build();

        MessageTemplateInfo templateInfo = new MessageTemplateInfo();
        templateInfo.setId(123L);
        templateInfo.setChannel(MessageChannelEnum.MI_WORK.getType());
        templateInfo.setTitleCn("Test Title");

        ProducerAndConsumer producerAndConsumer = mock(ProducerAndConsumer.class);

        when(templateCache.get(123L)).thenReturn(templateInfo);
        when(messageUserInfoMapper.batchInsert(anyList())).thenReturn(1);
        when(producerAndConsumerManager.getSysProducerAndConsumer("sys-123"))
                .thenReturn(Optional.of(producerAndConsumer));

        // When
        messageRpcService.sendMessage(request, userResponseObserver);

        // Then
        verify(producerAndConsumer).produce(anyList());
        verify(messageService, never()).asyncSendMsg2EventBus(anyList());
    }

    @Test
    public void testSendMessage_ParamError() {
        // Given
        MessageRequest request = MessageRequest.newBuilder().build(); // Empty request

        // When
        messageRpcService.sendMessage(request, userResponseObserver);

        // Then
        ArgumentCaptor<MessageUserResponse> responseCaptor = ArgumentCaptor.forClass(MessageUserResponse.class);
        verify(userResponseObserver).onNext(responseCaptor.capture());

        MessageUserResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.PARAM_ERROR.getCode(), response.getCode());
    }

    @Test
    public void testInnerSendMessage_Success() throws Exception {
        // Given
        MessageUser user = MessageUser.newBuilder()
                .setExtraId("extra-123")
                .setAppId("app-123")
                .setUsername("testuser")
                .build();

        MessageRequest request = MessageRequest.newBuilder()
                .setMessageTemplateId(MessageTemplateId.newBuilder().setMessageTemplateId(123L))
                .addUsers(user)
                .build();

        MessageTemplateInfo templateInfo = new MessageTemplateInfo();
        templateInfo.setId(123L);
        templateInfo.setChannel(MessageChannelEnum.MI_WORK.getType());

        MiWorkResponseBO responseBO = new MiWorkResponseBO();
        responseBO.setMsgId("msg-123");

        when(messageTemplateInfoMapper.selectById(123L)).thenReturn(templateInfo);
        when(enhanceMiWorkRobotManager.sendMsg(anyString(), any(MessageUserInfo.class), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);

        // When
        messageRpcService.innerSendMessage(request, userResponseObserver);

        // Then
        ArgumentCaptor<MessageUserResponse> responseCaptor = ArgumentCaptor.forClass(MessageUserResponse.class);
        verify(userResponseObserver).onNext(responseCaptor.capture());

        MessageUserResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
    }

    @Test
    public void testGetFinalContent() {
        // Given
        ExtraIdUsernameRequest request = ExtraIdUsernameRequest.newBuilder()
                .setExtraId("extra-123")
                .setUsername("testuser")
                .build();

        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setExtraId("extra-123");
        messageUserInfo.setUsername("testuser");
        messageUserInfo.setFinalContent("Final content");

        when(messageUserInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageUserInfo));

        // When
        messageRpcService.getFinalContent(request, finalContentResponseObserver);

        // Then
        ArgumentCaptor<FinalContentResponse> responseCaptor = ArgumentCaptor.forClass(FinalContentResponse.class);
        verify(finalContentResponseObserver).onNext(responseCaptor.capture());
        verify(finalContentResponseObserver).onCompleted();

        FinalContentResponse response = responseCaptor.getValue();
        assertEquals(1, response.getFinalContentCount());
    }

    @Test
    public void testInterruptMessageByExtraId() {
        // Given
        ExtraIdRequest request = ExtraIdRequest.newBuilder()
                .setExtraId("extra-123")
                .build();

        when(messageFilterInfoMapper.insertOrUpdateSelective(any(MessageFilterInfo.class))).thenReturn(1);
        when(messageUserInfoMapper.update(any(), any())).thenReturn(1);

        // When
        messageRpcService.interruptMessageByExtraId(request, commonResponseObserver);

        // Then
        ArgumentCaptor<CommonResponse> responseCaptor = ArgumentCaptor.forClass(CommonResponse.class);
        verify(commonResponseObserver).onNext(responseCaptor.capture());
        verify(commonResponseObserver).onCompleted();

        CommonResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        verify(sendMqEventBus).postEvent(any(SingleEvent.class));
    }

    @Test
    public void testRetractMessageByExtraId() {
        // Given
        ExtraIdRequest request = ExtraIdRequest.newBuilder()
                .setExtraId("extra-123")
                .build();

        when(messageFilterInfoMapper.insertOrUpdateSelective(any(MessageFilterInfo.class))).thenReturn(1);
        when(messageUserInfoMapper.update(any(), any())).thenReturn(1);
        doNothing().when(messageService).asyncRetractMessage(anyString());

        // When
        messageRpcService.retractMessageByExtraId(request, commonResponseObserver);

        // Then
        ArgumentCaptor<CommonResponse> responseCaptor = ArgumentCaptor.forClass(CommonResponse.class);
        verify(commonResponseObserver).onNext(responseCaptor.capture());

        CommonResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        verify(messageService).asyncRetractMessage("extra-123");
    }

    @Test
    public void testUploadLarkImage_Success() throws Exception {
        // Given
        ImageUrlRequest request = ImageUrlRequest.newBuilder()
                .setUrl("http://example.com/image.jpg")
                .setAppId("app-123")
                .build();

        when(miWorkRobotManager.uploadImageForLark("app-123", "http://example.com/image.jpg"))
                .thenReturn("image-key-123");

        // When
        messageRpcService.uploadLarkImage(request, imageKeyResponseObserver);

        // Then
        ArgumentCaptor<ImageKeyResponse> responseCaptor = ArgumentCaptor.forClass(ImageKeyResponse.class);
        verify(imageKeyResponseObserver).onNext(responseCaptor.capture());

        ImageKeyResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        assertEquals("image-key-123", response.getKey());
    }

    @Test
    public void testUploadLarkImage_Error() throws Exception {
        // Given
        ImageUrlRequest request = ImageUrlRequest.newBuilder()
                .setUrl("http://example.com/image.jpg")
                .setAppId("app-123")
                .build();

        when(miWorkRobotManager.uploadImageForLark("app-123", "http://example.com/image.jpg"))
                .thenThrow(new RuntimeException("Upload failed"));

        // When
        messageRpcService.uploadLarkImage(request, imageKeyResponseObserver);

        // Then
        ArgumentCaptor<ImageKeyResponse> responseCaptor = ArgumentCaptor.forClass(ImageKeyResponse.class);
        verify(imageKeyResponseObserver).onNext(responseCaptor.capture());

        ImageKeyResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.SERVER_INNER_ERROR.getCode(), response.getCode());
    }

    @Test
    public void testGetMessageNumberAndTimeByExtraIdBatch_Success() {
        // Given
        MessageExtraIdRequest request = MessageExtraIdRequest.newBuilder()
                .addExtraIdList("extra-123")
                .build();

        ExtraIdCountBO countBO = new ExtraIdCountBO();
        countBO.setExtraId("extra-123");
        countBO.setTotalCount(10L);

        ExtraIdCostTimeBO costTimeBO = new ExtraIdCostTimeBO();
        costTimeBO.setExtraId("extra-123");
        costTimeBO.setCostTime(1000L);

        ErrorTypeListBo errorTypeListBo = new ErrorTypeListBo();
        errorTypeListBo.setExtraId("extra-123");
        errorTypeListBo.setList(Arrays.asList(new ErrorTypeBo()));

        when(messageUserInfoMapper.selectCountByExtraIdList(anyList()))
                .thenReturn(Arrays.asList(countBO));
        when(messageUserInfoMapper.selectErrCountByExtraIdList(anyList(), anyList()))
                .thenReturn(Arrays.asList(errorTypeListBo));
        when(messageUserInfoMapper.selectCostTimeByExtraIdList(anyList()))
                .thenReturn(Arrays.asList(costTimeBO));

        // When
        messageRpcService.getMessageNumberAndTimeByExtraIdBatch(request, numberAndTimeResponseObserver);

        // Then
        ArgumentCaptor<MessageNumberAndTimeResponse> responseCaptor = 
                ArgumentCaptor.forClass(MessageNumberAndTimeResponse.class);
        verify(numberAndTimeResponseObserver).onNext(responseCaptor.capture());

        MessageNumberAndTimeResponse response = responseCaptor.getValue();
        assertEquals(200, response.getCode());
    }

    @Test
    public void testGetMessagePageByExtraId() {
        // Given
        MessageExtraIdPageRequest request = MessageExtraIdPageRequest.newBuilder()
                .setExtraId("extra-123")
                .setPage(1)
                .setSize(10)
                .build();

        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setExtraId("extra-123");

        Page<MessageUserInfo> page = new Page<>(1, 10);
        page.setRecords(Arrays.asList(messageUserInfo));
        page.setTotal(1);

        when(messageUserInfoMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(page);

        // When
        messageRpcService.getMessagePageByExtraId(request, customResponseObserver);

        // Then
        ArgumentCaptor<MessageResultSingleForCustomResponse> responseCaptor =
                ArgumentCaptor.forClass(MessageResultSingleForCustomResponse.class);
        verify(customResponseObserver).onNext(responseCaptor.capture());

        MessageResultSingleForCustomResponse response = responseCaptor.getValue();
        assertEquals(200, response.getCode());
    }

    @Test
    public void testGetMessageGroupDeptByExtraId() {
        // Given
        MessageExtraIdPageRequest request = MessageExtraIdPageRequest.newBuilder()
                .setExtraId("extra-123")
                .setPage(1)
                .setSize(10)
                .build();

        MessageGroupDeptRecordBO recordBO = new MessageGroupDeptRecordBO();

        when(messageUserInfoMapper.selectByGroupPage(any(Page.class), anyString(), anyList(), anyList(), anyList()))
                .thenReturn(Arrays.asList(recordBO));

        // When
        messageRpcService.getMessageGroupDeptByExtraId(request, deptResponseObserver);

        // Then
        ArgumentCaptor<MessageResultSingleForDeptResponse> responseCaptor =
                ArgumentCaptor.forClass(MessageResultSingleForDeptResponse.class);
        verify(deptResponseObserver).onNext(responseCaptor.capture());

        MessageResultSingleForDeptResponse response = responseCaptor.getValue();
        assertEquals(200, response.getCode());
    }

    @Test
    public void testGetAverageCostTimeByDateAndExtraIdBatch() {
        // Given
        AverageCostTimeByDate avgCostTime = AverageCostTimeByDate.newBuilder()
                .setDate("2023-01-01")
                .setChannel("lark")
                .addExtraIdList("extra-123")
                .build();

        CostTimeByDateAndExtraIdRequest request = CostTimeByDateAndExtraIdRequest.newBuilder()
                .addAverageCostTimeByDate(avgCostTime)
                .build();

        ExtraIdCostTimeBO costTimeBO = new ExtraIdCostTimeBO();
        costTimeBO.setExtraId("extra-123");
        costTimeBO.setCostTime(1000L);

        when(messageUserInfoMapper.selectSuccessOrInterruptCostTimeByExtraIdList(anyList()))
                .thenReturn(Arrays.asList(costTimeBO));

        // When
        messageRpcService.getAverageCostTimeByDateAndExtraIdBatch(request, costTimeResponseObserver);

        // Then
        verify(costTimeResponseObserver).onNext(any(CostTimeByDateAndExtraIdResponse.class));
        verify(costTimeResponseObserver).onCompleted();
    }

    @Test
    public void testGetMessageAverageCostTimeByExtraIdBatch() {
        // Given
        MessageAverageCostTime larkCostTime = MessageAverageCostTime.newBuilder()
                .setScope("lark")
                .addExtraIdList("extra-123")
                .build();

        MessageAverageCostTimeRequest request = MessageAverageCostTimeRequest.newBuilder()
                .addMessageAverageCostTime(larkCostTime)
                .build();

        when(messageUserInfoMapper.selectAverageCostTimeByExtraIdList(anyList()))
                .thenReturn(1000L);

        // When
        messageRpcService.getMessageAverageCostTimeByExtraIdBatch(request, avgCostTimeResponseObserver);

        // Then
        ArgumentCaptor<MessageAverageCostTimeResponse> responseCaptor =
                ArgumentCaptor.forClass(MessageAverageCostTimeResponse.class);
        verify(avgCostTimeResponseObserver).onNext(responseCaptor.capture());

        MessageAverageCostTimeResponse response = responseCaptor.getValue();
        assertEquals(200, response.getCode());
    }

    @Test
    public void testFetchGroupList_Success() throws Exception {
        // Given
        GroupListRequest request = GroupListRequest.newBuilder()
                .setAppId("app-123")
                .build();

        Group group = new Group();
        group.setChatId("chat-123");
        group.setName("Test Group");
        group.setOwnerUserId("user-123");

        LarkClient.GroupListResult groupListResult = new LarkClient.GroupListResult();
        groupListResult.setGroups(Arrays.asList(group));

        when(miWorkRobotManager.fetchGroupList("app-123")).thenReturn(groupListResult);

        // When
        messageRpcService.fetchGroupList(request, groupListResponseObserver);

        // Then
        ArgumentCaptor<GroupListResponse> responseCaptor = ArgumentCaptor.forClass(GroupListResponse.class);
        verify(groupListResponseObserver).onNext(responseCaptor.capture());

        GroupListResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
    }

    @Test
    public void testFetchGroupInfos_Success() {
        // Given
        GroupInfosRequest request = GroupInfosRequest.newBuilder()
                .setAppId("app-123")
                .addChatId("chat-123")
                .build();

        LarkClient.GroupInfoResult groupInfoResult = new LarkClient.GroupInfoResult();
        groupInfoResult.setChatId("chat-123");
        groupInfoResult.setName("Test Group");
        groupInfoResult.setOwnerUserId("user-123");

        GroupInfosBO groupInfosBO = new GroupInfosBO();
        groupInfosBO.setGroupInfoResults(Arrays.asList(groupInfoResult));

        when(miWorkRobotManager.fetchGroupInfos("app-123", Arrays.asList("chat-123")))
                .thenReturn(groupInfosBO);

        // When
        messageRpcService.fetchGroupInfos(request, groupInfosResponseObserver);

        // Then
        ArgumentCaptor<GroupInfosResponse> responseCaptor = ArgumentCaptor.forClass(GroupInfosResponse.class);
        verify(groupInfosResponseObserver).onNext(responseCaptor.capture());

        GroupInfosResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
    }

    @Test
    public void testGetMessageAvgCostTimeByExtraIdList() {
        // Given
        MessageAvgCostTimeRequest request = MessageAvgCostTimeRequest.newBuilder()
                .addExtraIdList("extra-123")
                .build();

        AvgCostTimeBO avgCostTimeBO = new AvgCostTimeBO();
        avgCostTimeBO.setExtraId("extra-123");
        avgCostTimeBO.setCostTime(1000L);

        when(messageUserInfoMapper.getAvgCostTimeByExtraIdList(anyList()))
                .thenReturn(Arrays.asList(avgCostTimeBO));

        // When
        messageRpcService.getMessageAvgCostTimeByExtraIdList(request, msgAvgCostTimeResponseObserver);

        // Then
        ArgumentCaptor<MessageAvgCostTimeResponse> responseCaptor =
                ArgumentCaptor.forClass(MessageAvgCostTimeResponse.class);
        verify(msgAvgCostTimeResponseObserver).onNext(responseCaptor.capture());

        MessageAvgCostTimeResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
    }

    @Test
    public void testGetMessageRead() {
        // Given
        MessageReadRequest request = MessageReadRequest.newBuilder()
                .setParentExtraId("parent-123")
                .addSubExtraIdList("sub-123")
                .build();

        CountAndCostTimeBO readCount = new CountAndCostTimeBO();
        readCount.setXCount(5L);

        CountAndCostTimeBO sendCount = new CountAndCostTimeBO();
        sendCount.setXCount(10L);

        CountAndCostTimeBO todoCount = new CountAndCostTimeBO();
        todoCount.setXCount(2L);

        CountAndCostTimeBO totalCount = new CountAndCostTimeBO();
        totalCount.setXCount(15L);
        totalCount.setCostTime(2000L);

        CountAndCostTimeBO retractCount = new CountAndCostTimeBO();
        retractCount.setXCount(1L);

        when(messageUserInfoMapper.selectReadCount1(anyString(), anyList())).thenReturn(readCount);
        when(messageUserInfoMapper.countByExtraIdListAndStatus(eq("parent-123"), eq(MessageStatusEnum.SEND_SUCCESS.getStatus())))
                .thenReturn(sendCount);
        when(messageUserInfoMapper.countToDo("parent-123")).thenReturn(todoCount);
        when(messageUserInfoMapper.countByExtraIdListAndStatus(eq("parent-123"), isNull()))
                .thenReturn(totalCount);
        when(messageUserInfoMapper.countByExtraIdListAndStatus(eq("parent-123"), eq(MessageStatusEnum.RETRACTED.getStatus())))
                .thenReturn(retractCount);

        // When
        messageRpcService.getMessageRead(request, readResponseObserver);

        // Then
        ArgumentCaptor<MessageReadResponse> responseCaptor = ArgumentCaptor.forClass(MessageReadResponse.class);
        verify(readResponseObserver).onNext(responseCaptor.capture());

        MessageReadResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        assertEquals(5, response.getMessageRead().getReadCount());
        assertEquals(10, response.getMessageRead().getSendCount());
        assertEquals(2, response.getMessageRead().getTodoCount());
        assertEquals(15, response.getMessageRead().getTotalCount());
        assertEquals(1, response.getMessageRead().getRetractCount());
        assertEquals(2000L, response.getMessageRead().getCostTime());
    }

    @Test
    public void testGetPageMessageUnReadDetail() {
        // Given
        MessageUnreadDetailRequest request = MessageUnreadDetailRequest.newBuilder()
                .setParentExtraId("parent-123")
                .addSubExtraId("sub-123")
                .setPageNo(1)
                .setPageSize(10)
                .setSearchKey("test")
                .addExcludeUsername("exclude1")
                .build();

        Page<String> page = new Page<>(1, 10);
        page.setRecords(Arrays.asList("user1", "user2"));
        page.setTotal(2);

        Page<String> pageWithoutSearch = new Page<>(1, 10);
        pageWithoutSearch.setTotal(5);

        when(messageUserInfoMapper.selectPageUnReadDetail(any(Page.class), eq("test"), any(HashSet.class), eq("parent-123"), anyList()))
                .thenReturn(page);
        when(messageUserInfoMapper.selectPageUnReadDetail(any(Page.class), eq(""), any(HashSet.class), eq("parent-123"), anyList()))
                .thenReturn(pageWithoutSearch);

        // When
        messageRpcService.getPageMessageUnReadDetail(request, unReadDetailResponseObserver);

        // Then
        ArgumentCaptor<MessageUnReadDetailResponse> responseCaptor =
                ArgumentCaptor.forClass(MessageUnReadDetailResponse.class);
        verify(unReadDetailResponseObserver).onNext(responseCaptor.capture());

        MessageUnReadDetailResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        assertEquals(2, response.getMessageUnreadDetail().getTotal());
        assertEquals(5, response.getMessageUnreadDetail().getTotalWithoutSearch());
    }

    @Test
    public void testGetMessageFailedDetail() {
        // Given
        ExtraIdRequest request = ExtraIdRequest.newBuilder()
                .setExtraId("extra-123")
                .build();

        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setUsername("failedUser");

        when(messageUserInfoMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Arrays.asList(messageUserInfo));

        // When
        messageRpcService.getMessageFailedDetail(request, usernameResponseObserver);

        // Then
        ArgumentCaptor<UsernameResponse> responseCaptor = ArgumentCaptor.forClass(UsernameResponse.class);
        verify(usernameResponseObserver).onNext(responseCaptor.capture());

        UsernameResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
        assertTrue(response.getUsernamesList().contains("failedUser"));
    }

    // 测试边界情况和私有方法覆盖

    @Test
    public void testSendMessage_EmailChannel() {
        // Given
        MessageUser user = MessageUser.newBuilder()
                .setExtraId("extra-123")
                .setAppId("app-123")
                .setEmail("<EMAIL>")
                .build();

        MessageRequest request = MessageRequest.newBuilder()
                .setMessageTemplateId(MessageTemplateId.newBuilder().setMessageTemplateId(123L))
                .addUsers(user)
                .build();

        MessageTemplateInfo templateInfo = new MessageTemplateInfo();
        templateInfo.setId(123L);
        templateInfo.setChannel(MessageChannelEnum.EMAIL.getType());

        when(templateCache.get(123L)).thenReturn(templateInfo);
        when(messageUserInfoMapper.batchInsert(anyList())).thenReturn(1);

        // When
        messageRpcService.sendMessage(request, userResponseObserver);

        // Then
        ArgumentCaptor<MessageUserResponse> responseCaptor = ArgumentCaptor.forClass(MessageUserResponse.class);
        verify(userResponseObserver).onNext(responseCaptor.capture());

        MessageUserResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
    }

    @Test
    public void testSendMessage_SmsChannel() {
        // Given
        MessageUser user = MessageUser.newBuilder()
                .setExtraId("extra-123")
                .setAppId("app-123")
                .setPhone("13800138000")
                .build();

        MessageRequest request = MessageRequest.newBuilder()
                .setMessageTemplateId(MessageTemplateId.newBuilder().setMessageTemplateId(123L))
                .addUsers(user)
                .build();

        MessageTemplateInfo templateInfo = new MessageTemplateInfo();
        templateInfo.setId(123L);
        templateInfo.setChannel(MessageChannelEnum.SMS.getType());

        when(templateCache.get(123L)).thenReturn(templateInfo);
        when(messageUserInfoMapper.batchInsert(anyList())).thenReturn(1);

        // When
        messageRpcService.sendMessage(request, userResponseObserver);

        // Then
        ArgumentCaptor<MessageUserResponse> responseCaptor = ArgumentCaptor.forClass(MessageUserResponse.class);
        verify(userResponseObserver).onNext(responseCaptor.capture());

        MessageUserResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.OK.getCode(), response.getCode());
    }

    @Test
    public void testGetFinalContent_NullFinalContent() {
        // Given
        ExtraIdUsernameRequest request = ExtraIdUsernameRequest.newBuilder()
                .setExtraId("extra-123")
                .setUsername("testuser")
                .build();

        MessageUserInfo messageUserInfo = new MessageUserInfo();
        messageUserInfo.setExtraId("extra-123");
        messageUserInfo.setUsername("testuser");
        messageUserInfo.setFinalContent(null);

        when(messageUserInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageUserInfo));

        // When
        messageRpcService.getFinalContent(request, finalContentResponseObserver);

        // Then
        ArgumentCaptor<FinalContentResponse> responseCaptor = ArgumentCaptor.forClass(FinalContentResponse.class);
        verify(finalContentResponseObserver).onNext(responseCaptor.capture());

        FinalContentResponse response = responseCaptor.getValue();
        assertEquals(1, response.getFinalContentCount());
    }

    @Test
    public void testGetMessageNumberAndTimeByExtraIdBatch_Exception() {
        // Given
        MessageExtraIdRequest request = MessageExtraIdRequest.newBuilder()
                .addExtraIdList("extra-123")
                .build();

        when(messageUserInfoMapper.selectCountByExtraIdList(anyList()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        messageRpcService.getMessageNumberAndTimeByExtraIdBatch(request, numberAndTimeResponseObserver);

        // Then
        ArgumentCaptor<MessageNumberAndTimeResponse> responseCaptor = 
                ArgumentCaptor.forClass(MessageNumberAndTimeResponse.class);
        verify(numberAndTimeResponseObserver).onNext(responseCaptor.capture());

        MessageNumberAndTimeResponse response = responseCaptor.getValue();
        assertEquals(500, response.getCode());
    }

    @Test
    public void testGetMessageAverageCostTimeByExtraIdBatch_Exception() {
        // Given
        MessageAverageCostTime larkCostTime = MessageAverageCostTime.newBuilder()
                .setScope("lark")
                .addExtraIdList("extra-123")
                .build();

        MessageAverageCostTimeRequest request = MessageAverageCostTimeRequest.newBuilder()
                .addMessageAverageCostTime(larkCostTime)
                .build();

        when(messageUserInfoMapper.selectAverageCostTimeByExtraIdList(anyList()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        messageRpcService.getMessageAverageCostTimeByExtraIdBatch(request, avgCostTimeResponseObserver);

        // Then
        ArgumentCaptor<MessageAverageCostTimeResponse> responseCaptor =
                ArgumentCaptor.forClass(MessageAverageCostTimeResponse.class);
        verify(avgCostTimeResponseObserver).onNext(responseCaptor.capture());

        MessageAverageCostTimeResponse response = responseCaptor.getValue();
        assertEquals(500, response.getCode());
    }

    @Test
    public void testFetchGroupList_Exception() throws Exception {
        // Given
        GroupListRequest request = GroupListRequest.newBuilder()
                .setAppId("app-123")
                .build();

        when(miWorkRobotManager.fetchGroupList("app-123"))
                .thenThrow(new RuntimeException("Fetch failed"));

        // When
        messageRpcService.fetchGroupList(request, groupListResponseObserver);

        // Then
        ArgumentCaptor<GroupListResponse> responseCaptor = ArgumentCaptor.forClass(GroupListResponse.class);
        verify(groupListResponseObserver).onNext(responseCaptor.capture());

        GroupListResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.SERVER_INNER_ERROR.getCode(), response.getCode());
    }

    @Test
    public void testFetchGroupInfos_Exception() {
        // Given
        GroupInfosRequest request = GroupInfosRequest.newBuilder()
                .setAppId("app-123")
                .addChatId("chat-123")
                .build();

        when(miWorkRobotManager.fetchGroupInfos("app-123", Arrays.asList("chat-123")))
                .thenThrow(new RuntimeException("Fetch failed"));

        // When
        messageRpcService.fetchGroupInfos(request, groupInfosResponseObserver);

        // Then
        ArgumentCaptor<GroupInfosResponse> responseCaptor = ArgumentCaptor.forClass(GroupInfosResponse.class);
        verify(groupInfosResponseObserver).onNext(responseCaptor.capture());

        GroupInfosResponse response = responseCaptor.getValue();
        assertEquals(ResultCode.SERVER_INNER_ERROR.getCode(), response.getCode());
    }
}
