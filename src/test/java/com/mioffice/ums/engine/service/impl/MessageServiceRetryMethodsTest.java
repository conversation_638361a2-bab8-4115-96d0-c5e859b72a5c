package com.mioffice.ums.engine.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mioffice.ums.engine.cache.TopicCache;
import com.mioffice.ums.engine.config.ManagerConfig;
import com.mioffice.ums.engine.config.MqProperties;
import com.mioffice.ums.engine.entity.info.MessageUserInfo;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.event.bus.SendMqEventBus;
import com.mioffice.ums.engine.manager.*;
import com.mioffice.ums.engine.mapper.MessageUserInfoMapper;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumer;
import com.mioffice.ums.engine.rocketmq.ProducerAndConsumerManager;
import com.mioffice.ums.engine.service.MessageService;
import com.mioffice.ums.engine.service.grpc.client.AppListGrpcClient;
import com.mioffice.ums.engine.service.grpc.server.LarkRobotRpcService;
import com.mioffice.ums.engine.service.grpc.server.MessageBotRpcService;
import com.mioffice.ums.engine.service.grpc.server.MessageRpcService;
import com.xiaomi.info.grpc.client.autoconfigure.MrpcClientAutoConfiguration;
import com.xiaomi.info.grpc.server.autoconfigure.MrpcServerAutoConfiguration;
import com.xiaomi.info.grpc.server.context.MrpcBeanFactory;
import com.xiaomi.info.grpc.server.soa.registry.ServerRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MessageService 重试方法测试类
 * 测试 retrySendFailMsg、retrySendingMsg、retryUnifiedTopicFailedMessages、retryUnifiedTopicSendingMessages 四个方法
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
@SpringBootTest
@ComponentScan(basePackages = "com.mioffice.ums.engine", excludeFilters
        = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {MrpcBeanFactory.class,
        MrpcServerAutoConfiguration.class, MrpcClientAutoConfiguration.class}))
@EnableAutoConfiguration(exclude = {MrpcClientAutoConfiguration.class, MrpcServerAutoConfiguration.class})
public class MessageServiceRetryMethodsTest {

    @Autowired
    private MessageService messageService;

    @Autowired
    private MessageUserInfoMapper messageUserInfoMapper;

    @MockBean
    private MessageRpcService messageRpcService;

    @MockBean
    private AppListGrpcClient appListGrpcClient;

    @MockBean
    private MessageBotRpcService messageBotRpcService;

    @MockBean
    private LarkRobotRpcService larkRobotRpcService;

    @MockBean
    private TopicCache topicCache;

    @MockBean
    private ManagerConfig managerConfig;

    @MockBean
    private EnhanceMiWorkRobotManager enhanceMiWorkRobotManager;

    @MockBean
    private MessageV1MiWorkRobotManager messageV1MiWorkRobotManager;

    @MockBean
    private EmailRobotManager emailRobotManager;

    @MockBean
    private SmsRobotManager smsRobotManager;

    @MockBean
    private IdDeduplicateManager idDeduplicateManager;

    @MockBean
    private ProducerAndConsumerManager producerAndConsumerManager;

    @MockBean
    private SendMqEventBus sendMqEventBus;

    @MockBean
    private MqProperties mqProperties;

    @MockBean
    private UnifiedTopicManager unifiedTopicManager;

    @MockBean
    private MrpcBeanFactory mrpcBeanFactory;

    @MockBean
    private ServerRegistry serverRegistry;

    @MockBean
    private LarkSaasRobotManager larkSaasRobotManager;

    @MockBean
    private LarkSaasV1RobotManager larkSaasV1RobotManager;

    private List<MessageUserInfo> testMessages;
    private MqProperties.Retry retryConfig;

    @BeforeEach
    void setUp() {
        // 设置重试配置
        retryConfig = new MqProperties.Retry();
        retryConfig.setLimit(50);
        retryConfig.setFailedAfter(1); // 1分钟后重试失败消息
        retryConfig.setSendingAfter(5); // 5分钟后重试发送中消息
        when(mqProperties.getRetry()).thenReturn(retryConfig);

        // 设置专属队列机器人ID
        Set<String> enableTopicRobotIds = new HashSet<>();
        enableTopicRobotIds.add("exclusive-robot-1");
        enableTopicRobotIds.add("exclusive-robot-2");
        when(producerAndConsumerManager.getEnableTopicRobotIdSet()).thenReturn(enableTopicRobotIds);

        // 设置统一Topic机器人ID
        Set<String> unifiedTopicRobotIds = new HashSet<>();
        unifiedTopicRobotIds.add("unified-robot-1");
        unifiedTopicRobotIds.add("unified-robot-2");
        when(producerAndConsumerManager.getUnifiedTopicRobotIds()).thenReturn(unifiedTopicRobotIds);

        // 设置统一Topic启用状态
        when(producerAndConsumerManager.isUnifiedTopicEnabled()).thenReturn(true);

        // 设置ProducerAndConsumer mock
        ProducerAndConsumer mockProducerAndConsumer = mock(ProducerAndConsumer.class);
        when(producerAndConsumerManager.getRobotProducerAndConsumer(anyString()))
                .thenReturn(Optional.of(mockProducerAndConsumer));

        // 设置统一Topic发送成功
        when(producerAndConsumerManager.sendToUnifiedTopic(anyList())).thenReturn(true);

        // 设置MqProperties的UnifiedTopic配置，避免UnifiedTopicManager空指针
        MqProperties.UnifiedTopic unifiedTopicConfig = new MqProperties.UnifiedTopic();
        unifiedTopicConfig.setEnabled(true);
        unifiedTopicConfig.setName("ums-unified-topic");
        unifiedTopicConfig.setRobotIds(unifiedTopicRobotIds);
        unifiedTopicConfig.setDelayMinSeconds(1);
        unifiedTopicConfig.setDelayMaxSeconds(10);
        when(mqProperties.getUnifiedTopic()).thenReturn(unifiedTopicConfig);

        // 设置MqProperties的RateLimit配置
        MqProperties.RateLimit rateLimit = new MqProperties.RateLimit();

        MqProperties.RateLimit.Email emailConfig = new MqProperties.RateLimit.Email();
        emailConfig.setRateMs(1000L);
        emailConfig.setRate(10L);
        rateLimit.setEmail(emailConfig);

        MqProperties.RateLimit.Sms smsConfig = new MqProperties.RateLimit.Sms();
        smsConfig.setRateMs(1000L);
        smsConfig.setRate(10L);
        rateLimit.setSms(smsConfig);

        MqProperties.RateLimit.Miwork miworkConfig = new MqProperties.RateLimit.Miwork();
        miworkConfig.setRateMs(1000L);
        miworkConfig.setRate(50L);
        miworkConfig.setSecondLimit(50);
        miworkConfig.setMinuteLimit(1000);
        rateLimit.setMiwork(miworkConfig);

        when(mqProperties.getRateLimit()).thenReturn(rateLimit);

        // 设置MqProperties的LarkApiUpgrade配置
        MqProperties.LarkApiUpgrade larkApiUpgrade = new MqProperties.LarkApiUpgrade();
        larkApiUpgrade.setEnableWhiteList(true);
        Set<String> whiteList = new HashSet<>();
        whiteList.add("test-app-id");
        larkApiUpgrade.setWhiteList(whiteList);
        when(mqProperties.getLarkApiUpgrade()).thenReturn(larkApiUpgrade);

        // 设置MqProperties的ExclusiveQueue配置
        MqProperties.ExclusiveQueue exclusiveQueue = new MqProperties.ExclusiveQueue();
        exclusiveQueue.setExcludeBots(new HashSet<>());
        when(mqProperties.getExclusiveQueue()).thenReturn(exclusiveQueue);

        // 设置UnifiedTopicManager的mock行为
        when(unifiedTopicManager.isUnifiedTopicEnabled()).thenReturn(true);
        when(unifiedTopicManager.getUnifiedTopicName()).thenReturn("ums-unified-topic");
        when(unifiedTopicManager.getUnifiedTopicRobotIds()).thenReturn(unifiedTopicRobotIds);
        when(unifiedTopicManager.shouldUseUnifiedTopic(anyString())).thenAnswer(invocation -> {
            String robotId = invocation.getArgument(0);
            return unifiedTopicRobotIds.contains(robotId);
        });

        // 准备测试数据
        prepareTestData();
    }

    private void prepareTestData() {
        testMessages = new ArrayList<>();
        
        // 清理可能存在的测试数据
        cleanupTestData();
        
        // 创建失败状态的测试消息
        MessageUserInfo failMsg1 = createTestMessage("test-fail-1", MessageStatusEnum.SEND_FAIL, "normal-robot-1");
        MessageUserInfo failMsg2 = createTestMessage("test-fail-2", MessageStatusEnum.SEND_FAIL, "unified-robot-1");
        MessageUserInfo failMsg3 = createTestMessage("test-fail-3", MessageStatusEnum.SEND_FAIL, "exclusive-robot-1");
        
        // 创建发送中状态的测试消息
        MessageUserInfo sendingMsg1 = createTestMessage("test-sending-1", MessageStatusEnum.SENDING, "normal-robot-2");
        MessageUserInfo sendingMsg2 = createTestMessage("test-sending-2", MessageStatusEnum.SENDING, "unified-robot-2");
        MessageUserInfo sendingMsg3 = createTestMessage("test-sending-3", MessageStatusEnum.SENDING, "exclusive-robot-2");
        
        testMessages.addAll(Arrays.asList(failMsg1, failMsg2, failMsg3, sendingMsg1, sendingMsg2, sendingMsg3));
        
        // 插入测试数据到数据库
        for (MessageUserInfo msg : testMessages) {
            messageUserInfoMapper.insert(msg);
        }
    }

    private MessageUserInfo createTestMessage(String extraId, MessageStatusEnum status, String appId) {
        MessageUserInfo msg = new MessageUserInfo();
        msg.setExtraId(extraId);
        msg.setAppId(appId);
        msg.setMessageStatus(status.getStatus());
        msg.setChannel(MessageChannelEnum.MI_WORK.getType());
        msg.setRetryCount(0);
        msg.setUpdateTime(System.currentTimeMillis() - 10 * 60 * 1000); // 10分钟前
        msg.setTitleCn("Test Title");
        msg.setExtraContent("Test Content");
        msg.setUsername("zuolei");
        msg.setSysId("S0138");
        return msg;
    }

    private void cleanupTestData() {
        // 清理测试数据
        LambdaQueryWrapper<MessageUserInfo> deleteWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                .like(MessageUserInfo::getExtraId, "test-");
        messageUserInfoMapper.delete(deleteWrapper);
    }

    @Test
    void testRetrySendFailMsg() throws InterruptedException {
        // 执行重试失败消息
        messageService.retrySendFailMsg();
        
        // 等待异步执行完成
        Thread.sleep(10000);
    }

    @Test
    void testRetrySendFailMsgOfTopic() throws InterruptedException {
        // 执行专属队列失败消息重试
        messageService.retrySendFailMsgOfTopic();
        
        // 等待异步执行完成
        Thread.sleep(1000);
    }

    @Test
    void testRetrySendingMsg() throws InterruptedException {
        // 执行重试发送中消息
        messageService.retrySendingMsg(null);
        
        // 等待异步执行完成
        Thread.sleep(1000);
        
        // 验证普通机器人的发送中消息被处理
        List<MessageUserInfo> sendingMessages = messageUserInfoMapper.selectList(
                Wrappers.<MessageUserInfo>lambdaQuery()
                        .like(MessageUserInfo::getExtraId, "test-sending-")
                        .eq(MessageUserInfo::getAppId, "normal-robot-2")
        );
        
        assertFalse(sendingMessages.isEmpty(), "应该有发送中消息被处理");
    }

    @Test
    void testRetrySendingMsgOfTopic() throws InterruptedException {
        // 执行专属队列发送中消息重试
        messageService.retrySendingMsgOfTopic(null);
        
        // 等待异步执行完成
        Thread.sleep(1000);
    }

    @Test
    void testRetryUnifiedTopicFailedMessages() throws InterruptedException {
        // 执行统一Topic失败消息重试
        messageService.retryUnifiedTopicFailedMessages();
        
        // 等待异步执行完成
        Thread.sleep(1000);
    }

    @Test
    void testRetryUnifiedTopicSendingMessages() throws InterruptedException {
        // 执行统一Topic发送中消息重试
        messageService.retryUnifiedTopicSendingMessages(null);
        
        // 等待异步执行完成
        Thread.sleep(1000);
        
        // 验证统一Topic机器人的发送中消息被处理
        MessageUserInfo unifiedSendingMsg = messageUserInfoMapper.selectOne(
                Wrappers.<MessageUserInfo>lambdaQuery()
                        .eq(MessageUserInfo::getExtraId, "test-sending-2")
                        .eq(MessageUserInfo::getAppId, "unified-robot-2")
        );
    }

    @Test
    void testRetryUnifiedTopicFailedMessages_Disabled() throws InterruptedException {
        // 设置统一Topic功能禁用
        when(producerAndConsumerManager.isUnifiedTopicEnabled()).thenReturn(false);
        
        // 执行统一Topic失败消息重试
        messageService.retryUnifiedTopicFailedMessages();
        
        // 等待异步执行完成
        Thread.sleep(100);
        
        // 验证统一Topic发送未被调用
        verify(producerAndConsumerManager, never()).sendToUnifiedTopic(anyList());
    }

    @Test
    void testRetryUnifiedTopicSendingMessages_Disabled() throws InterruptedException {
        // 设置统一Topic功能禁用
        when(producerAndConsumerManager.isUnifiedTopicEnabled()).thenReturn(false);
        
        // 执行统一Topic发送中消息重试
        messageService.retryUnifiedTopicSendingMessages(null);
        
        // 等待异步执行完成
        Thread.sleep(100);
        
        // 验证统一Topic发送未被调用
        verify(producerAndConsumerManager, never()).sendToUnifiedTopic(anyList());
    }

    @Test
    void testRetryUnifiedTopicFailedMessages_EmptyRobotIds() throws InterruptedException {
        // 设置空的统一Topic机器人ID列表
        when(producerAndConsumerManager.getUnifiedTopicRobotIds()).thenReturn(new HashSet<>());
        
        // 执行统一Topic失败消息重试
        messageService.retryUnifiedTopicFailedMessages();
        
        // 等待异步执行完成
        Thread.sleep(100);
        
        // 验证统一Topic发送未被调用
        verify(producerAndConsumerManager, never()).sendToUnifiedTopic(anyList());
    }

    @Test
    void testRetryUnifiedTopicSendingMessages_EmptyRobotIds() throws InterruptedException {
        // 设置空的统一Topic机器人ID列表
        when(producerAndConsumerManager.getUnifiedTopicRobotIds()).thenReturn(new HashSet<>());
        
        // 执行统一Topic发送中消息重试
        messageService.retryUnifiedTopicSendingMessages(null);
        
        // 等待异步执行完成
        Thread.sleep(100);
        
        // 验证统一Topic发送未被调用
        verify(producerAndConsumerManager, never()).sendToUnifiedTopic(anyList());
    }

    @Test
    void testRetryUnifiedTopicFailedMessages_SendToUnifiedTopicFails() throws InterruptedException {
        // 设置统一Topic发送失败
        when(producerAndConsumerManager.sendToUnifiedTopic(anyList())).thenReturn(false);

        // 执行统一Topic失败消息重试
        messageService.retryUnifiedTopicFailedMessages();

        // 等待异步执行完成
        Thread.sleep(1000);

        // 验证回退到Talos公共队列
        verify(sendMqEventBus, atLeastOnce()).postEvent(any());

        // 验证消息状态仍然更新为SENDING
        MessageUserInfo unifiedFailMsg = messageUserInfoMapper.selectOne(
                Wrappers.<MessageUserInfo>lambdaQuery()
                        .eq(MessageUserInfo::getExtraId, "test-fail-2")
                        .eq(MessageUserInfo::getAppId, "unified-robot-1")
        );

        assertNotNull(unifiedFailMsg);
        assertEquals(MessageStatusEnum.SENDING.getStatus(), unifiedFailMsg.getMessageStatus().byteValue());
        assertTrue(unifiedFailMsg.getRetryCount() > 0);
    }

    @Test
    void testRetryUnifiedTopicSendingMessages_SendToUnifiedTopicFails() throws InterruptedException {
        // 设置统一Topic发送失败
        when(producerAndConsumerManager.sendToUnifiedTopic(anyList())).thenReturn(false);

        // 执行统一Topic发送中消息重试
        messageService.retryUnifiedTopicSendingMessages(null);

        // 等待异步执行完成
        Thread.sleep(1000);

        // 验证回退到Talos公共队列
        verify(sendMqEventBus, atLeastOnce()).postEvent(any());
    }

    @Test
    void testRetrySendingMsg_WithCustomTimeMillis() throws InterruptedException {
        // 使用自定义时间参数
        messageService.retrySendingMsg(10L); // 10分钟前

        // 等待异步执行完成
        Thread.sleep(1000);
    }

    @Test
    void testRetrySendingMsgOfTopic_WithCustomTimeMillis() throws InterruptedException {
        // 使用自定义时间参数
        messageService.retrySendingMsgOfTopic(10L); // 10分钟前

        // 等待异步执行完成
        Thread.sleep(1000);

        // 验证ProducerAndConsumer的produce方法被调用
        verify(producerAndConsumerManager, atLeastOnce()).getRobotProducerAndConsumer(anyString());
    }

    @Test
    void testRetryUnifiedTopicSendingMessages_WithCustomTimeMillis() throws InterruptedException {
        // 使用自定义时间参数
        messageService.retryUnifiedTopicSendingMessages(10L); // 10分钟前

        // 等待异步执行完成
        Thread.sleep(1000);

        // 验证统一Topic发送被调用
        verify(producerAndConsumerManager, atLeastOnce()).sendToUnifiedTopic(anyList());
    }

    @Test
    void testRetrySendFailMsgOfTopic_NoProducerAndConsumer() throws InterruptedException {
        // 设置没有ProducerAndConsumer
        when(producerAndConsumerManager.getRobotProducerAndConsumer(anyString()))
                .thenReturn(Optional.empty());

        // 执行专属队列失败消息重试
        messageService.retrySendFailMsgOfTopic();

        // 等待异步执行完成
        Thread.sleep(1000);

        // 验证方法正常执行，不会抛出异常
        verify(producerAndConsumerManager, atLeastOnce()).getRobotProducerAndConsumer(anyString());
    }

    @Test
    void testRetrySendingMsgOfTopic_NoProducerAndConsumer() throws InterruptedException {
        // 设置没有ProducerAndConsumer
        when(producerAndConsumerManager.getRobotProducerAndConsumer(anyString()))
                .thenReturn(Optional.empty());

        // 执行专属队列发送中消息重试
        messageService.retrySendingMsgOfTopic(null);
    }

    @Test
    void testRetrySendFailMsg_EmptyExcludeAppIds() throws InterruptedException {
        // 设置空的排除应用ID列表
        when(producerAndConsumerManager.getEnableTopicRobotIdSet()).thenReturn(new HashSet<>());
        when(producerAndConsumerManager.getUnifiedTopicRobotIds()).thenReturn(new HashSet<>());

        // 执行重试失败消息
        messageService.retrySendFailMsg();

        // 等待异步执行完成
        Thread.sleep(1000);

        // 验证所有普通机器人的消息都被处理
        List<MessageUserInfo> allFailMessages = messageUserInfoMapper.selectList(
                Wrappers.<MessageUserInfo>lambdaQuery()
                        .like(MessageUserInfo::getExtraId, "test-fail-")
                        .eq(MessageUserInfo::getMessageStatus, MessageStatusEnum.SENDING.getStatus())
        );

        assertFalse(allFailMessages.isEmpty(), "应该有更多消息被处理");

        // 验证事件发布被调用
        verify(sendMqEventBus, atLeastOnce()).postEvent(any());
    }

    @Test
    void testRetrySendingMsg_EmptyExcludeAppIds() throws InterruptedException {
        // 设置空的排除应用ID列表
        when(producerAndConsumerManager.getEnableTopicRobotIdSet()).thenReturn(new HashSet<>());
        when(producerAndConsumerManager.getUnifiedTopicRobotIds()).thenReturn(new HashSet<>());

        // 执行重试发送中消息
        messageService.retrySendingMsg(null);
    }

    @Test
    void testRetryUnifiedTopicFailedMessages_MaxBatchCountReached() throws InterruptedException {
        // 创建大量测试数据来触发最大批次限制
        createLargeTestDataSet("large-fail-", MessageStatusEnum.SEND_FAIL, "unified-robot-1", 1000);

        try {
            // 执行统一Topic失败消息重试
            messageService.retryUnifiedTopicFailedMessages();

            // 等待异步执行完成
            Thread.sleep(5000);

            // 验证统一Topic发送被调用多次
            verify(producerAndConsumerManager, atLeast(1)).sendToUnifiedTopic(anyList());

        } finally {
            // 清理大量测试数据
            cleanupLargeTestData("large-fail-");
        }
    }

    @Test
    void testRetryUnifiedTopicSendingMessages_MaxBatchCountReached() throws InterruptedException {
        // 创建大量测试数据来触发最大批次限制
        createLargeTestDataSet("large-sending-", MessageStatusEnum.SENDING, "unified-robot-2", 1000);

        try {
            // 执行统一Topic发送中消息重试
            messageService.retryUnifiedTopicSendingMessages(null);

            // 等待异步执行完成
            Thread.sleep(5000);

            // 验证统一Topic发送被调用多次
            verify(producerAndConsumerManager, atLeast(1)).sendToUnifiedTopic(anyList());

        } finally {
            // 清理大量测试数据
            cleanupLargeTestData("large-sending-");
        }
    }

    private void createLargeTestDataSet(String extraIdPrefix, MessageStatusEnum status, String appId, int count) {
        List<MessageUserInfo> largeDataSet = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            MessageUserInfo msg = createTestMessage(extraIdPrefix + i, status, appId);
            largeDataSet.add(msg);
        }

        // 批量插入
        for (MessageUserInfo msg : largeDataSet) {
            messageUserInfoMapper.insert(msg);
        }
    }

    private void cleanupLargeTestData(String extraIdPrefix) {
        LambdaQueryWrapper<MessageUserInfo> deleteWrapper = Wrappers.<MessageUserInfo>lambdaQuery()
                .like(MessageUserInfo::getExtraId, extraIdPrefix);
        messageUserInfoMapper.delete(deleteWrapper);
    }

    @Test
    void testRetrySendFailMsg_RetryLimitExceeded() throws InterruptedException {
        // 创建重试次数已达到限制的消息
        MessageUserInfo limitExceededMsg = createTestMessage("test-limit-exceeded", MessageStatusEnum.SEND_FAIL, "normal-robot-limit");
        limitExceededMsg.setRetryCount(60); // 超过限制的50次
        messageUserInfoMapper.insert(limitExceededMsg);

        try {
            // 执行重试失败消息
            messageService.retrySendFailMsg();

            // 等待异步执行完成
            Thread.sleep(1000);
        } finally {
            // 清理测试数据
            messageUserInfoMapper.delete(Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getExtraId, "test-limit-exceeded"));
        }
    }

    @Test
    void testRetrySendFailMsgOfTopic_RetryLimitExceeded() throws InterruptedException {
        // 创建重试次数已达到限制的专属队列消息
        MessageUserInfo limitExceededMsg = createTestMessage("test-topic-limit-exceeded", MessageStatusEnum.SEND_FAIL, "exclusive-robot-1");
        limitExceededMsg.setRetryCount(60); // 超过限制的50次
        messageUserInfoMapper.insert(limitExceededMsg);

        try {
            // 执行专属队列失败消息重试
            messageService.retrySendFailMsgOfTopic();

            // 等待异步执行完成
            Thread.sleep(1000);
        } finally {
            // 清理测试数据
            messageUserInfoMapper.delete(Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getExtraId, "test-topic-limit-exceeded"));
        }
    }

    @Test
    void testRetryUnifiedTopicFailedMessages_RetryLimitExceeded() throws InterruptedException {
        // 创建重试次数已达到限制的统一Topic消息
        MessageUserInfo limitExceededMsg = createTestMessage("test-unified-limit-exceeded", MessageStatusEnum.SEND_FAIL, "unified-robot-1");
        limitExceededMsg.setRetryCount(60); // 超过限制的50次
        messageUserInfoMapper.insert(limitExceededMsg);

        try {
            // 执行统一Topic失败消息重试
            messageService.retryUnifiedTopicFailedMessages();
        } finally {
            // 清理测试数据
            messageUserInfoMapper.delete(Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getExtraId, "test-unified-limit-exceeded"));
        }
    }

    @Test
    void testRetrySendFailMsg_RecentUpdateTime() throws InterruptedException {
        // 创建最近更新的消息（不应该被重试）
        MessageUserInfo recentMsg = createTestMessage("test-recent", MessageStatusEnum.SEND_FAIL, "normal-robot-recent");
        recentMsg.setUpdateTime(System.currentTimeMillis() - 30 * 1000); // 30秒前（小于1分钟）
        messageUserInfoMapper.insert(recentMsg);

        try {
            // 执行重试失败消息
            messageService.retrySendFailMsg();

            // 等待异步执行完成
            Thread.sleep(1000);

        } finally {
            // 清理测试数据
            messageUserInfoMapper.delete(Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getExtraId, "test-recent"));
        }
    }

    @Test
    void testRetrySendingMsg_RecentUpdateTime() throws InterruptedException {
        // 创建最近更新的发送中消息（不应该被重试）
        MessageUserInfo recentSendingMsg = createTestMessage("test-recent-sending", MessageStatusEnum.SENDING, "normal-robot-recent-sending");
        recentSendingMsg.setUpdateTime(System.currentTimeMillis() - 2 * 60 * 1000); // 2分钟前（小于5分钟）
        messageUserInfoMapper.insert(recentSendingMsg);

        try {
            // 执行重试发送中消息
            messageService.retrySendingMsg(null);

            // 等待异步执行完成
            Thread.sleep(1000);


        } finally {
            // 清理测试数据
            messageUserInfoMapper.delete(Wrappers.<MessageUserInfo>lambdaQuery()
                    .eq(MessageUserInfo::getExtraId, "test-recent-sending"));
        }
    }

    @Test
    void testRetryMethods_DifferentChannels() throws InterruptedException {
        // 创建不同渠道的测试消息
        MessageUserInfo emailMsg = createTestMessage("test-email", MessageStatusEnum.SEND_FAIL, "normal-robot-email");
        emailMsg.setChannel(MessageChannelEnum.EMAIL.getType());

        MessageUserInfo smsMsg = createTestMessage("test-sms", MessageStatusEnum.SEND_FAIL, "normal-robot-sms");
        smsMsg.setChannel(MessageChannelEnum.SMS.getType());

        messageUserInfoMapper.insert(emailMsg);
        messageUserInfoMapper.insert(smsMsg);

        try {
            // 执行重试失败消息
            messageService.retrySendFailMsg();

            // 等待异步执行完成
            Thread.sleep(5000);

            // 验证不同渠道的消息都被处理
            MessageUserInfo updatedEmailMsg = messageUserInfoMapper.selectOne(
                    Wrappers.<MessageUserInfo>lambdaQuery()
                            .eq(MessageUserInfo::getExtraId, "test-email")
            );

            MessageUserInfo updatedSmsMsg = messageUserInfoMapper.selectOne(
                    Wrappers.<MessageUserInfo>lambdaQuery()
                            .eq(MessageUserInfo::getExtraId, "test-sms")
            );

            assertNotNull(updatedEmailMsg);
            assertNotNull(updatedSmsMsg);
            assertEquals(MessageStatusEnum.SENDING.getStatus(), updatedEmailMsg.getMessageStatus().byteValue());
            assertEquals(MessageStatusEnum.SENDING.getStatus(), updatedSmsMsg.getMessageStatus().byteValue());

            // 验证事件发布被调用（不同渠道会分别发布事件）
            verify(sendMqEventBus, atLeastOnce()).postEvent(any());

        } finally {
            // 清理测试数据
            messageUserInfoMapper.delete(Wrappers.<MessageUserInfo>lambdaQuery()
                    .in(MessageUserInfo::getExtraId, Arrays.asList("test-email", "test-sms")));
        }
    }

    // 清理测试数据
    @org.junit.jupiter.api.AfterEach
    void tearDown() {
        cleanupTestData();
    }
}
