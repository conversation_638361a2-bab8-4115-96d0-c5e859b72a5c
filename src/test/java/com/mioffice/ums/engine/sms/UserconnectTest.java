package com.mioffice.ums.engine.sms;

import com.google.gson.Gson;
import com.mioffice.ums.engine.entity.bo.SmsReqBo;
import com.mioffice.ums.engine.entity.bo.SmsResponseBO;
import com.mioffice.ums.engine.robot.SmsRobot;
import com.xiaomi.miliao.thrift.ClientFactory;
import com.xiaomi.miliao.zookeeper.EnvironmentType;
import com.xiaomi.miliao.zookeeper.ZKFacade;
import com.xiaomi.sms.SmsUserconnectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Test;

/**
 * <AUTHOR>
 * @since 2020/8/31
 */
@Slf4j
public class UserconnectTest {
    @Test
    public void send() throws JSONException, TException {
        ZKFacade.getZKSettings().setEnviromentType(EnvironmentType.STAGING);
        SmsUserconnectService.Iface client = ClientFactory.getClient(SmsUserconnectService.Iface.class);
        //收件人联系方式
        JSONObject message = new JSONObject();
        message.put("msg", "测试");  //假设模板中含有变量var1, 比如 "Hi {var1}, 你好",那么这里直接指定变量，但变量名不能与userid, address, type, content_type等保留字重复
        String resultJsonStr = client.sendMessageByPhone("17600181980", "CI98444_100195",message.toString());
        //resultJsonStr: {"msgid":"1509527311085-4652904681433523400","desc":"success","errorCode":0}
        log.info("{}", resultJsonStr);
    }

    @Test
    public void sendSMS() throws Exception {
        ZKFacade.getZKSettings().setEnviromentType(EnvironmentType.STAGING);

        SmsUserconnectService.Iface client = ClientFactory.getClient(SmsUserconnectService.Iface.class);
        SmsRobot smsRobot = new SmsRobot("CI98444_100195", client, new Gson());

        SmsReqBo smsReqBo = new SmsReqBo();
        smsReqBo.setPhone("17600181980");
        smsReqBo.setContent("测试短信");


        SmsResponseBO smsResponseBO = smsRobot.sendMsg(smsReqBo);
        System.out.println(smsResponseBO);
    }
}
