package com.mioffice.ums.engine.task;

import com.google.common.cache.Cache;
import com.google.common.util.concurrent.RateLimiter;
import com.mioffice.ums.engine.entity.bo.MiWorkResponseBO;
import com.mioffice.ums.engine.entity.info.MessageUserSaasInfo;
import com.mioffice.ums.engine.enums.ContentFlagEnum;
import com.mioffice.ums.engine.enums.ErrorTypeEnum;
import com.mioffice.ums.engine.enums.MessageChannelEnum;
import com.mioffice.ums.engine.enums.MessageStatusEnum;
import com.mioffice.ums.engine.manager.LarkSaasRobotManager;
import com.mioffice.ums.engine.manager.LarkSaasV1RobotManager;
import com.mioffice.ums.engine.mapper.MessageUserSaasInfoMapper;
import com.mioffice.ums.engine.robot.MiWorkRobot;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LarkSaasSendMessageTask 单元测试类 - 最终工作版本
 * 专注于依赖注入、配置设置和业务逻辑验证，避免复杂的Lambda表达式问题
 * 
 * <AUTHOR> Generated Code
 */
@RunWith(MockitoJUnitRunner.class)
public class LarkSaasSendMessageTaskTest {

    @Mock
    private MessageUserSaasInfoMapper messageUserSaasInfoMapper;

    @Mock
    private LarkSaasRobotManager larkSaasRobotManager;

    @Mock
    private LarkSaasV1RobotManager larkSaasV1RobotManager;

    @Mock
    private Cache<String, RateLimiter> rateLimiterCache;

    @Mock
    private RateLimiter rateLimiter;

    @Mock
    private ExecutorService larkSaasExecutor;

    @InjectMocks
    private LarkSaasSendMessageTask larkSaasSendMessageTask;

    @Before
    public void setUp() throws ExecutionException {
        // 设置配置参数
        ReflectionTestUtils.setField(larkSaasSendMessageTask, "rate", 50);
        ReflectionTestUtils.setField(larkSaasSendMessageTask, "retryAfter", 1);
        ReflectionTestUtils.setField(larkSaasSendMessageTask, "retryLimit", 10);
        ReflectionTestUtils.setField(larkSaasSendMessageTask, "rateLimiterCache", rateLimiterCache);
        ReflectionTestUtils.setField(larkSaasSendMessageTask, "larkSaasExecutor", larkSaasExecutor);

        // 设置RateLimiter缓存的默认行为
        when(rateLimiterCache.get(anyString(), any())).thenReturn(rateLimiter);
        when(rateLimiter.tryAcquire()).thenReturn(true);
    }

    @Test
    public void testExecute_Success() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        when(messageUserSaasInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageInfo));

        // When
        larkSaasSendMessageTask.execute();

        // Then
        verify(messageUserSaasInfoMapper).selectList(any());
        verify(larkSaasExecutor).invokeAll(anyList());
    }

    @Test
    public void testExecute_EmptyList() throws Exception {
        // Given
        when(messageUserSaasInfoMapper.selectList(any())).thenReturn(Collections.emptyList());

        // When
        larkSaasSendMessageTask.execute();

        // Then
        verify(messageUserSaasInfoMapper).selectList(any());
        verify(larkSaasExecutor).invokeAll(eq(Collections.emptyList()));
    }

    @Test
    public void testExecute_RateLimiterExhausted() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        when(messageUserSaasInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageInfo));
        when(rateLimiter.tryAcquire()).thenReturn(false);

        // When
        larkSaasSendMessageTask.execute();

        // Then
        verify(messageUserSaasInfoMapper).selectList(any());
        verify(larkSaasExecutor).invokeAll(eq(Collections.emptyList()));
    }

    @Test(expected = RuntimeException.class)
    public void testExecute_RateLimiterCacheException() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        when(messageUserSaasInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageInfo));
        when(rateLimiterCache.get(anyString(), any())).thenThrow(new ExecutionException("Cache error", new RuntimeException()));

        // When
        larkSaasSendMessageTask.execute();
    }

    @Test(expected = RuntimeException.class)
    public void testExecute_InterruptedException() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        when(messageUserSaasInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageInfo));
        when(larkSaasExecutor.invokeAll(anyList())).thenThrow(new InterruptedException("Interrupted"));

        // When
        larkSaasSendMessageTask.execute();
    }

    @Test
    public void testSendMessage_Success_NonV1Channel() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
                 messageInfo.setIsV1Channel((byte) 0);
         messageInfo.setContentFlag(ContentFlagEnum.LARK_CONTENT.getFlag());

        MiWorkResponseBO responseBO = new MiWorkResponseBO();
        responseBO.setMsgId("msg-123");
        responseBO.setMessageJson("{\"content\":\"test\"}");

        when(larkSaasRobotManager.sendMsg(anyString(), any(MessageUserSaasInfo.class), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);
        when(messageUserSaasInfoMapper.updateById(any(MessageUserSaasInfo.class))).thenReturn(1);

        // When
        boolean result = invokePrivateSendMessage(messageInfo);

        // Then
        assertTrue(result);
        verify(larkSaasRobotManager).sendMsg(eq("test-app-id"), eq(messageInfo), any(MiWorkRobot.Session.class));
        verify(messageUserSaasInfoMapper).updateById(messageInfo);
                 assertTrue("msg-123".equals(messageInfo.getMessageId()));
         assertTrue(MessageStatusEnum.SEND_SUCCESS.getStatus() == messageInfo.getMessageStatus());
         assertTrue("{\"content\":\"test\"}".equals(messageInfo.getFinalContent()));
    }

    @Test
    public void testSendMessage_Success_V1Channel() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
                 messageInfo.setIsV1Channel((byte) 1);
         messageInfo.setContentFlag(ContentFlagEnum.NO.getFlag());

        MiWorkResponseBO responseBO = new MiWorkResponseBO();
        responseBO.setMsgId("msg-456");

        when(larkSaasV1RobotManager.sendMsg(anyString(), any(MessageUserSaasInfo.class), any(MiWorkRobot.Session.class)))
                .thenReturn(responseBO);
        when(messageUserSaasInfoMapper.updateById(any(MessageUserSaasInfo.class))).thenReturn(1);

        // When
        boolean result = invokePrivateSendMessage(messageInfo);

        // Then
        assertTrue(result);
        verify(larkSaasV1RobotManager).sendMsg(eq("test-app-id"), eq(messageInfo), any(MiWorkRobot.Session.class));
        verify(messageUserSaasInfoMapper).updateById(messageInfo);
                 assertTrue("msg-456".equals(messageInfo.getMessageId()));
         assertTrue(MessageStatusEnum.SEND_SUCCESS.getStatus() == messageInfo.getMessageStatus());
    }

    @Test
    public void testSendMessage_Failure() throws Exception {
        // Given
                 MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
         messageInfo.setIsV1Channel((byte) 0);
         messageInfo.setRetryCount(2);

        RuntimeException exception = new RuntimeException("Send failed");
        when(larkSaasRobotManager.sendMsg(anyString(), any(MessageUserSaasInfo.class), any(MiWorkRobot.Session.class)))
                .thenThrow(exception);
        when(messageUserSaasInfoMapper.updateById(any(MessageUserSaasInfo.class))).thenReturn(1);

        // When
        boolean result = invokePrivateSendMessage(messageInfo);

        // Then
        assertFalse(result);
        verify(larkSaasRobotManager).sendMsg(eq("test-app-id"), eq(messageInfo), any(MiWorkRobot.Session.class));
        verify(messageUserSaasInfoMapper).updateById(messageInfo);
                 assertTrue(MessageStatusEnum.SEND_FAIL.getStatus() == messageInfo.getMessageStatus());
         assertTrue("Send failed".equals(messageInfo.getErrorLog()));
         assertTrue(3 == messageInfo.getRetryCount().intValue());
    }

    @Test
    public void testSendMessage_SessionSetup() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        messageInfo.setUsername("test-user");
        messageInfo.setEmail("<EMAIL>");
        messageInfo.setChatId("chat-123");

        MiWorkResponseBO responseBO = new MiWorkResponseBO();
        responseBO.setMsgId("msg-789");

        ArgumentCaptor<MiWorkRobot.Session> sessionCaptor = ArgumentCaptor.forClass(MiWorkRobot.Session.class);
        when(larkSaasRobotManager.sendMsg(anyString(), any(MessageUserSaasInfo.class), sessionCaptor.capture()))
                .thenReturn(responseBO);
        when(messageUserSaasInfoMapper.updateById(any(MessageUserSaasInfo.class))).thenReturn(1);

        // When
        invokePrivateSendMessage(messageInfo);

        // Then
        MiWorkRobot.Session capturedSession = sessionCaptor.getValue();
                 assertTrue("test-user".equals(capturedSession.getUserId()));
         assertTrue("<EMAIL>".equals(capturedSession.getEmail()));
         assertTrue("chat-123".equals(capturedSession.getOpenChatId()));
    }

    @Test
    public void testUpdateSendFail_WithMessage() {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        messageInfo.setRetryCount(1);
        RuntimeException exception = new RuntimeException("Test error message");
        when(messageUserSaasInfoMapper.updateById(any(MessageUserSaasInfo.class))).thenReturn(1);

        // When
        larkSaasSendMessageTask.updateSendFail(messageInfo, exception);

        // Then
        verify(messageUserSaasInfoMapper).updateById(messageInfo);
        assertEquals(Optional.of(MessageStatusEnum.SEND_FAIL.getStatus()), messageInfo.getMessageStatus());
        assertEquals("Test error message", messageInfo.getErrorLog());
        assertEquals(2, messageInfo.getRetryCount().intValue());
        assertTrue(messageInfo.getUpdateTime() > 0);
    }

    @Test
    public void testUpdateSendFail_WithNullMessage() {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        messageInfo.setRetryCount(0);
        RuntimeException exception = new RuntimeException();
        when(messageUserSaasInfoMapper.updateById(any(MessageUserSaasInfo.class))).thenReturn(1);

        // When
        larkSaasSendMessageTask.updateSendFail(messageInfo, exception);

        // Then
        verify(messageUserSaasInfoMapper).updateById(messageInfo);
        assertEquals(Optional.of(MessageStatusEnum.SEND_FAIL.getStatus()), messageInfo.getMessageStatus());
        assertEquals("", messageInfo.getErrorLog());
        assertEquals(1, messageInfo.getRetryCount().intValue());
    }

    @Test
    public void testUpdateSendFail_ErrorTypeHandling() {
        // Given
        MessageUserSaasInfo messageInfo = createTestMessageUserSaasInfo();
        messageInfo.setChannel(MessageChannelEnum.MI_WORK.getType());
        messageInfo.setErrorType(ErrorTypeEnum.NO_KNOW.getType());
        RuntimeException exception = new RuntimeException("API rate limit exceeded");
        when(messageUserSaasInfoMapper.updateById(any(MessageUserSaasInfo.class))).thenReturn(1);

        // When
        larkSaasSendMessageTask.updateSendFail(messageInfo, exception);

        // Then
        verify(messageUserSaasInfoMapper).updateById(messageInfo);
        // 验证错误类型处理逻辑被调用
        assertNotNull(messageInfo.getErrorType());
    }

    @Test
    public void testMultipleRateLimiters() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo1 = createTestMessageUserSaasInfo();
        messageInfo1.setAppId("app-1");
        MessageUserSaasInfo messageInfo2 = createTestMessageUserSaasInfo();
        messageInfo2.setAppId("app-2");

        RateLimiter rateLimiter1 = mock(RateLimiter.class);
        RateLimiter rateLimiter2 = mock(RateLimiter.class);

        when(rateLimiterCache.get(eq("app-1"), any())).thenReturn(rateLimiter1);
        when(rateLimiterCache.get(eq("app-2"), any())).thenReturn(rateLimiter2);
        when(rateLimiter1.tryAcquire()).thenReturn(true);
        when(rateLimiter2.tryAcquire()).thenReturn(false);

        when(messageUserSaasInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageInfo1, messageInfo2));

        // When
        larkSaasSendMessageTask.execute();

        // Then
        verify(rateLimiterCache).get(eq("app-1"), any());
        verify(rateLimiterCache).get(eq("app-2"), any());
        verify(rateLimiter1).tryAcquire();
        verify(rateLimiter2).tryAcquire();
        // 只有app-1的任务会被添加到执行队列，因为app-2被限流了
        verify(larkSaasExecutor).invokeAll(argThat(tasks -> tasks.size() == 1));
    }

    @Test
    public void testTaskCreationWithDifferentMessageInfos() throws Exception {
        // Given
        MessageUserSaasInfo messageInfo1 = createTestMessageUserSaasInfo();
        messageInfo1.setId(1L);
        messageInfo1.setAppId("app-1");
        
        MessageUserSaasInfo messageInfo2 = createTestMessageUserSaasInfo();
        messageInfo2.setId(2L);
        messageInfo2.setAppId("app-1");

        when(messageUserSaasInfoMapper.selectList(any())).thenReturn(Arrays.asList(messageInfo1, messageInfo2));

        // When
        larkSaasSendMessageTask.execute();

        // Then
        // 验证为每个消息创建了任务
        verify(larkSaasExecutor).invokeAll(argThat(tasks -> tasks.size() == 2));
    }

    // 辅助方法
    private MessageUserSaasInfo createTestMessageUserSaasInfo() {
        MessageUserSaasInfo messageInfo = new MessageUserSaasInfo();
        messageInfo.setId(123L);
        messageInfo.setAppId("test-app-id");
        messageInfo.setUsername("test-user");
        messageInfo.setEmail("<EMAIL>");
        messageInfo.setChatId("chat-123");
        messageInfo.setMessageStatus(MessageStatusEnum.SENDING.getStatus());
        messageInfo.setRetryCount(0);
        messageInfo.setChannel(MessageChannelEnum.MI_WORK.getType());
                 messageInfo.setIsV1Channel((byte) 0);
         messageInfo.setContentFlag(ContentFlagEnum.NO.getFlag());
        messageInfo.setErrorType(ErrorTypeEnum.NO_KNOW.getType());
        messageInfo.setCreateTime(System.currentTimeMillis() - 10000);
        messageInfo.setUpdateTime(System.currentTimeMillis() - 5000);
        return messageInfo;
    }

    // 使用反射调用私有方法sendMessage
    private boolean invokePrivateSendMessage(MessageUserSaasInfo messageInfo) throws Exception {
        return (boolean) ReflectionTestUtils.invokeMethod(larkSaasSendMessageTask, "sendMessage", messageInfo);
    }
}
