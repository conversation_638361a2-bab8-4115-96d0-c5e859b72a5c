package com.mioffice.ums.engine.task;

import com.mioffice.ums.engine.service.MessageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 统一主题重试任务测试类
 * 测试RetryUnifiedTopicFailMessageTask和RetryUnifiedTopicSendingMessageTask
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
class RetryUnifiedTopicTasksTest {

    @Mock
    private MessageService messageService;

    private RetryUnifiedTopicFailMessageTask retryFailTask;
    private RetryUnifiedTopicSendingMessageTask retrySendingTask;

    @BeforeEach
    void setUp() {
        retryFailTask = new RetryUnifiedTopicFailMessageTask(messageService);
        retrySendingTask = new RetryUnifiedTopicSendingMessageTask(messageService);
    }

    /**
     * 测试场景1：统一主题失败消息重试任务执行
     * 
     * 验证点：
     * - 正确调用MessageService的重试方法
     * - 不依赖任何参数
     */
    @Test
    void testRetryUnifiedTopicFailMessageTask_Execute() {
        // Given
        doNothing().when(messageService).retryUnifiedTopicFailedMessages();

        // When
        retryFailTask.execute();

        // Then
        verify(messageService).retryUnifiedTopicFailedMessages();
    }

    /**
     * 测试场景2：统一主题失败消息重试任务 - MessageService异常处理
     * 
     * 验证点：
     * - MessageService抛出异常
     * - 异常能正确向上传播
     * - 任务不会吞没异常
     */
    @Test
    void testRetryUnifiedTopicFailMessageTask_Execute_WithServiceException() {
        // Given
        RuntimeException serviceException = new RuntimeException("Service error");
        doThrow(serviceException).when(messageService).retryUnifiedTopicFailedMessages();

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            retryFailTask.execute();
        });
        
        verify(messageService).retryUnifiedTopicFailedMessages();
    }

    /**
     * 测试场景3：统一主题发送中消息重试任务构造函数测试
     * 
     * 验证点：
     * - 能正确创建任务实例
     * - MessageService依赖正确注入
     */
    @Test
    void testRetryUnifiedTopicSendingMessageTask_Constructor() {
        // Given & When
        RetryUnifiedTopicSendingMessageTask task = new RetryUnifiedTopicSendingMessageTask(messageService);

        // Then
        assertNotNull(task);
    }

    /**
     * 测试场景4：统一主题失败消息重试任务构造函数测试
     * 
     * 验证点：
     * - 能正确创建任务实例
     * - MessageService依赖正确注入
     */
    @Test
    void testRetryUnifiedTopicFailMessageTask_Constructor() {
        // Given & When
        RetryUnifiedTopicFailMessageTask task = new RetryUnifiedTopicFailMessageTask(messageService);

        // Then
        assertNotNull(task);
    }

    /**
     * 测试场景5：统一主题发送中消息重试任务 - 空MessageService
     * 
     * 验证点：
     * - 传入null MessageService
     * - 能正确创建但可能在执行时出错
     */
    @Test
    void testRetryUnifiedTopicSendingMessageTask_NullMessageService() {
        // Given & When
        RetryUnifiedTopicSendingMessageTask task = new RetryUnifiedTopicSendingMessageTask(null);

        // Then
        assertNotNull(task);
    }

    /**
     * 测试场景6：统一主题失败消息重试任务 - 空MessageService
     * 
     * 验证点：
     * - 传入null MessageService
     * - 能正确创建但可能在执行时出错
     */
    @Test
    void testRetryUnifiedTopicFailMessageTask_NullMessageService() {
        // Given & When
        RetryUnifiedTopicFailMessageTask task = new RetryUnifiedTopicFailMessageTask(null);

        // Then
        assertNotNull(task);
    }

    /**
     * 测试场景7：统一主题失败消息重试任务 - 多次执行
     * 
     * 验证点：
     * - 任务可以多次执行
     * - 每次执行都会调用MessageService
     */
    @Test
    void testRetryUnifiedTopicFailMessageTask_MultipleExecutions() {
        // Given
        doNothing().when(messageService).retryUnifiedTopicFailedMessages();

        // When
        retryFailTask.execute();
        retryFailTask.execute();
        retryFailTask.execute();

        // Then
        verify(messageService, times(3)).retryUnifiedTopicFailedMessages();
    }

    /**
     * 测试场景8：验证任务类的类型结构
     * 
     * 验证点：
     * - RetryUnifiedTopicFailMessageTask实现了正确的接口
     * - RetryUnifiedTopicSendingMessageTask实现了正确的接口
     */
    @Test
    void testTasksImplementCorrectInterface() {
        // Given & When & Then
        assertTrue(retryFailTask instanceof com.xiaomi.cloud.plan.client.plan.PlanExecutor);
        assertTrue(retrySendingTask instanceof com.xiaomi.cloud.plan.client.plan.PlanExecutor);
    }

    /**
     * 测试场景9：验证任务类的注解配置
     * 
     * 验证点：
     * - 任务类有正确的@PlanTask注解
     * - 注解配置了正确的名称和描述
     */
    @Test
    void testTasksHaveCorrectAnnotations() {
        // Given & When & Then
        assertTrue(retryFailTask.getClass().isAnnotationPresent(com.xiaomi.cloud.plan.client.spring.annotation.PlanTask.class));
        assertTrue(retrySendingTask.getClass().isAnnotationPresent(com.xiaomi.cloud.plan.client.spring.annotation.PlanTask.class));
        
        // 检查注解内容
        com.xiaomi.cloud.plan.client.spring.annotation.PlanTask failTaskAnnotation = 
            retryFailTask.getClass().getAnnotation(com.xiaomi.cloud.plan.client.spring.annotation.PlanTask.class);
        assertEquals("RetryUnifiedTopicFailMessageTask", failTaskAnnotation.name());
        
        com.xiaomi.cloud.plan.client.spring.annotation.PlanTask sendingTaskAnnotation = 
            retrySendingTask.getClass().getAnnotation(com.xiaomi.cloud.plan.client.spring.annotation.PlanTask.class);
        assertEquals("RetryUnifiedTopicSendingMessageTask", sendingTaskAnnotation.name());
    }

    /**
     * 测试场景10：验证任务的定时配置
     * 
     * 验证点：
     * - 任务有正确的cron表达式配置
     * - 描述信息正确
     */
    @Test
    void testTasksHaveCorrectScheduling() {
        // Given & When & Then
        com.xiaomi.cloud.plan.client.spring.annotation.PlanTask failTaskAnnotation = 
            retryFailTask.getClass().getAnnotation(com.xiaomi.cloud.plan.client.spring.annotation.PlanTask.class);
        assertEquals("0 0/5 * * * ?", failTaskAnnotation.quartzCron());
        assertTrue(failTaskAnnotation.description().contains("重试统一Topic发送失败的消息"));
        
        com.xiaomi.cloud.plan.client.spring.annotation.PlanTask sendingTaskAnnotation = 
            retrySendingTask.getClass().getAnnotation(com.xiaomi.cloud.plan.client.spring.annotation.PlanTask.class);
        assertEquals("0 0/5 * * * ?", sendingTaskAnnotation.quartzCron());
        assertTrue(sendingTaskAnnotation.description().contains("重试统一Topic发送中的消息"));
    }
} 