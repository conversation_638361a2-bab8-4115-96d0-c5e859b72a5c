package com.mioffice.ums.engine.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.larksuite.appframework.sdk.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2020/9/10
 */
@Slf4j
public class JsonUtilsTest {

    @Test
    public void test() throws IOException {
//        Map<String, Object> map = new HashMap<>();
//        map.put("a", "a");
//        map.put("b", 12);
//        map.put("c", 1.2);
//        String json = JsonUtils.toJson(map);
//        log.info("{}", json);
//
//        String str = "{\"result\":\"OK\",\"description\":\"成功\",\"code\":0,\"code2\":0.012}";
//        Map<String, Object> parse = JsonUtils.parse(str, new TypeToken<Map<String, Object>>() {
//        }.getType());
//        log.info("{}", parse);

         String msg = "{\n" +
                "  \"msg_type\": \"interactive\",\n" +
                "  \"update_multi\": false,\n" +
                "  \"card\": {\n" +
                "    \"config\": {\n" +
                "      \"wide_screen_mode\": true\n" +
                "    },\n" +
                "    \"header\": {\n" +
                "      \"template\": \"blue\",\n" +
                "      \"title\": {\n" +
                "        \"content\": \"${title!}\",\n" +
                "        \"tag\": \"plain_text\"\n" +
                "      }\n" +
                "    },\n" +
                "    \"elements\": [\n" +
                "      {\n" +
                "        \"tag\": \"markdown\",\n" +
                "        \"content\": \"${content!}\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"tag\": \"action\",\n" +
                "        \"actions\": [\n" +
                "          {\n" +
                "            \"tag\": \"button\",\n" +
                "            \"text\": {\n" +
                "              \"tag\": \"plain_text\",\n" +
                "              \"content\": \"查看详情\"\n" +
                "            },\n" +
                "            \"type\": \"default\",\n" +
                "            \"url\": \"${baseUrl!}/user-feedback/feedback-manage-detail?vocNo=${vocNo!}&activeTabKey=${activeTabKey!}\"\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

        ObjectNode jsonMsg = JsonUtil.larkFormatToObject(msg, new TypeReference<ObjectNode>() {});

        jsonMsg.put("msg_type", "interactive");

        if (jsonMsg.has("card")) {
            if (jsonMsg.has("update_multi")) {
                boolean update_multi = jsonMsg.get("update_multi").asBoolean();
                if (jsonMsg.get("card").has("config")) {
                    ObjectNode config = (ObjectNode) jsonMsg.get("card").get("config");
                    config.remove("wide_screen_mode");
                    config.put("update_multi", update_multi);
                    jsonMsg.remove("update_multi");
                }
            }
            jsonMsg.set("content", jsonMsg.get("card"));
            jsonMsg.remove("card");
        }

        jsonMsg.put("receive_id", "junfudong");

        String finallyStr = JsonUtil.larkFormatToJsonString(jsonMsg);

        log.info(finallyStr);
    }
}
